using RPASystem.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using RPASystem.Model;
using System.Runtime.CompilerServices;
using RPASystem.Service;
using RPASystem.Service;
using Infrastructure.Attribute;

namespace RPASystem.Service
{
    [AppService(ServiceType = typeof(IJobTaskService), ServiceLifetime = LifeTime.Scoped)]
    public partial class JobTaskService : IJobTaskService
    {
        private readonly RPASystemDbContext db;
        private readonly IOrchestrationService orchestrationService;

        public JobTaskService(RPASystemDbContext context, IOrchestrationService orchestrationService)
        {
            this.db = context;
            this.orchestrationService = orchestrationService;
        }

        /// <summary>
        /// 更新任务状态，并同步更新父任务状态
        /// </summary>
        public async Task UpdateJobTaskStatusAsync(long id, JobTaskStatusEnum status, string machineName = null, string outputResults = null, string outputFile = null)
        {
            var jobTask = await db.JobTasks.FindAsync(id);
            if (jobTask == null)
            {
                return;
            }

            jobTask.Status = status;
            if (!string.IsNullOrEmpty(machineName))
            {
                jobTask.AssignedResourceMachine = machineName;
            }
            if (!string.IsNullOrEmpty(outputResults))
            {
                jobTask.OutputResults = outputResults;
            }
            if (!string.IsNullOrEmpty(outputFile))
            {
                jobTask.OutputFile = outputFile;
            }

            // 更新状态相关字段
            await UpdateTaskStatusFields(jobTask, status);
            await db.SaveChangesAsync();
            // 更新父任务状态
            await UpdateParentTaskStatus(id);

        }

        /// <summary>
        /// 更新父任务状态
        /// </summary>
        private async Task UpdateParentTaskStatus(long taskId)
        {
            var task = await db.JobTasks.FindAsync(taskId);
            if (task == null || task.ParentTaskID == 0)
            {
                return;
            }

            var parentTask = await db.JobTasks.FindAsync(task.ParentTaskID);
            if (parentTask == null || parentTask.TaskType != TaskType.SystemOrchestrationSplit)
            {
                return; // 只处理系统编排拆分任务
            }

            var siblingTasks = await db.JobTasks
                .Where(t => t.ParentTaskID == parentTask.ID)
                .ToListAsync();

            var newStatus = CalculateParentStatus(siblingTasks);

            //运行合并文件逻辑，让合并方法决定父任务的状态。
            newStatus = await MergeDirFilesAsync(parentTask, newStatus, siblingTasks);

            if (parentTask.Status != newStatus)
            {
                parentTask.Status = newStatus;
                // 如果父任务状态变为完成状态（成功、失败、取消），更新结束时间
                if (newStatus == JobTaskStatusEnum.Success || newStatus == JobTaskStatusEnum.Failed || newStatus == JobTaskStatusEnum.Cancelled)
                {
                    parentTask.EndTime = DateTime.Now;
                }
                await db.SaveChangesAsync();
            }
        }

        /// <summary>
        /// 根据子任务计算父任务状态
        /// </summary>
        private JobTaskStatusEnum CalculateParentStatus(List<JobTask> childTasks)
        {
            // 如果有任何子任务处于等待或运行中状态，父任务状态为运行中
            if (childTasks.Any(t => t.Status == JobTaskStatusEnum.Pending || t.Status == JobTaskStatusEnum.Running))
            {
                return JobTaskStatusEnum.Running;
            }

            // 所有子任务都已完成（成功、失败或取消）
            if (childTasks.Any(t => t.Status == JobTaskStatusEnum.Failed))
            {
                return JobTaskStatusEnum.Failed;
            }

            if (childTasks.Any(t => t.Status == JobTaskStatusEnum.Cancelled))
            {
                return JobTaskStatusEnum.Cancelled;
            }

            if (childTasks.All(t => t.Status == JobTaskStatusEnum.Success))
            {
                return JobTaskStatusEnum.Success;
            }

            return JobTaskStatusEnum.Running; // 默认状态
        }

        /// <summary>
        /// 更新任务状态和相关字段
        /// Pending状态时，清空开始时间和结束时间
        /// Running状态时，只在首次运行时设置开始时间
        /// 其他状态时，设置结束时间
        /// </summary>
        private async Task UpdateTaskStatusFields(JobTask jobTask, JobTaskStatusEnum status)
        {
            // 更新任务状态
            jobTask.Status = status;

            switch (status)
            {
                case JobTaskStatusEnum.Pending:
                    // Pending状态时，清空开始时间和结束时间
                    jobTask.StartTime = null;
                    jobTask.EndTime = null;
                    jobTask.AssignedResourceMachine = null;
                    jobTask.OutputResults = null;
                    if (jobTask.TaskType == TaskType.SystemOrchestrationSplit)
                    {
                        // 删除所有子任务
                        DeleteChildTasks(jobTask.ID);
                        // 调用 OrchestrationService 的方法重新生成子任务
                        await orchestrationService.ProcessSplitTaskAsync(jobTask);
                    }
                    break;

                case JobTaskStatusEnum.Running:
                    // Running状态时，只在首次运行时设置开始时间
                    if (jobTask.StartTime == null)
                    {
                        jobTask.StartTime = DateTime.Now;
                    }
                    // Running时确保结束时间为空
                    jobTask.EndTime = null;
                    break;

                case JobTaskStatusEnum.Success:
                case JobTaskStatusEnum.Failed:
                case JobTaskStatusEnum.Cancelled:
                    // 其他状态时，设置结束时间
                    jobTask.EndTime = DateTime.Now;
                    break;
            }
        }

        /// <summary>
        /// 删除任务及其所有子任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>删除结果</returns>
        public bool DeleteJobTask(long id)
        {
            var task = db.JobTasks.Find(id);
            if (task == null) return false;

            // 递归删除所有子任务
            DeleteChildTasks(id);

            // 删除当前任务
            db.JobTasks.Remove(task);
            return db.SaveChanges() > 0;
        }

        /// <summary>
        /// 递归删除子任务
        /// </summary>
        /// <param name="parentId">父任务ID</param>
        private void DeleteChildTasks(long parentId)
        {
            // 获取所有子任务
            var childTasks = db.JobTasks.Where(t => t.ParentTaskID == parentId).ToList();

            foreach (var childTask in childTasks)
            {
                // 递归删除子任务的子任务
                DeleteChildTasks(childTask.ID);

                // 删除子任务
                db.JobTasks.Remove(childTask);
            }
        }

        public List<ViewJobTaskExeProgram> GetAllJobTasksWithExePrograms()
        {
            var result = from jobTask in db.JobTasks
                         join exeProgram in db.ExePrograms on jobTask.ExeProgramID equals exeProgram.ID
                         orderby jobTask.CreatedAt descending
                         select new ViewJobTaskExeProgram
                         {
                             JobTaskId = jobTask.ID,
                             ExeProgramName = exeProgram.ProgramName,
                             JobTaskName = jobTask.TaskName,
                             Priority = jobTask.Priority,
                             CreatedAt = jobTask.CreatedAt,
                             StartTime = jobTask.StartTime,
                             EndTime = jobTask.EndTime,
                             AssignedResourceMachine = jobTask.AssignedResourceMachine,
                             InputParameters = jobTask.InputParameters,
                             OutputResults = jobTask.OutputResults,
                             Status = jobTask.Status.ToString(),
                             Notes = jobTask.Notes,
                             ParentTaskID = jobTask.ParentTaskID,
                             TaskType = jobTask.TaskType,
                             OutputFile = jobTask.OutputFile,
                             RetryCount = jobTask.RetryCount
                         };

            return result.ToList();
        }

        public async Task<long> CreateJobTaskAsync(JobTask jobTask)
        {
            db.JobTasks.Add(jobTask);
            await db.SaveChangesAsync();
            return jobTask.ID;
        }

        public async Task<JobTask> GetJobTaskByIdAsync(long id)
        {
            return await db.JobTasks.FindAsync(id);
        }

        public async Task<JobTask> GetJobTaskByNameAsync(string taskName)
        {
            if (string.IsNullOrWhiteSpace(taskName))
            {
                throw new ArgumentException("任务名称不能为空", nameof(taskName));
            }
            return await db.JobTasks.FirstOrDefaultAsync(t => t.TaskName == taskName);
        }

        public async Task<List<ViewJobTaskExeProgram>> GetPendingTasksWithProgramAsync()
        {
            return await db.JobTasks
                .Where(t => t.Status == JobTaskStatusEnum.Pending && t.TaskType == TaskType.Normal)
                .OrderByDescending(t => t.Priority)
                .ThenBy(t => t.CreatedAt)
                .Join(
                    db.ExePrograms,
                    task => task.ExeProgramID,
                    program => program.ID,
                    (task, program) => new ViewJobTaskExeProgram
                    {
                        JobTaskId = task.ID,
                        ParentTaskID = task.ParentTaskID,
                        JobTaskName = task.TaskName,
                        Priority = task.Priority,
                        InputParameters = task.InputParameters,
                        Status = task.Status.ToString(),
                        ExeProgramName = program.ProgramName,
                        Version = program.Version,
                        ProgramType = program.ProgramType,
                        ExeProgramId = program.ID,
                        ResourceSelection = task.ResourceSelection,
                        IsExclusive = program.IsExclusive,
                        OutputFile = task.OutputFile,
                        RetryCount = task.RetryCount
                    })
                .ToListAsync();
        }

        public async Task<string> GetTopTaskNameAsync(long taskId)
        {
            // 使用递归CTE查询获取所有父任务
            var sql = FormattableStringFactory.Create(@"
                WITH RECURSIVE TaskHierarchy AS (
                    SELECT ID, ParentTaskID, TaskName, 1 as Level
                    FROM JobTasks 
                    WHERE ID = {0}
                    
                    UNION ALL
                    
                    SELECT p.ID, p.ParentTaskID, p.TaskName, th.Level + 1
                    FROM JobTasks p
                    INNER JOIN TaskHierarchy th ON p.ID = th.ParentTaskID
                )
                SELECT TaskName as Value
                FROM TaskHierarchy
                WHERE ParentTaskID = 0 OR ParentTaskID NOT IN (SELECT ID FROM TaskHierarchy)
                ORDER BY Level DESC
                LIMIT 1", taskId);

            return await db.Database.SqlQuery<string>(sql).FirstOrDefaultAsync() ?? string.Empty;
        }

        /// <summary>
        /// 获取顶层任务的程序名称
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>顶层任务的程序名称</returns>
        public async Task<string> GetTopTaskProgramNameAsync(long taskId)
        {
            // 使用递归CTE查询获取顶层任务及其程序名称
            var sql = FormattableStringFactory.Create(@"
                WITH RECURSIVE TaskHierarchy AS (
                    SELECT t.ID, t.ParentTaskID, t.ExeProgramID, 1 as Level
                    FROM JobTasks t
                    WHERE t.ID = {0}
                    
                    UNION ALL
                    
                    SELECT p.ID, p.ParentTaskID, p.ExeProgramID, th.Level + 1
                    FROM JobTasks p
                    INNER JOIN TaskHierarchy th ON p.ID = th.ParentTaskID
                )
                SELECT e.ProgramName as Value
                FROM TaskHierarchy th
                JOIN ExePrograms e ON th.ExeProgramID = e.ID
                WHERE th.ParentTaskID = 0 OR th.ParentTaskID NOT IN (SELECT ID FROM TaskHierarchy)
                ORDER BY th.Level DESC
                LIMIT 1", taskId);

            return await db.Database.SqlQuery<string>(sql).FirstOrDefaultAsync() ?? string.Empty;
        }

        public async Task<(List<ViewJobTaskExeProgram> Items, int TotalCount)> GetJobTasksPagedAsync(JobTaskQueryParams queryParams)
        {
            // 确定是否有查询条件
            bool hasFilter = !string.IsNullOrWhiteSpace(queryParams.TaskName) || 
                            !string.IsNullOrWhiteSpace(queryParams.ProgramName) ||
                            !string.IsNullOrWhiteSpace(queryParams.ResourceMachine) ||
                            queryParams.TaskType.HasValue ||
                            !string.IsNullOrWhiteSpace(queryParams.Status);
            
            // 构建基础查询
            var query = from jobTask in db.JobTasks
                        join exeProgram in db.ExePrograms on jobTask.ExeProgramID equals exeProgram.ID
                        select new { jobTask, exeProgram };
            
            // 1. 如果没有查询条件，只查顶级父任务
            if (!hasFilter)
            {
                query = query.Where(x => x.jobTask.ParentTaskID == 0);
            }
            // 2. 如果有查询条件，应用筛选
            else
            {
                if (!string.IsNullOrWhiteSpace(queryParams.TaskName))
                {
                    query = query.Where(x => x.jobTask.TaskName.Contains(queryParams.TaskName));
                }
                
                if (!string.IsNullOrWhiteSpace(queryParams.ProgramName))
                {
                    query = query.Where(x => x.exeProgram.ProgramName.Contains(queryParams.ProgramName));
                }
                
                if (!string.IsNullOrWhiteSpace(queryParams.ResourceMachine))
                {
                    query = query.Where(x => x.jobTask.AssignedResourceMachine.Contains(queryParams.ResourceMachine));
                }
                
                if (queryParams.TaskType.HasValue)
                {
                    query = query.Where(x => x.jobTask.TaskType == queryParams.TaskType.Value);
                }
                
                if (!string.IsNullOrWhiteSpace(queryParams.Status))
                {
                    var statusEnum = Enum.Parse<JobTaskStatusEnum>(queryParams.Status);
                    query = query.Where(x => x.jobTask.Status == statusEnum);
                }
            }
            
            // 获取总记录数
            var totalCount = await query.CountAsync();

            // 3. 所有查询默认按ID倒序排序，以提升性能
            var pagedTasks = await query
                .OrderByDescending(x => x.jobTask.ID)
                .Skip((queryParams.PageNumber - 1) * queryParams.PageSize)
                .Take(queryParams.PageSize)
                .Select(x => new ViewJobTaskExeProgram
                {
                    JobTaskId = x.jobTask.ID,
                    ExeProgramName = x.exeProgram.ProgramName,
                    JobTaskName = x.jobTask.TaskName,
                    Priority = x.jobTask.Priority,
                    CreatedAt = x.jobTask.CreatedAt,
                    StartTime = x.jobTask.StartTime,
                    EndTime = x.jobTask.EndTime,
                    AssignedResourceMachine = x.jobTask.AssignedResourceMachine,
                    InputParameters = x.jobTask.InputParameters,
                    OutputResults = x.jobTask.OutputResults,
                    Status = x.jobTask.Status.ToString(),
                    Notes = x.jobTask.Notes,
                    ParentTaskID = x.jobTask.ParentTaskID,
                    TaskType = x.jobTask.TaskType,
                    ResourceSelection = x.jobTask.ResourceSelection ?? "",
                    Version = x.exeProgram.Version,
                    ProgramType = x.exeProgram.ProgramType,
                    ExeProgramId = x.exeProgram.ID,
                    OutputFile = x.jobTask.OutputFile,
                    RetryCount = x.jobTask.RetryCount,
                })
                .ToListAsync();

            // 收集所有需要获取子任务的顶级任务ID
            var topLevelTaskIds = new HashSet<long>();
            
            // 从结果中找出所有顶级任务
            foreach (var task in pagedTasks)
            {
                if (task.ParentTaskID == 0)
                {
                    topLevelTaskIds.Add(task.JobTaskId);
                }
            }
            
            // 如果有查询条件，还需要查找所有子任务的父任务链
            if (hasFilter)
            {
                foreach (var task in pagedTasks)
                {
                    if (task.ParentTaskID != 0)
                    {
                        // 查找父任务链，直到顶级父任务
                        var currentParentId = task.ParentTaskID;
                        while (currentParentId != 0)
                        {
                            var parentTask = await db.JobTasks.FindAsync(currentParentId);
                            if (parentTask == null) break;
                            
                            if (parentTask.ParentTaskID == 0)
                            {
                                // 找到顶级父任务，添加到列表
                                topLevelTaskIds.Add(parentTask.ID);
                                break;
                            }
                            
                            currentParentId = parentTask.ParentTaskID;
                        }
                    }
                }
            }
            
            // 获取所有子任务
            var allChildTasks = new List<ViewJobTaskExeProgram>();
            foreach (var topLevelId in topLevelTaskIds)
            {
                var childTasks = await GetAllChildTasksViewAsync(topLevelId);
                allChildTasks.AddRange(childTasks);
            }
            
            // 构建最终结果集
            var result = new List<ViewJobTaskExeProgram>(pagedTasks);
            
            // 添加子任务，但避免重复
            foreach (var child in allChildTasks)
            {
                if (!result.Any(t => t.JobTaskId == child.JobTaskId))
                {
                    result.Add(child);
                }
            }
            
            // 为系统拆分任务添加子任务统计信息
            foreach (var task in result.Where(t => t.TaskType == TaskType.SystemOrchestrationSplit))
            {
                var childStats = await db.JobTasks
                    .Where(t => t.ParentTaskID == task.JobTaskId)
                    .ToListAsync();

                task.ChildTaskStats = new ChildTaskStats
                {
                    Total = childStats.Count,
                    Success = childStats.Count(t => t.Status == JobTaskStatusEnum.Success),
                    Running = childStats.Count(t => t.Status == JobTaskStatusEnum.Running),
                    Failed = childStats.Count(t => t.Status == JobTaskStatusEnum.Failed),
                    Pending = childStats.Count(t => t.Status == JobTaskStatusEnum.Pending),
                    Cancelled = childStats.Count(t => t.Status == JobTaskStatusEnum.Cancelled)
                };
            }

            return (result, totalCount);
        }

        /// <summary>
        /// 递归添加子任务
        /// </summary>
        private void AddChildTasksRecursively(long parentId, List<ViewJobTaskExeProgram> allChildTasks, List<ViewJobTaskExeProgram> result)
        {
            var directChildren = allChildTasks.Where(t => t.ParentTaskID == parentId).ToList();
            foreach (var child in directChildren)
            {
                result.Add(child);
                AddChildTasksRecursively(child.JobTaskId, allChildTasks, result);
            }
        }

        /// <summary>
        /// 设置任务状态为已完成
        /// </summary>
        public async Task<bool> SetStatusDone(long taskId)
        {
            var jobTask = await db.JobTasks.FindAsync(taskId);
            if (jobTask == null)
            {
                return false;
            }

            jobTask.Status = JobTaskStatusEnum.Success;
            jobTask.EndTime = DateTime.Now;
            return await db.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 重试任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>重试操作结果</returns>
        public async Task<bool> RetryJobTaskAsync(long id)
        {
            var jobTask = await db.JobTasks.FindAsync(id);
            if (jobTask == null)
            {
                return false;
            }

            // 增加重试计数
            jobTask.RetryCount += 1;
            
            // 重置任务状态
            jobTask.Status = JobTaskStatusEnum.Pending;
            jobTask.StartTime = null;
            jobTask.EndTime = null;
            jobTask.OutputResults = null;
            jobTask.AssignedResourceMachine = null;
            
            // 如果是编排任务，需要特殊处理
            if (jobTask.TaskType == TaskType.SystemOrchestrationSplit)
            {
                // 调用编排服务进行处理
                await UpdateTaskStatusFields(jobTask, JobTaskStatusEnum.Pending);
            }
            
            return await db.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 获取符合自动重试条件的任务列表
        /// </summary>
        /// <returns>可重试任务列表</returns>
        public async Task<List<ViewJobTaskExeProgram>> GetTasksForAutoRetryAsync()
        {
            // 查询状态为"失败"且重试次数<5的任务
            var tasks = await (from jobTask in db.JobTasks
                      join exeProgram in db.ExePrograms on jobTask.ExeProgramID equals exeProgram.ID
                      where jobTask.Status == JobTaskStatusEnum.Failed && jobTask.RetryCount < 3 && jobTask.TaskType == TaskType.Normal
                      orderby jobTask.Priority descending // 按优先级降序排序
                      select new ViewJobTaskExeProgram
                      {
                          JobTaskId = jobTask.ID,
                          ExeProgramName = exeProgram.ProgramName,
                          JobTaskName = jobTask.TaskName,
                          Priority = jobTask.Priority,
                          Status = jobTask.Status.ToString(),
                          RetryCount = jobTask.RetryCount,
                          ResourceSelection = jobTask.ResourceSelection,
                          ParentTaskID = jobTask.ParentTaskID,
                          TaskType = jobTask.TaskType
                      }).ToListAsync();

            return tasks;
        }
        
        /// <summary>
        /// 获取所有任务的子任务
        /// </summary>
        public async Task<List<JobTask>> GetAllChildTasksAsync(long parentId)
        {
            var sql = FormattableStringFactory.Create(@"
                WITH RECURSIVE TaskHierarchy AS (
                    SELECT *
                    FROM JobTasks 
                    WHERE ParentTaskID = {0}
                    
                    UNION ALL
                    
                    SELECT t.*
                    FROM JobTasks t
                    INNER JOIN TaskHierarchy th ON t.ParentTaskID = th.ID
                )
                SELECT * FROM TaskHierarchy", parentId);

            return await db.JobTasks
                .FromSql(sql)
                .ToListAsync();
        }

        /// <summary>
        /// 获取需要停止的任务列表（运行中或待运行状态）
        /// </summary>
        public async Task<List<JobTask>> GetTasksToStopAsync(long taskId)
        {
            var mainTask = await GetJobTaskByIdAsync(taskId);
            if (mainTask == null) return new List<JobTask>();

            var result = new List<JobTask>();

            // 只有运行中或待运行的任务需要停止
            if (mainTask.Status == JobTaskStatusEnum.Running ||
                mainTask.Status == JobTaskStatusEnum.Pending)
            {
                result.Add(mainTask);
            }

            // 获取所有子任务
            var childTasks = await GetAllChildTasksAsync(taskId);
            result.AddRange(childTasks.Where(t =>
                t.Status == JobTaskStatusEnum.Running ||
                t.Status == JobTaskStatusEnum.Pending));

            return result;
        }

        /// <summary>
        /// 获取可用资源机
        /// </summary>
        public async Task<List<string>> GetAvailableResourceMachinesAsync(string resourceSelection)
        {
            if (string.IsNullOrEmpty(resourceSelection))
            {
                return new List<string>();
            }

            var result = new HashSet<string>();
            var selections = resourceSelection.Split('|', StringSplitOptions.RemoveEmptyEntries);

            foreach (var selection in selections)
            {
                // 直接查资源池
                var pool = await db.ResourcePools.FirstOrDefaultAsync(p => p.PoolName == selection);
                if (pool != null)
                {
                    foreach (var machine in pool.MachineList)
                    {
                        result.Add(machine);
                    }
                }
                else
                {
                    // 直接添加资源机名称
                    result.Add(selection);
                }
            }

            // 验证资源机是否存在
            var existingMachines = await db.ResourceMachines
                .Where(m => result.Contains(m.MachineName))
                .Select(m => m.MachineName)
                .ToListAsync();

            return existingMachines;
        }

        /// <summary>
        /// 更新任务及其子任务
        /// </summary>
        public async Task<bool> UpdateJobTaskWithChildrenAsync(long taskId, JobTaskUpdateDto updateDto)
        {
            var mainTask = await db.JobTasks.FindAsync(taskId);
            if (mainTask == null)
            {
                throw new Exception($"未找到ID为 {taskId} 的任务");
            }

            // 更新主任务
            if (updateDto.Priority.HasValue)
            {
                mainTask.Priority = updateDto.Priority.Value;
            }
            if (!string.IsNullOrEmpty(updateDto.ResourceSelection))
            {
                mainTask.ResourceSelection = updateDto.ResourceSelection;
            }
            if (updateDto.Status.HasValue)
            {
                await UpdateJobTaskStatusAsync(taskId, updateDto.Status.Value);
            }
            if (!string.IsNullOrEmpty(updateDto.Notes))
            {
                mainTask.Notes = updateDto.Notes;
            }

            // 获取所有子任务
            var childTasks = await GetAllChildTasksAsync(taskId);

            foreach (var childTask in childTasks)
            {
                // 更新优先级（所有子任务）
                if (updateDto.Priority.HasValue)
                {
                    childTask.Priority = updateDto.Priority.Value;
                }

                // 更新资源选择（仅普通任务）
                if (!string.IsNullOrEmpty(updateDto.ResourceSelection) && childTask.TaskType == TaskType.Normal)
                {
                    childTask.ResourceSelection = updateDto.ResourceSelection;
                }

                // 更新状态（所有子任务）
                if (updateDto.Status.HasValue)
                {
                    await UpdateJobTaskStatusAsync(childTask.ID, updateDto.Status.Value);
                }
            }

            await db.SaveChangesAsync();
            return true;
        }

        public async Task<(List<ViewJobTaskExeProgram> Items, int TotalCount)> GetSubTasksPagedAsync(SubTaskQueryParams queryParams)
        {
            // 构建基础查询
            var query = from jobTask in db.JobTasks
                        join exeProgram in db.ExePrograms on jobTask.ExeProgramID equals exeProgram.ID
                        where jobTask.ParentTaskID == queryParams.ParentTaskId
                        select new { jobTask, exeProgram };

            // 应用筛选条件
            if (!string.IsNullOrWhiteSpace(queryParams.ResourceMachine))
            {
                query = query.Where(x => x.jobTask.AssignedResourceMachine.Contains(queryParams.ResourceMachine));
            }
            if (queryParams.Status != null && queryParams.Status.Any())
            {
                var statusEnums = queryParams.Status.Select(s => Enum.Parse<JobTaskStatusEnum>(s)).ToList();
                query = query.Where(x => statusEnums.Contains(x.jobTask.Status));
            }

            // 获取总记录数
            var totalCount = await query.CountAsync();

            // 分页并获取数据
            var pagedTasks = await query
                .OrderByDescending(x => x.jobTask.CreatedAt)
                .Skip((queryParams.PageNumber - 1) * queryParams.PageSize)
                .Take(queryParams.PageSize)
                .Select(x => new ViewJobTaskExeProgram
                {
                    JobTaskId = x.jobTask.ID,
                    ExeProgramName = x.exeProgram.ProgramName,
                    JobTaskName = x.jobTask.TaskName,
                    Priority = x.jobTask.Priority,
                    CreatedAt = x.jobTask.CreatedAt,
                    StartTime = x.jobTask.StartTime,
                    EndTime = x.jobTask.EndTime,
                    AssignedResourceMachine = x.jobTask.AssignedResourceMachine,
                    InputParameters = x.jobTask.InputParameters,
                    OutputResults = x.jobTask.OutputResults,
                    Status = x.jobTask.Status.ToString(),
                    Notes = x.jobTask.Notes,
                    ParentTaskID = x.jobTask.ParentTaskID,
                    TaskType = x.jobTask.TaskType,
                    ResourceSelection = x.jobTask.ResourceSelection,
                    OutputFile = x.jobTask.OutputFile,
                    RetryCount = x.jobTask.RetryCount
                })
                .ToListAsync();

            return (pagedTasks, totalCount);
        }

        private async Task<List<ViewJobTaskExeProgram>> GetAllChildTasksViewAsync(long parentId)
        {
            // 使用递归CTE查询获取所有子任务ID
            var childTaskSql = FormattableStringFactory.Create(@"
                WITH RECURSIVE TaskHierarchy AS (
                    SELECT ID
                    FROM JobTasks
                    WHERE ParentTaskID = {0}
                    
                    UNION ALL
                    
                    SELECT c.ID
                    FROM JobTasks c
                    JOIN TaskHierarchy th ON c.ParentTaskID = th.ID
                )
                SELECT ID FROM TaskHierarchy", parentId);

            // 获取所有子任务ID
            var childTaskIds = await db.Database
                .SqlQuery<long>(childTaskSql)
                .ToListAsync();

            if (childTaskIds.Count == 0)
            {
                return new List<ViewJobTaskExeProgram>();
            }

            // 使用获取到的ID列表查询完整信息
            return await (from jobTask in db.JobTasks
                         join exeProgram in db.ExePrograms on jobTask.ExeProgramID equals exeProgram.ID
                         where childTaskIds.Contains(jobTask.ID)
                         select new ViewJobTaskExeProgram
                         {
                             JobTaskId = jobTask.ID,
                             ExeProgramName = exeProgram.ProgramName,
                             JobTaskName = jobTask.TaskName,
                             Priority = jobTask.Priority,
                             CreatedAt = jobTask.CreatedAt,
                             StartTime = jobTask.StartTime,
                             EndTime = jobTask.EndTime,
                             AssignedResourceMachine = jobTask.AssignedResourceMachine,
                             InputParameters = jobTask.InputParameters,
                             OutputResults = jobTask.OutputResults,
                             Status = jobTask.Status.ToString(),
                             Notes = jobTask.Notes,
                             ParentTaskID = jobTask.ParentTaskID,
                             TaskType = jobTask.TaskType,
                             ResourceSelection = jobTask.ResourceSelection,
                             OutputFile = jobTask.OutputFile,
                             RetryCount = jobTask.RetryCount,
                             Version = exeProgram.Version,
                             ProgramType = exeProgram.ProgramType,
                             ExeProgramId = exeProgram.ID
                         }).ToListAsync();
        }
    }

}
