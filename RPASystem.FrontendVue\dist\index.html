<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <link rel="icon" href="/favicon.ico">
  <title>RPASystem管理系统</title>
  <!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]-->
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }

    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    .first-loading-wrp {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 90vh;
      min-height: 90vh;
    }

    .first-loading-wrp>h1 {
      font-size: 30px;
      font-weight: bolder;
    }

    .first-loading-wrp .loading-wrp {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 48px;
    }

    .dot {
      position: relative;
      box-sizing: border-box;
      display: inline-block;
      width: 45px;
      height: 45px;
      font-size: 64px;
      transform: rotate(45deg);
      animation: antRotate 1.2s infinite linear;
    }

    .dot i {
      position: absolute;
      display: block;
      width: 16px;
      height: 16px;
      background-color: #1890ff;
      border-radius: 100%;
      opacity: 0.3;
      transform: scale(0.75);
      transform-origin: 50% 50%;
      animation: antSpinMove 1s infinite linear alternate;
    }

    .dot i:nth-child(1) {
      top: 0;
      left: 0;
    }

    .dot i:nth-child(2) {
      top: 0;
      right: 0;
      -webkit-animation-delay: 0.4s;
      animation-delay: 0.4s;
    }

    .dot i:nth-child(3) {
      right: 0;
      bottom: 0;
      -webkit-animation-delay: 0.8s;
      animation-delay: 0.8s;
    }

    .dot i:nth-child(4) {
      bottom: 0;
      left: 0;
      -webkit-animation-delay: 1.2s;
      animation-delay: 1.2s;
    }

    @keyframes antRotate {
      to {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg);
      }
    }

    @-webkit-keyframes antRotate {
      to {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg);
      }
    }

    @keyframes antSpinMove {
      to {
        opacity: 1;
      }
    }

    @-webkit-keyframes antSpinMove {
      to {
        opacity: 1;
      }
    }
  </style>
  <script type="module" crossorigin src="/static/js/index-D9f5ARRd.js"></script>
  <link rel="stylesheet" crossorigin href="/static/css/index-D3V9cxXI.css">
</head>

<body>
  <div id="app">
    <div class="first-loading-wrp">
      <div class="loading-wrp">
        <span class="dot dot-spin"> <i></i> <i></i> <i></i> <i></i> </span>
      </div>
      <h5>正在加载系统资源...</h5>
    </div>
  </div>
</body>

</html>