import{e as d}from"./401-mUbbotS2.js";import{_ as m,r as f,c as k,i as o,l as s,x as n,o as x,D as a,j as e,k as b,v as g}from"./index-D9f5ARRd.js";const h={class:"errPage-container"},w={class:"list-unstyled"},v={class:"link-type"},y=["src"],B={__name:"401",setup(C){let{proxy:r}=g();const u=f(d+"?"+ +new Date);function _(){r.$route.query.noGoBack?r.$router.push({path:"/"}):r.$router.go(-1)}return(G,t)=>{const c=n("el-button"),i=n("router-link"),l=n("el-col"),p=n("el-row");return x(),k("div",h,[o(c,{icon:"arrow-left",class:"pan-back-btn",onClick:_},{default:s(()=>t[0]||(t[0]=[a(" 返回 ")])),_:1}),o(p,null,{default:s(()=>[o(l,{span:12},{default:s(()=>[t[2]||(t[2]=e("h1",{class:"text-jumbo text-ginormous"}," 401错误! ",-1)),t[3]||(t[3]=e("h2",null,"您没有访问权限！",-1)),t[4]||(t[4]=e("h6",null,"对不起，您没有访问权限，请不要进行非法操作！您可以返回主页面",-1)),e("ul",w,[e("li",v,[o(i,{to:"/"},{default:s(()=>t[1]||(t[1]=[a(" 回首页 ")])),_:1})])])]),_:1}),o(l,{span:12},{default:s(()=>[e("img",{src:b(u),width:"313",height:"428",alt:"Girl has dropped her ice cream."},null,8,y)]),_:1})]),_:1})])}}},V=m(B,[["__scopeId","data-v-51b534d6"]]);export{V as default};
