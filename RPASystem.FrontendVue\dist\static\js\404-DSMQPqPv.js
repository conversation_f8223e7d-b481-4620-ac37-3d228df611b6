import{_ as o,e as l,c,j as t,I as _,t as n,k as d,i as r,l as p,x as m,o as u,D as v}from"./index-D9f5ARRd.js";const f="/static/png/404-N4aRkdWY.png",a="/static/png/404_cloud-CPexjtDj.png",g={class:"wscn-http404-container"},h={class:"wscn-http404"},x={class:"bullshit"},k={class:"bullshit__headline"},b={__name:"404",setup(N){let i=l(()=>"找不到网页！");return(V,s)=>{const e=m("router-link");return u(),c("div",g,[t("div",h,[s[3]||(s[3]=_('<div class="pic-404" data-v-aa824f31><img class="pic-404__parent" src="'+f+'" alt="404" data-v-aa824f31><img class="pic-404__child left" src="'+a+'" alt="404" data-v-aa824f31><img class="pic-404__child mid" src="'+a+'" alt="404" data-v-aa824f31><img class="pic-404__child right" src="'+a+'" alt="404" data-v-aa824f31></div>',1)),t("div",x,[s[1]||(s[1]=t("div",{class:"bullshit__oops"}," 404错误! ",-1)),t("div",k,n(d(i)),1),s[2]||(s[2]=t("div",{class:"bullshit__info"}," 对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。 ",-1)),r(e,{to:"/index",class:"bullshit__return-home"},{default:p(()=>s[0]||(s[0]=[v(" 返回首页 ")])),_:1})])])])}}},w=o(b,[["__scopeId","data-v-aa824f31"]]);export{w as default};
