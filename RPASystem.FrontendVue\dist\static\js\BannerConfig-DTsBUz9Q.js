import{ai as U,r as m,E as X,a5 as we,x as d,N as Y,o as s,c as S,O as T,S as O,k as t,i as n,l as o,D as g,F as I,K as j,p as _,t as f,A as ke,m as Z,j as P,a8 as ee,a6 as Ce,q as Se,s as Ue,v as $e}from"./index-D9f5ARRd.js";function Be(b){return U({url:"public/BannerConfig/list",method:"get",params:b})}function Ie(b){return U({url:"public/BannerConfig",method:"post",data:b})}function je(b){return U({url:"public/BannerConfig",method:"PUT",data:b})}function xe(b){return U({url:"public/BannerConfig/"+b,method:"get"})}function qe(b){return U({url:"public/BannerConfig/delete/"+b,method:"delete"})}function Ne(b){return U({url:"public/BannerConfig/ChangeSort",method:"get",params:b})}const Re=["onClick"],Le=Ue({name:"bannerconfig"}),Fe=Object.assign(Le,{setup(b){const{proxy:p}=$e(),z=m([]),L=m(!1),x=m(!0),i=X({pageNum:1,pageSize:10,sort:"id",sortType:"desc",title:void 0,jumpType:void 0,showStatus:void 0,adType:void 0}),E=m([{visible:!0,prop:"id",label:"id"},{visible:!0,prop:"title",label:"标题"},{visible:!0,prop:"content",label:"说明"},{visible:!0,prop:"link",label:"链接",showOverflowTooltip:!0},{visible:!0,prop:"imgUrl",label:"图片",type:"img"},{visible:!0,prop:"jumpType",label:"跳转类型",type:"dict",dictType:"jumpTypeOptions"},{visible:!1,prop:"addTime",label:"添加时间"},{visible:!0,prop:"clicksNumber",label:"点击次数"},{visible:!0,prop:"showStatus",label:"是否显示",type:"dict",dictType:"sys_show_hide"},{visible:!0,prop:"adType",label:"广告类型",type:"dict",dictType:"sys_ad_type"},{visible:!0,prop:"beginTime",label:"显示时间",type:"slot",width:"130",align:"left"},{visible:!0,prop:"sortId",label:"排序id",type:"slot"},{visible:!0,prop:"actions",label:"操作",type:"slot",width:"120"}]),Q=m(0),A=m([]),le=m();m([new Date(2e3,1,1,0,0,0),new Date(2e3,2,1,23,59,59)]);const q=m(-1),M=m([]),te=a=>{a&&M.value.push(a)};function ne(a){q.value=a,setTimeout(()=>{M.value[a].focus()},100)}function oe(a){q.value=-1,p.$confirm("是否保存数据?").then(function(){return Ne({value:a.sortId,id:a.id})}).then(()=>{k(),p.$modal.msgSuccess("修改成功")})}var ae=["sys_show_hide","sys_ad_type"];p.getDicts(ae).then(a=>{a.data.forEach(l=>{K.options[l.dictType]=l.list})});function w(){L.value=!0,Be(i).then(a=>{const{code:l,data:c}=a;l==200&&(A.value=c.result,Q.value=c.totalNum,L.value=!1)})}function k(){i.pageNum=1,w()}function ue(){p.resetForm("queryRef"),k()}function ie(a){var l=void 0,c=void 0;a.prop!=null&&a.order!=null&&(l=a.prop,c=a.order),i.sort=l,i.sortType=c,k()}const re=m(),D=m(""),N=m(0),v=m(!1),K=X({single:!0,multiple:!0,form:{},rules:{title:[{required:!0,message:"Title不能为空",trigger:"blur"}],jumpType:[{required:!0,message:"跳转类型不能为空",trigger:"change",type:"number"}],showStatus:[{required:!0,message:"是否显示不能为空",trigger:"blur",type:"number"}],adType:[{required:!0,message:"广告类型不能为空",trigger:"change",type:"number"}],beginTime:[{required:!0,message:"广告显示时间不能为空",trigger:"change"}],endTime:[{required:!0,message:"广告显示时间不能为空",trigger:"change"}],link:[{required:!0,message:"跳转链接不能为空",trigger:"blur"}]},options:{jumpTypeOptions:[{dictLabel:"不跳转",dictValue:"0"},{dictLabel:"外链",dictValue:"1"},{dictLabel:"内部跳转",dictValue:"2"}],sys_show_hide:[],sys_ad_type:[]}}),{form:u,rules:de,options:C}=we(K);function se(){v.value=!1,F()}function F(){u.value={id:null,title:null,content:null,link:null,imgUrl:null,jumpType:null,addTime:null,clicksNumber:null,showStatus:1,adType:null,beginTime:null,endTime:null,sortId:0},p.resetForm("formRef")}function pe(){F(),v.value=!0,D.value="添加广告管理",N.value=1}function me(a){F();const l=a.id||z.value;xe(l).then(c=>{const{code:r,data:h}=c;r==200&&(v.value=!0,D.value="修改广告管理",N.value=2,u.value={...h})})}function fe(){p.$refs.formRef.validate(a=>{a&&(u.value.id!=null&&N.value===2?je(u.value).then(l=>{p.$modal.msgSuccess("修改成功"),v.value=!1,w()}):Ie(u.value).then(l=>{p.$modal.msgSuccess("新增成功"),v.value=!1,w()}))})}function ce(a){const l=a.id||z.value;p.$confirm('是否确认删除参数编号为"'+l+'"的数据项？',"警告",{confirmButtonText:p.$t("common.ok"),cancelButtonText:p.$t("common.cancel"),type:"warning"}).then(function(){return qe(l)}).then(()=>{w(),p.$modal.msgSuccess("删除成功")})}function ge(){p.$confirm("是否确认导出广告管理数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await p.downFile("/public/BannerConfig/export",{...i})})}return k(),(a,l)=>{const c=d("el-input"),r=d("el-form-item"),h=d("el-radio-button"),$=d("el-radio-group"),V=d("el-button"),G=d("el-form"),y=d("el-col"),be=d("right-toolbar"),H=d("el-row"),ye=d("myTable"),_e=d("pagination"),J=d("el-radio"),ve=d("UploadImage"),W=d("el-date-picker"),Ve=d("el-input-number"),Te=d("el-dialog"),R=Y("hasPermi"),he=Y("loading");return s(),S("div",null,[T(n(G,{model:t(i),"label-position":"right",inline:"",ref_key:"queryRef",ref:le,onSubmit:l[4]||(l[4]=ke(()=>{},["prevent"]))},{default:o(()=>[n(r,{label:"标题",prop:"title"},{default:o(()=>[n(c,{modelValue:t(i).title,"onUpdate:modelValue":l[0]||(l[0]=e=>t(i).title=e),placeholder:"请输入标题"},null,8,["modelValue"])]),_:1}),n(r,{label:"是否显示",prop:"showStatus"},{default:o(()=>[n($,{modelValue:t(i).showStatus,"onUpdate:modelValue":l[1]||(l[1]=e=>t(i).showStatus=e)},{default:o(()=>[n(h,null,{default:o(()=>l[19]||(l[19]=[g("全部")])),_:1}),(s(!0),S(I,null,j(t(C).sys_show_hide,e=>(s(),_(h,{key:e.dictValue,value:e.dictValue},{default:o(()=>[g(f(e.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(r,{label:"广告类型",prop:"adType"},{default:o(()=>[n($,{modelValue:t(i).adType,"onUpdate:modelValue":l[2]||(l[2]=e=>t(i).adType=e),onChange:l[3]||(l[3]=e=>k())},{default:o(()=>[n(h,null,{default:o(()=>l[20]||(l[20]=[g("全部")])),_:1}),(s(!0),S(I,null,j(t(C).sys_ad_type,e=>(s(),_(h,{key:e.dictValue,value:e.dictValue},{default:o(()=>[g(f(e.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(r,null,{default:o(()=>[n(V,{icon:"search",type:"primary",onClick:k},{default:o(()=>[g(f(a.$t("btn.search")),1)]),_:1}),n(V,{icon:"refresh",onClick:ue},{default:o(()=>[g(f(a.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[O,t(x)]]),n(H,{gutter:15,class:"mb10"},{default:o(()=>[n(y,{span:1.5},{default:o(()=>[T((s(),_(V,{type:"primary",plain:"",icon:"plus",onClick:pe},{default:o(()=>[g(f(a.$t("btn.add")),1)]),_:1})),[[R,["bannerconfig:add"]]])]),_:1}),n(y,{span:1.5},{default:o(()=>[T((s(),_(V,{type:"warning",plain:"",icon:"download",onClick:ge},{default:o(()=>[g(f(a.$t("btn.export")),1)]),_:1})),[[R,["bannerconfig:export"]]])]),_:1}),n(be,{showSearch:t(x),"onUpdate:showSearch":l[5]||(l[5]=e=>Z(x)?x.value=e:null),onQueryTable:w,columns:t(E)},null,8,["showSearch","columns"])]),_:1}),T((s(),_(ye,{"row-key":"id",columns:t(E),data:t(A),dicts:t(C),"header-cell-class-name":"el-table-header-cell",onSortChange:ie},{beginTime:o(({row:e})=>[P("div",null,f(t(ee)(e.beginTime)),1),P("div",null,f(t(ee)(e.endTime)),1)]),sortId:o(({scope:e})=>[T(P("span",{onClick:B=>ne(e.$index)},f(e.row.sortId),9,Re),[[O,t(q)!=e.$index]]),T(n(c,{ref:te,modelValue:e.row.sortId,"onUpdate:modelValue":B=>e.row.sortId=B,onBlur:B=>oe(e.row)},null,8,["modelValue","onUpdate:modelValue","onBlur"]),[[O,t(q)==e.$index]])]),actions:o(({scope:e})=>[T(n(V,{type:"success",size:"small",icon:"edit",title:"编辑",onClick:B=>me(e.row)},null,8,["onClick"]),[[R,["bannerconfig:edit"]]]),T(n(V,{type:"danger",size:"small",icon:"delete",title:"删除",onClick:B=>ce(e.row)},null,8,["onClick"]),[[R,["bannerconfig:delete"]]])]),_:1},8,["columns","data","dicts"])),[[he,t(L)]]),n(_e,{total:t(Q),page:t(i).pageNum,"onUpdate:page":l[6]||(l[6]=e=>t(i).pageNum=e),limit:t(i).pageSize,"onUpdate:limit":l[7]||(l[7]=e=>t(i).pageSize=e),onPagination:w},null,8,["total","page","limit"]),n(Te,{title:t(D),"lock-scroll":!1,modelValue:t(v),"onUpdate:modelValue":l[18]||(l[18]=e=>Z(v)?v.value=e:null),width:"600"},Ce({default:o(()=>[n(G,{ref_key:"formRef",ref:re,model:t(u),rules:t(de),"label-width":"100px"},{default:o(()=>[n(H,{gutter:20},{default:o(()=>[n(y,{lg:24},{default:o(()=>[n(r,{label:"类型",prop:"adType"},{default:o(()=>[n($,{modelValue:t(u).adType,"onUpdate:modelValue":l[8]||(l[8]=e=>t(u).adType=e)},{default:o(()=>[(s(!0),S(I,null,j(t(C).sys_ad_type,e=>(s(),_(J,{key:e.dictValue,value:parseInt(e.dictValue)},{default:o(()=>[g(f(e.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),n(y,{lg:24},{default:o(()=>[n(r,{label:"标题",prop:"title"},{default:o(()=>[n(c,{modelValue:t(u).title,"onUpdate:modelValue":l[9]||(l[9]=e=>t(u).title=e),placeholder:"请输入标题"},null,8,["modelValue"])]),_:1})]),_:1}),n(y,{lg:24},{default:o(()=>[n(r,{label:"说明",prop:"content"},{default:o(()=>[n(c,{modelValue:t(u).content,"onUpdate:modelValue":l[10]||(l[10]=e=>t(u).content=e),placeholder:"请输入说明"},null,8,["modelValue"])]),_:1})]),_:1}),n(y,{lg:24},{default:o(()=>[n(r,{label:"图片",prop:"imgUrl"},{default:o(()=>[n(ve,{modelValue:t(u).imgUrl,"onUpdate:modelValue":l[11]||(l[11]=e=>t(u).imgUrl=e),limit:1,data:{uploadType:1}},null,8,["modelValue"])]),_:1})]),_:1}),n(y,{lg:24},{default:o(()=>[n(r,{label:"跳转类型",prop:"jumpType"},{default:o(()=>[n($,{modelValue:t(u).jumpType,"onUpdate:modelValue":l[12]||(l[12]=e=>t(u).jumpType=e)},{default:o(()=>[(s(!0),S(I,null,j(t(C).jumpTypeOptions,e=>(s(),_(h,{key:e.dictValue,value:parseInt(e.dictValue)},{default:o(()=>[g(f(e.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(u).jumpType!=0?(s(),_(y,{key:0,lg:24},{default:o(()=>[n(r,{label:"链接",prop:"link"},{default:o(()=>[n(c,{modelValue:t(u).link,"onUpdate:modelValue":l[13]||(l[13]=e=>t(u).link=e),placeholder:"请输入链接"},null,8,["modelValue"])]),_:1})]),_:1})):Se("",!0),n(y,{lg:12},{default:o(()=>[n(r,{label:"开始时间",prop:"beginTime"},{default:o(()=>[n(W,{modelValue:t(u).beginTime,"onUpdate:modelValue":l[14]||(l[14]=e=>t(u).beginTime=e),type:"datetime",placeholder:"选择日期时间"},null,8,["modelValue"])]),_:1})]),_:1}),n(y,{lg:12},{default:o(()=>[n(r,{label:"结束时间",prop:"endTime"},{default:o(()=>[n(W,{modelValue:t(u).endTime,"onUpdate:modelValue":l[15]||(l[15]=e=>t(u).endTime=e),type:"datetime",placeholder:"选择日期时间"},null,8,["modelValue"])]),_:1})]),_:1}),n(y,{lg:12},{default:o(()=>[n(r,{label:"排序id",prop:"sortId"},{default:o(()=>[n(Ve,{modelValue:t(u).sortId,"onUpdate:modelValue":l[16]||(l[16]=e=>t(u).sortId=e),modelModifiers:{number:!0},min:0,controls:!0,"controls-position":"right",placeholder:"请输入排序id"},null,8,["modelValue"])]),_:1})]),_:1}),n(y,{lg:12},{default:o(()=>[n(r,{label:"是否显示",prop:"showStatus"},{default:o(()=>[n($,{modelValue:t(u).showStatus,"onUpdate:modelValue":l[17]||(l[17]=e=>t(u).showStatus=e)},{default:o(()=>[(s(!0),S(I,null,j(t(C).sys_show_hide,e=>(s(),_(J,{key:e.dictValue,value:parseInt(e.dictValue)},{default:o(()=>[g(f(e.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:2},[t(N)!=3?{name:"footer",fn:o(()=>[n(V,{text:"",onClick:se},{default:o(()=>[g(f(a.$t("btn.cancel")),1)]),_:1}),n(V,{type:"primary",onClick:fe},{default:o(()=>[g(f(a.$t("btn.submit")),1)]),_:1})]),key:"0"}:void 0]),1032,["title","modelValue"])])}}});export{Fe as default};
