import{i as f}from"./index-BtiuLXK9.js";import{M as u,o as d,c as m,V as p,B as y,v as g}from"./index-D9f5ARRd.js";const x=6e3,S={__name:"<PERSON><PERSON><PERSON>",props:{className:{type:String,default:"chart"},width:{type:String,default:"100%"},height:{type:String,default:"300px"}},setup(t){let e=null;const{proxy:s}=g(),r=["衬衫","羊毛衫","雪纺衫","裤子","高跟鞋","袜子"],i=[79,52,200,334,390,330,220],c=["#fa796f","#54c1fb","#ca6cd4","#59dcc1","#09a4ea","#e98f4d","#ea8e49"],o=[];i.forEach((l,a)=>{let h={value:i[a],itemStyle:{color:c[a]}};o.push(h)});function n(){e=f(s.$refs.chartRef,"macarons"),e.setOption({tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},title:{text:"销量统计",textStyle:{color:"#00e4ff"}},grid:{top:60,left:"2%",right:"2%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",data:r,axisTick:{alignWithLabel:!0}}],yAxis:[{type:"value",axisTick:{show:!1}}],series:[{name:"销量",type:"bar",stack:"vistors",barWidth:"40%",data:o,animationDuration:x,label:{show:!0}}]})}return u(()=>{n()}),(l,a)=>(d(),m("div",{ref:"chartRef",class:p(t.className),style:y({height:t.height,width:t.width})},null,6))}};export{S as default};
