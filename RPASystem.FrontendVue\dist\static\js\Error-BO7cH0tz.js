import{e as m}from"./401-mUbbotS2.js";import{_ as p,J as u,e as f,r as g,c as h,i as t,l as o,x as r,o as x,j as s,t as k,k as n,D as w}from"./index-D9f5ARRd.js";const v={class:"errPage-container"},E={class:"text-danger"},S={class:"list-unstyled"},b=["src"],j={__name:"Error",setup(B){const c=u(),l=f(()=>c.globalErrorMsg),_=g(m+"?"+ +new Date);return(D,e)=>{const i=r("router-link"),a=r("el-col"),d=r("el-row");return x(),h("div",v,[t(d,null,{default:o(()=>[t(a,{span:12},{default:o(()=>[e[1]||(e[1]=s("h1",{class:"text-jumbo text-ginormous"},"提示!",-1)),s("h3",E,k(n(l).msg),1),s("div",S,[t(i,{to:"/"},{default:o(()=>e[0]||(e[0]=[w(" 回首页 ")])),_:1})])]),_:1}),t(a,{span:12},{default:o(()=>[s("img",{src:n(_),width:"313",height:"428",alt:"Girl has dropped her ice cream."},null,8,b)]),_:1})]),_:1})])}}},y=p(j,[["__scopeId","data-v-c66d820c"]]);export{y as default};
