import{_ as Qe,r as b,e as ve,M as et,R as tt,x as v,o as i,c as y,O as lt,S as at,i as a,l as o,z as Z,D as f,k as U,T as ot,U as nt,j as p,V as rt,t as c,p as T,B as A,W as st,q as N,X as ut,Y as it,Z as dt,F as H,K as fe,s as pt,n as G,P as w,H as Q,$ as ct}from"./index-D9f5ARRd.js";import{c as mt}from"./clipboard-yJaGoOU6.js";import{R as ge}from"./ResourceSelector-BUKaoZyN.js";import{J as vt,s as ft,g as gt,c as bt,d as wt,r as kt,a as yt,b as ht,e as Tt,u as _t}from"./ParametersEditor-DialksbR.js";import St from"./SubTaskDialog-DPYHX2MQ.js";import xt from"./RemoteDesktop-D2iZK8lV.js";import{g as Pt}from"./SystemConfigManager-DIcLld2v.js";const Vt={class:"app-container"},Ct=["onClick"],Rt={key:1},Nt={key:1},Ft=["onClick"],It={key:1},Ut={style:{"text-align":"left","white-space":"pre-wrap"}},$t={class:"progress-container"},Dt={class:"progress-bar"},Mt=["onClick"],Et={key:1},zt=["onClick"],Jt={key:1},jt=["onClick","title"],Bt=["title"],Ot={key:2},Lt={class:"pagination-container"},At={class:"dialog-footer"},Kt={class:"dialog-footer"},Ht=pt({name:"jobtaskmanager"}),Wt=Object.assign(Ht,{setup(Yt){const be=b(!0),$=b(null),D=b(!1),we=()=>{D.value=!D.value,G(()=>{if($.value){const l=e=>{e.forEach(r=>{$.value.toggleRowExpansion(r,D.value),r.children&&r.children.length>0&&l(r.children)})};l(W.value)}})},ee=b([]),M=b([]),E=b(!1),s=b({priority:10,exeProgramId:null,inputParameters:"",taskType:0,resourceSelection:"",notes:"",outputFile:""}),F=b([]),I=b({visible:!1,parentTaskId:null,parentTaskName:""}),z=b({visible:!1,machineName:""}),J=b(!1),g=b({id:null,priority:null,resourceSelection:"",status:"",notes:""}),W=ve(()=>{const l=[],e={};return ee.value.forEach(r=>{const n={...r,children:[]};e[r.jobTaskId]=n,r.parentTaskID===0?l.push(n):e[r.parentTaskID]?e[r.parentTaskID].children.push(n):l.push(n)}),l}),h=b({taskName:"",programName:"",resourceMachine:"",taskType:null,status:""}),_=b({pageNumber:1,pageSize:10,total:0}),V=async()=>{try{const l={...h.value,pageNumber:_.value.pageNumber,pageSize:_.value.pageSize},e=await ft(l);ee.value=e.data.items,_.value.total=e.data.totalCount,D.value&&G(()=>{ke()})}catch(l){console.error("获取任务列表失败:",l),w.error("获取任务列表失败")}},ke=()=>{$.value&&setTimeout(()=>{const l=e=>{e.forEach(r=>{$.value.toggleRowExpansion(r,!0),r.children&&r.children.length>0&&l(r.children)})};l(W.value)},100)},j=()=>{_.value.pageNumber=1,V()},ye=()=>{h.value={taskName:"",programName:"",resourceMachine:"",taskType:null,status:""},j()},he=l=>{_.value.pageNumber=l,V()},Te=async()=>{try{const l=await gt();M.value=l.data}catch(l){console.error("获取程序列表失败:",l)}},te=()=>{s.value={priority:10,exeProgramId:"",inputParameters:"{}",taskType:0,resourceSelection:"",notes:"",outputFile:F.value.length>0?F.value[0]:""},C.value={excelPerSplitNum:10,mergeType:""},Y.value="[]"},_e=()=>{te(),E.value=!0},S=b(!1),B=b(!1),Se=async()=>{var l,e,r;if(!S.value)try{if(S.value=!0,B.value=!0,s.value.taskType===2){const m={...JSON.parse(s.value.inputParameters||"{}"),ExcelPerSplitNum:C.value.excelPerSplitNum.toString()};C.value.mergeType!==""&&(m.MergeType=C.value.mergeType),s.value.inputParameters=JSON.stringify(m)}if(!Ne(s.value.inputParameters)){S.value=!1,B.value=!1;return}const n={programName:(l=M.value.find(u=>u.id===s.value.exeProgramId))==null?void 0:l.programName,inputParameters:s.value.inputParameters,taskPriority:s.value.priority,taskType:s.value.taskType,resourceSelection:s.value.resourceSelection,notes:s.value.notes,outputFile:s.value.outputFile};await bt(n),E.value=!1,w.success("创建任务成功"),te(),await V()}catch(n){console.error("创建任务失败:",n),w.error("创建任务失败: "+(((r=(e=n.response)==null?void 0:e.data)==null?void 0:r.error)||n.message))}finally{S.value=!1,B.value=!1}},xe=l=>Q.confirm("此操作将永久删除该任务及其所有子任务，是否继续？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{wt(l.jobTaskId).then(()=>{w.success("删除成功"),V()})}).catch(()=>{}),Pe=async l=>{try{await Q.confirm("确定要重试该任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await kt(l),w.success("已开始重试任务"),await V()}catch(e){e!=="cancel"&&(console.error("重试任务失败:",e),w.error("重试任务失败"))}},Ve=async l=>{try{await Q.confirm("确定要停止该任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await yt(l),w.success("已发送停止指令"),await V()}catch(e){e!=="cancel"&&(console.error("停止任务失败:",e),w.error("停止任务失败"))}},Y=b("[]"),Ce=async l=>{const e=M.value.find(r=>r.id===l);if(e){le.value=e.programType===0;try{const r=await ht(l);Y.value=r.data.inputParameters,await G(),e.resourceSelection?s.value={...s.value,resourceSelection:e.resourceSelection}:s.value={...s.value,resourceSelection:""}}catch(r){console.error("获取程序参数失败:",r),w.error("获取程序参数失败")}}},Re=l=>{s.value.inputParameters=l},Ne=l=>{if(s.value.taskType===2)try{const e=JSON.parse(l);if(!e.InputFile||e.InputFile==="")return w.error("系统编排拆分任务必须上传InputFile文件"),!1}catch{return w.error("参数格式错误"),!1}return!0},Fe=l=>{w.error(l)},C=b({excelPerSplitNum:10,mergeType:""}),le=b(!1),Ie=({row:l})=>l.parentTaskID!==0?"child-task-row":"",ae=l=>({Pending:"info",Running:"primary",Success:"success",Failed:"danger",Cancelled:"warning"})[l]||"info",Ue=l=>{try{const e=JSON.parse(l||"{}");return e.InputFile&&e.InputFile!==""}catch{return!1}},$e=async l=>{try{const e=JSON.parse(l||"{}");if(e.InputFile){const r=e.InputFile,n=await Tt(r),u=window.URL.createObjectURL(new Blob([n.data])),m=document.createElement("a");m.href=u;const d=new Date,x=d.getFullYear()+String(d.getMonth()+1).padStart(2,"0")+String(d.getDate()).padStart(2,"0")+String(d.getHours()).padStart(2,"0")+String(d.getMinutes()).padStart(2,"0")+String(d.getSeconds()).padStart(2,"0")+".xlsx";m.setAttribute("download",x),document.body.appendChild(m),m.click(),document.body.removeChild(m),window.URL.revokeObjectURL(u)}}catch(e){console.error("下载文件失败:",e),w.error("下载文件失败，请重试")}},De=l=>{try{return JSON.parse(l||"{}").ReturnResult!==void 0}catch{return!1}},Me=l=>{try{const e=JSON.parse(l||"{}");e.ReturnResult!==void 0&&mt(e.ReturnResult)}catch(e){console.error("解析输出结果失败:",e),w.error("解析输出结果失败")}},Ee=l=>{_.value.pageSize=l,_.value.pageNumber=1,V()};let O=null;const ze=async()=>{try{O=new ct().withUrl("/resourceMachineHub").build(),O.on("RefreshJobTasks",()=>{console.log("RefreshJobTasks signal received"),V()}),await O.start(),console.log("SignalR Connected")}catch(l){console.error("SignalR Connection Error: ",l),w.error("实时连接失败，页面更新可能不及时")}},Je=async()=>{try{const l=await Pt("FileStorage.ServerIPs");l.data&&(F.value=l.data.split(",").map(e=>e.trim()).filter(e=>e!==""),F.value.length>0&&(s.value.outputFile=F.value[0]))}catch(l){console.error("获取文件服务器列表失败:",l)}},oe=ve(()=>{if(!s.value.exeProgramId)return!1;const l=M.value.find(e=>e.id===s.value.exeProgramId);return(l==null?void 0:l.programType)!==2}),je=l=>{I.value={visible:!0,parentTaskId:l.jobTaskId,parentTaskName:l.jobTaskName}},Be=l=>{z.value={visible:!0,machineName:l}},Oe=l=>{g.value={id:l.jobTaskId,priority:null,resourceSelection:l.resourceSelection||"",status:"",notes:l.notes||""},J.value=!0},Le=async()=>{try{S.value=!0;const l={};g.value.priority!==null&&(l.priority=g.value.priority),g.value.resourceSelection&&(l.resourceSelection=g.value.resourceSelection),g.value.status!==""&&(l.status=g.value.status),g.value.notes&&(l.notes=g.value.notes),await _t(g.value.id,l),w.success("更新成功"),J.value=!1,await V()}catch(l){w.error(`更新失败: ${l.message}`)}finally{S.value=!1}};et(async()=>{const e=new URLSearchParams(window.location.search).get("status");e&&(h.value.status=e),await V(),await Te(),await Je(),await ze()}),tt(()=>{O&&O.stop()});const ne=(l,e)=>{if(!l||!e)return"";const r=new Date(l),n=new Date(e),u=Math.floor((n-r)/1e3);if(u<0)return"";const m=Math.floor(u/(24*3600)),d=Math.floor(u%(24*3600)/3600),x=Math.floor(u%3600/60),R=u%60;return m===0&&d===0&&x===0?`${R}秒`:m===0&&d===0?x===0?`${R}秒`:`${x}分${R}秒`:m===0?`${d}时${x}分${R}秒`:`${m}天${d}时${x}分${R}秒`},L=(l,e)=>{var u;if(!((u=l.childTaskStats)!=null&&u.total))return"0%";const r=l.childTaskStats.total;let n;switch(e){case"Success":n=l.childTaskStats.success;break;case"Running":n=l.childTaskStats.running;break;case"Failed":n=l.childTaskStats.failed;break;case"Pending":n=l.childTaskStats.pending;break;case"Cancelled":n=l.childTaskStats.cancelled;break;default:n=0}return`${(n/r*100).toFixed(1)}%`},Ae=l=>({0:"info",1:"warning",2:"success"})[l]||"info",Ke=l=>{if(!l)return!1;const e=l.toLowerCase();if(e.startsWith("\\\\")||e.startsWith("//")||e.includes("://"))return!0;const r=e.match(/^([a-z]):\\/i);if(r){const n=r[1].toLowerCase();if(n>="g"&&n<="z")return!0}return!1},He=l=>{if(l)try{const e="opendir:"+l;console.log("Opening directory:",e),window.location.href=e}catch(e){console.error("打开目录失败:",e),w.error("打开目录失败")}};return(l,e)=>{const r=v("el-input"),n=v("el-form-item"),u=v("el-option"),m=v("el-select"),d=v("el-button"),x=v("el-form"),R=v("el-icon"),re=v("el-col"),We=v("el-row"),k=v("el-table-column"),q=v("el-tag"),K=v("el-tooltip"),Ye=v("el-button-group"),qe=v("el-table"),Xe=v("el-pagination"),X=v("el-input-number"),se=v("el-radio-button"),Ze=v("el-radio-group"),ue=v("el-dialog"),Ge=v("el-loading");return i(),y("div",Vt,[lt(a(x,{model:h.value,inline:!0},{default:o(()=>[a(n,{label:"任务名称"},{default:o(()=>[a(r,{modelValue:h.value.taskName,"onUpdate:modelValue":e[0]||(e[0]=t=>h.value.taskName=t),placeholder:"请输入任务名称",clearable:"",onKeyup:Z(j,["enter"]),style:{width:"200px"}},null,8,["modelValue"])]),_:1}),a(n,{label:"程序名称"},{default:o(()=>[a(r,{modelValue:h.value.programName,"onUpdate:modelValue":e[1]||(e[1]=t=>h.value.programName=t),placeholder:"请输入程序名称",clearable:"",onKeyup:Z(j,["enter"]),style:{width:"200px"}},null,8,["modelValue"])]),_:1}),a(n,{label:"资源机"},{default:o(()=>[a(r,{modelValue:h.value.resourceMachine,"onUpdate:modelValue":e[2]||(e[2]=t=>h.value.resourceMachine=t),placeholder:"请输入资源机",clearable:"",onKeyup:Z(j,["enter"]),style:{width:"200px"}},null,8,["modelValue"])]),_:1}),a(n,{label:"任务类型"},{default:o(()=>[a(m,{modelValue:h.value.taskType,"onUpdate:modelValue":e[3]||(e[3]=t=>h.value.taskType=t),placeholder:"请选择任务类型",clearable:"",style:{width:"150px"}},{default:o(()=>[a(u,{label:"普通任务",value:0}),a(u,{label:"编排任务",value:1}),a(u,{label:"系统编排拆分任务",value:2})]),_:1},8,["modelValue"])]),_:1}),a(n,{label:"状态"},{default:o(()=>[a(m,{modelValue:h.value.status,"onUpdate:modelValue":e[4]||(e[4]=t=>h.value.status=t),placeholder:"请选择状态",clearable:"",style:{width:"150px"}},{default:o(()=>[a(u,{label:"待运行",value:"Pending"}),a(u,{label:"运行中",value:"Running"}),a(u,{label:"成功",value:"Success"}),a(u,{label:"失败",value:"Failed"}),a(u,{label:"已取消",value:"Cancelled"})]),_:1},8,["modelValue"])]),_:1}),a(n,null,{default:o(()=>[a(d,{type:"primary",icon:"Search",onClick:j},{default:o(()=>e[26]||(e[26]=[f("搜索")])),_:1}),a(d,{icon:"Refresh",onClick:ye},{default:o(()=>e[27]||(e[27]=[f("重置")])),_:1})]),_:1})]),_:1},8,["model"]),[[at,be.value]]),a(We,{gutter:10,class:"mb8"},{default:o(()=>[a(re,{span:1.5,style:{"margin-left":"auto"}},{default:o(()=>[a(d,{type:"primary",plain:"",onClick:_e},{default:o(()=>[a(R,null,{default:o(()=>[a(U(ot))]),_:1}),e[28]||(e[28]=f("新增任务 "))]),_:1})]),_:1}),a(re,{span:1.5},{default:o(()=>[a(d,{type:"info",plain:"",onClick:we},{default:o(()=>[a(R,null,{default:o(()=>[a(U(nt))]),_:1}),e[29]||(e[29]=f("展开/折叠 "))]),_:1})]),_:1})]),_:1}),a(qe,{data:W.value,"row-key":"jobTaskId","tree-props":{children:"children",hasChildren:"hasChildren"},style:{width:"100%","margin-top":"8px"},"row-class-name":Ie,border:"",size:"small","header-cell-style":{background:"#f8f8f9",color:"#606266"},ref_key:"taskTableRef",ref:$,"default-expand-all":D.value},{default:o(()=>[a(k,{prop:"jobTaskName",label:"任务名称","min-width":"180","class-name":"tree-node-column"},{default:o(t=>[p("span",{class:rt({"child-task":t.row.parentTaskID!==0})},c(t.row.jobTaskName),3)]),_:1}),a(k,{prop:"exeProgramName",label:"程序名称","min-width":"150","show-overflow-tooltip":""},{default:o(t=>[t.row.taskType===2?(i(),y("span",{key:0,class:"clickable-text",onClick:P=>je(t.row)},c(t.row.exeProgramName),9,Ct)):(i(),y("span",Rt,c(t.row.exeProgramName),1))]),_:1}),a(k,{prop:"taskType",label:"任务类型",width:"90","show-overflow-tooltip":""},{default:o(t=>[a(q,{type:Ae(t.row.taskType),effect:"plain"},{default:o(()=>[f(c(t.row.taskType===0?"普通任务":t.row.taskType===1?"编排任务":t.row.taskType===2?"拆分任务":"未知类型"),1)]),_:2},1032,["type"])]),_:1}),a(k,{prop:"priority",label:"优先级",width:"60"}),a(k,{prop:"createdAt",label:"创建时间",width:"95","show-overflow-tooltip":""}),a(k,{prop:"startTime",label:"开始时间",width:"95","show-overflow-tooltip":""}),a(k,{label:"运行时长",width:"95","show-overflow-tooltip":""},{default:o(t=>[t.row.endTime?(i(),T(K,{key:0,content:`结束时间: ${t.row.endTime}`,placement:"top",effect:"light"},{default:o(()=>[p("span",null,c(ne(t.row.startTime,t.row.endTime)),1)]),_:2},1032,["content"])):(i(),y("span",Nt,c(ne(t.row.startTime,t.row.endTime)),1))]),_:1}),a(k,{prop:"resourceSelection",label:"资源选择",width:"70","show-overflow-tooltip":""}),a(k,{prop:"assignedResourceMachine",label:"资源机",width:"70","show-overflow-tooltip":""},{default:o(t=>[t.row.status==="Running"&&t.row.assignedResourceMachine?(i(),y("span",{key:0,class:"clickable-text",onClick:P=>Be(t.row.assignedResourceMachine)},c(t.row.assignedResourceMachine),9,Ft)):(i(),y("span",It,c(t.row.assignedResourceMachine),1))]),_:1}),a(k,{prop:"status",label:"状态",width:"95"},{default:o(t=>[t.row.taskType===0?(i(),T(q,{key:0,type:ae(t.row.status)},{default:o(()=>[f(c(t.row.status),1)]),_:2},1032,["type"])):t.row.taskType===2?(i(),T(K,{key:1,placement:"top","show-after":100},{content:o(()=>{var P,ie,de,pe,ce,me;return[p("div",Ut,[f(" 总状态："+c(t.row.status),1),e[30]||(e[30]=p("br",null,null,-1)),f(" 总任务数："+c(((P=t.row.childTaskStats)==null?void 0:P.total)||0),1),e[31]||(e[31]=p("br",null,null,-1)),f(" 成功："+c(((ie=t.row.childTaskStats)==null?void 0:ie.success)||0),1),e[32]||(e[32]=p("br",null,null,-1)),f(" 运行中："+c(((de=t.row.childTaskStats)==null?void 0:de.running)||0),1),e[33]||(e[33]=p("br",null,null,-1)),f(" 失败："+c(((pe=t.row.childTaskStats)==null?void 0:pe.failed)||0),1),e[34]||(e[34]=p("br",null,null,-1)),f(" 等待中："+c(((ce=t.row.childTaskStats)==null?void 0:ce.pending)||0),1),e[35]||(e[35]=p("br",null,null,-1)),f(" 已取消："+c(((me=t.row.childTaskStats)==null?void 0:me.cancelled)||0),1)])]}),default:o(()=>[p("div",$t,[p("div",Dt,[p("div",{class:"progress-segment success",style:A({width:L(t.row,"Success")})},null,4),p("div",{class:"progress-segment running",style:A({width:L(t.row,"Running")})},null,4),p("div",{class:"progress-segment failed",style:A({width:L(t.row,"Failed")})},null,4),p("div",{class:"progress-segment pending",style:A({width:L(t.row,"Pending")})},null,4),p("div",{class:"progress-segment cancelled",style:A({width:L(t.row,"Cancelled")})},null,4)])])]),_:2},1024)):(i(),T(q,{key:2,type:ae(t.row.status)},{default:o(()=>[f(c(t.row.status),1)]),_:2},1032,["type"]))]),_:1}),a(k,{prop:"inputParameters",label:"输入参数",width:"80","show-overflow-tooltip":""},{default:o(t=>[Ue(t.row.inputParameters)?(i(),T(K,{key:0,content:t.row.inputParameters,placement:"top",effect:"light"},{default:o(()=>[p("span",{class:"clickable-text",onClick:P=>$e(t.row.inputParameters)}," 下载输入 ",8,Mt)]),_:2},1032,["content"])):(i(),y("span",Et,c(t.row.inputParameters),1))]),_:1}),a(k,{prop:"outputResults",label:"返回结果",width:"80","show-overflow-tooltip":""},{default:o(t=>[De(t.row.outputResults)?(i(),T(K,{key:0,content:t.row.outputResults,placement:"top",effect:"light"},{default:o(()=>[p("span",{class:"clickable-text",onClick:P=>Me(t.row.outputResults)}," 复制结果 ",8,zt)]),_:2},1032,["content"])):(i(),y("span",Jt,c(t.row.outputResults),1))]),_:1}),a(k,{prop:"outputFile",label:"输出文件","min-width":"80","show-overflow-tooltip":""},{default:o(t=>[t.row.outputFile&&Ke(t.row.outputFile)?(i(),y("span",{key:0,class:"clickable-text",onClick:P=>He(t.row.outputFile),title:t.row.outputFile}," 打开目录 ",8,jt)):t.row.outputFile?(i(),y("span",{key:1,title:t.row.outputFile},c(t.row.outputFile),9,Bt)):(i(),y("span",Ot))]),_:1}),a(k,{prop:"retryCount",label:"重试数",width:"55",align:"center"}),a(k,{prop:"notes",label:"备注","min-width":"80","show-overflow-tooltip":""}),a(k,{label:"操作",width:"100",align:"center"},{default:o(t=>[a(Ye,null,{default:o(()=>[t.row.status!=="Pending"&&t.row.status!=="Running"?(i(),T(d,{key:0,type:"primary",icon:U(st),circle:"",size:"small",onClick:P=>Pe(t.row.jobTaskId)},null,8,["icon","onClick"])):N("",!0),t.row.status==="Running"||t.row.status==="Pending"?(i(),T(d,{key:1,type:"warning",icon:U(ut),circle:"",size:"small",onClick:P=>Ve(t.row.jobTaskId)},null,8,["icon","onClick"])):N("",!0),a(d,{type:"primary",icon:U(it),circle:"",size:"small",onClick:P=>Oe(t.row)},null,8,["icon","onClick"]),a(d,{type:"danger",icon:U(dt),circle:"",size:"small",onClick:P=>xe(t.row)},null,8,["icon","onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data","default-expand-all"]),p("div",Lt,[a(Xe,{"current-page":_.value.pageNumber,"onUpdate:currentPage":e[5]||(e[5]=t=>_.value.pageNumber=t),"page-size":_.value.pageSize,"onUpdate:pageSize":e[6]||(e[6]=t=>_.value.pageSize=t),"page-sizes":[10,20,50,100],total:_.value.total,layout:"total, sizes, prev, pager, next, jumper",background:!0,onSizeChange:Ee,onCurrentChange:he},null,8,["current-page","page-size","total"])]),a(ue,{modelValue:E.value,"onUpdate:modelValue":e[16]||(e[16]=t=>E.value=t),title:"新增任务"},{footer:o(()=>[p("span",At,[a(d,{onClick:e[15]||(e[15]=t=>E.value=!1),disabled:S.value},{default:o(()=>e[38]||(e[38]=[f("取消")])),_:1},8,["disabled"]),a(d,{type:"primary",onClick:Se,loading:S.value,disabled:S.value},{default:o(()=>[f(c(S.value?"保存中...":"保存"),1)]),_:1},8,["loading","disabled"])])]),default:o(()=>[a(x,{model:s.value,"label-width":"120px"},{default:o(()=>[a(n,{label:"程序列表"},{default:o(()=>[a(m,{modelValue:s.value.exeProgramId,"onUpdate:modelValue":e[7]||(e[7]=t=>s.value.exeProgramId=t),placeholder:"请选择程序",onChange:Ce,filterable:""},{default:o(()=>[(i(!0),y(H,null,fe(M.value,t=>(i(),T(u,{key:t.id,label:t.programName,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(n,{label:"优先级"},{default:o(()=>[a(X,{modelValue:s.value.priority,"onUpdate:modelValue":e[8]||(e[8]=t=>s.value.priority=t),min:1,max:100,step:1},null,8,["modelValue"])]),_:1}),a(n,{label:"程序参数"},{default:o(()=>[a(vt,{"program-input-parameters":Y.value,"initial-parameters":s.value.inputParameters,"task-type":s.value.taskType,"onUpdate:parameters":Re,onFileTypeError:Fe},null,8,["program-input-parameters","initial-parameters","task-type"])]),_:1}),le.value?(i(),y(H,{key:0},[a(n,{label:"任务类型"},{default:o(()=>[a(Ze,{modelValue:s.value.taskType,"onUpdate:modelValue":e[9]||(e[9]=t=>s.value.taskType=t)},{default:o(()=>[a(se,{label:0},{default:o(()=>e[36]||(e[36]=[f("普通任务")])),_:1}),a(se,{label:2},{default:o(()=>e[37]||(e[37]=[f("系统编排拆分任务")])),_:1})]),_:1},8,["modelValue"])]),_:1}),s.value.taskType===2?(i(),y(H,{key:0},[a(n,{label:"每份拆分数量"},{default:o(()=>[a(X,{modelValue:C.value.excelPerSplitNum,"onUpdate:modelValue":e[10]||(e[10]=t=>C.value.excelPerSplitNum=t),min:1,step:1,placeholder:"请输入拆分数量"},null,8,["modelValue"])]),_:1}),a(n,{label:"合并类型"},{default:o(()=>[a(m,{modelValue:C.value.mergeType,"onUpdate:modelValue":e[11]||(e[11]=t=>C.value.mergeType=t),placeholder:"请选择合并类型",clearable:""},{default:o(()=>[a(u,{label:"不覆盖文件",value:"0"}),a(u,{label:"覆盖文件",value:"1"}),a(u,{label:"不覆盖文件合并相同Excel",value:"2"}),a(u,{label:"覆盖文件合并相同Excel",value:"3"})]),_:1},8,["modelValue"])]),_:1})],64)):N("",!0)],64)):N("",!0),oe.value?(i(),T(n,{key:1,label:"资源选择"},{default:o(()=>[a(ge,{"model-value":s.value.resourceSelection,"onUpdate:modelValue":e[12]||(e[12]=t=>s.value.resourceSelection=t)},null,8,["model-value"])]),_:1})):N("",!0),oe.value?(i(),T(n,{key:2,label:"输出文件服务器"},{default:o(()=>[a(m,{modelValue:s.value.outputFile,"onUpdate:modelValue":e[13]||(e[13]=t=>s.value.outputFile=t),placeholder:"请选择输出文件服务器",clearable:""},{default:o(()=>[(i(!0),y(H,null,fe(F.value,t=>(i(),T(u,{key:t,label:t,value:t},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),a(n,{label:"备注"},{default:o(()=>[a(r,{modelValue:s.value.notes,"onUpdate:modelValue":e[14]||(e[14]=t=>s.value.notes=t),placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(Ge,{visible:B.value,"onUpdate:visible":e[17]||(e[17]=t=>B.value=t),fullscreen:"","element-loading-text":"正在创建任务...","element-loading-background":"rgba(0, 0, 0, 0.8)"},null,8,["visible"]),a(ue,{modelValue:J.value,"onUpdate:modelValue":e[23]||(e[23]=t=>J.value=t),title:"编辑任务"},{footer:o(()=>[p("span",Kt,[a(d,{onClick:e[22]||(e[22]=t=>J.value=!1)},{default:o(()=>e[39]||(e[39]=[f("取消")])),_:1}),a(d,{type:"primary",onClick:Le,loading:S.value},{default:o(()=>e[40]||(e[40]=[f(" 确定 ")])),_:1},8,["loading"])])]),default:o(()=>[a(x,{model:g.value,"label-width":"120px"},{default:o(()=>[a(n,{label:"优先级"},{default:o(()=>[a(X,{modelValue:g.value.priority,"onUpdate:modelValue":e[18]||(e[18]=t=>g.value.priority=t),min:1,max:100,step:1,placeholder:"不修改留空",clearable:""},null,8,["modelValue"])]),_:1}),a(n,{label:"资源选择"},{default:o(()=>[a(ge,{"model-value":g.value.resourceSelection,"onUpdate:modelValue":e[19]||(e[19]=t=>g.value.resourceSelection=t)},null,8,["model-value"])]),_:1}),a(n,{label:"状态"},{default:o(()=>[a(m,{modelValue:g.value.status,"onUpdate:modelValue":e[20]||(e[20]=t=>g.value.status=t),placeholder:"不修改请留空",clearable:""},{default:o(()=>[a(u,{label:"待运行",value:0}),a(u,{label:"运行中",value:1}),a(u,{label:"成功",value:2}),a(u,{label:"失败",value:3}),a(u,{label:"已取消",value:4})]),_:1},8,["modelValue"])]),_:1}),a(n,{label:"备注"},{default:o(()=>[a(r,{modelValue:g.value.notes,"onUpdate:modelValue":e[21]||(e[21]=t=>g.value.notes=t),placeholder:"不修改请留空",clearable:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),I.value.visible?(i(),T(St,{key:0,visible:I.value.visible,"onUpdate:visible":e[24]||(e[24]=t=>I.value.visible=t),"parent-task-id":I.value.parentTaskId,"parent-task-name":I.value.parentTaskName},null,8,["visible","parent-task-id","parent-task-name"])):N("",!0),z.value.visible?(i(),T(xt,{key:1,visible:z.value.visible,"onUpdate:visible":e[25]||(e[25]=t=>z.value.visible=t),"machine-name":z.value.machineName},null,8,["visible","machine-name"])):N("",!0)])}}}),ll=Qe(Wt,[["__scopeId","data-v-b8eeeff3"]]);export{ll as default};
