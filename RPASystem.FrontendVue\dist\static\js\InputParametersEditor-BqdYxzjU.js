import{_ as U,r as I,w as P,x as d,o,c,F as D,K as T,i as t,l as p,p as V,q as h,D as _}from"./index-D9f5ARRd.js";const N={props:{programType:{type:Number,required:!0},initialParameters:{type:String,default:"[]"}},emits:["update:parameters"],setup(s,{emit:i}){const r=I([]),m=()=>{try{if(!s.initialParameters||s.initialParameters.trim()===""){s.programType===0?r.value=[{ParametersName:"InputFile",ParametersType:"file",ParametersDescription:"输入文件",DefaultValue:"",IsRequired:!0},{ParametersName:"ServerIP",ParametersType:"string",ParametersDescription:"服务器IP",DefaultValue:"",IsRequired:!0},{ParametersName:"UserName",ParametersType:"RpaCredentials",ParametersDescription:"用户名",DefaultValue:"",IsRequired:!0}]:r.value=[];return}const u=JSON.parse(s.initialParameters);Array.isArray(u)&&u.length>0?r.value=u:s.programType===0?r.value=[{ParametersName:"InputFile",ParametersType:"file",ParametersDescription:"输入文件",DefaultValue:"",IsRequired:!0},{ParametersName:"ServerIP",ParametersType:"string",ParametersDescription:"服务器IP",DefaultValue:"",IsRequired:!0},{ParametersName:"UserName",ParametersType:"RpaCredentials",ParametersDescription:"用户名",DefaultValue:"",IsRequired:!0}]:r.value=[]}catch(u){console.error("解析参数失败:",u),s.programType===0?r.value=[{ParametersName:"InputFile",ParametersType:"file",ParametersDescription:"输入文件",DefaultValue:"",IsRequired:!0},{ParametersName:"ServerIP",ParametersType:"string",ParametersDescription:"服务器IP",DefaultValue:"",IsRequired:!0},{ParametersName:"UserName",ParametersType:"RpaCredentials",ParametersDescription:"用户名",DefaultValue:"",IsRequired:!0}]:r.value=[]}},y=()=>{r.value.push({ParametersName:"",ParametersType:"string",ParametersDescription:"",DefaultValue:"",IsRequired:!0,ParametersOptions:"1",ParametersSelectValue:""}),l()},f=u=>{r.value.splice(u,1),l()},l=()=>{i("update:parameters",JSON.stringify(r.value))};return P(()=>s.initialParameters,m,{immediate:!0}),P(()=>s.programType,m),P(r,l,{deep:!0}),{parameters:r,addParameter:y,removeParameter:f}}};function R(s,i,r,m,y,f){const l=d("el-option"),u=d("el-select"),n=d("el-input"),x=d("el-switch"),g=d("el-button");return o(),c("div",null,[(o(!0),c(D,null,T(m.parameters,(e,v)=>(o(),c("div",{key:v,class:"param-row"},[t(u,{modelValue:e.ParametersType,"onUpdate:modelValue":a=>e.ParametersType=a,style:{width:"120px"}},{default:p(()=>[t(l,{label:"字符串",value:"string"}),t(l,{label:"整数",value:"int"}),t(l,{label:"布尔值",value:"bool"}),t(l,{label:"文件",value:"file"}),t(l,{label:"选择",value:"select"}),t(l,{label:"资源选择",value:"ResourceSelection"}),t(l,{label:"RPA账号凭证",value:"RpaCredentials"})]),_:2},1032,["modelValue","onUpdate:modelValue"]),t(n,{modelValue:e.ParametersName,"onUpdate:modelValue":a=>e.ParametersName=a,placeholder:"参数名称",style:{width:"150px",margin:"0 10px"}},null,8,["modelValue","onUpdate:modelValue"]),t(n,{modelValue:e.ParametersDescription,"onUpdate:modelValue":a=>e.ParametersDescription=a,placeholder:"参数描述",style:{width:"150px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"]),e.ParametersType==="RpaCredentials"?(o(),V(n,{key:0,modelValue:e.DefaultValue,"onUpdate:modelValue":a=>e.DefaultValue=a,placeholder:"默认账号名称",style:{width:"120px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"])):e.ParametersType!=="select"&&e.ParametersType!=="bool"?(o(),V(n,{key:1,modelValue:e.DefaultValue,"onUpdate:modelValue":a=>e.DefaultValue=a,placeholder:"默认值",style:{width:"120px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"])):e.ParametersType==="bool"?(o(),V(u,{key:2,modelValue:e.DefaultValue,"onUpdate:modelValue":a=>e.DefaultValue=a,style:{width:"120px","margin-right":"10px"}},{default:p(()=>[t(l,{label:"是",value:"true"}),t(l,{label:"否",value:"false"})]),_:2},1032,["modelValue","onUpdate:modelValue"])):h("",!0),e.ParametersType==="select"?(o(),c(D,{key:3},[t(n,{modelValue:e.ParametersSelectValue,"onUpdate:modelValue":a=>e.ParametersSelectValue=a,placeholder:"选项值(用|分隔)",style:{width:"200px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"]),t(u,{modelValue:e.ParametersOptions,"onUpdate:modelValue":a=>e.ParametersOptions=a,style:{width:"100px","margin-right":"10px"}},{default:p(()=>[t(l,{label:"单选",value:"1"}),t(l,{label:"多选",value:"2"})]),_:2},1032,["modelValue","onUpdate:modelValue"]),t(n,{modelValue:e.DefaultValue,"onUpdate:modelValue":a=>e.DefaultValue=a,placeholder:"默认值",style:{width:"120px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"])],64)):h("",!0),t(x,{modelValue:e.IsRequired,"onUpdate:modelValue":a=>e.IsRequired=a,"active-text":"必填","inactive-text":"选填",style:{"margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"]),t(g,{type:"danger",onClick:a=>m.removeParameter(v)},{default:p(()=>i[0]||(i[0]=[_("删除")])),_:2},1032,["onClick"])]))),128)),t(g,{type:"primary",onClick:m.addParameter,style:{"margin-top":"10px"}},{default:p(()=>i[1]||(i[1]=[_("添加参数")])),_:1},8,["onClick"])])}const w=U(N,[["render",R],["__scopeId","data-v-2e420405"]]);export{w as default};
