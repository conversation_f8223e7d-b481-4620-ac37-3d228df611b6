import{_ as w,r as I,w as P,x as n,o as s,c as p,F as h,K as k,i as l,l as m,p as y,q as _,D as x}from"./index-D9f5ARRd.js";const q={__name:"InputParametersEditor",props:{programType:{type:Number,required:!0},initialParameters:{type:String,default:"[]"}},emits:["update:parameters"],setup(D,{emit:U}){const u=D,T=U,t=I([]),f=()=>{try{if(!u.initialParameters||u.initialParameters.trim()===""){u.programType===0?t.value=[{ParametersName:"InputFile",ParametersType:"file",ParametersDescription:"输入文件",DefaultValue:"",IsRequired:!0},{ParametersName:"UserName",ParametersType:"RpaCredentials",ParametersDescription:"用户名",DefaultValue:"",IsRequired:!0}]:t.value=[];return}const o=JSON.parse(u.initialParameters);Array.isArray(o)&&o.length>0?t.value=o:u.programType===0?t.value=[{ParametersName:"InputFile",ParametersType:"file",ParametersDescription:"输入文件",DefaultValue:"",IsRequired:!0},{ParametersName:"UserName",ParametersType:"RpaCredentials",ParametersDescription:"用户名",DefaultValue:"",IsRequired:!0}]:t.value=[]}catch(o){console.error("解析参数失败:",o),u.programType===0?t.value=[{ParametersName:"InputFile",ParametersType:"file",ParametersDescription:"输入文件",DefaultValue:"",IsRequired:!0},{ParametersName:"UserName",ParametersType:"RpaCredentials",ParametersDescription:"用户名",DefaultValue:"",IsRequired:!0}]:t.value=[]}},b=()=>{t.value.push({ParametersName:"",ParametersType:"string",ParametersDescription:"",DefaultValue:"",IsRequired:!0,ParametersOptions:"1",ParametersSelectValue:""}),c()},N=o=>{t.value.splice(o,1),c()},c=()=>{T("update:parameters",JSON.stringify(t.value))};return P(()=>u.initialParameters,f,{immediate:!0}),P(()=>u.programType,f),P(t,c,{deep:!0}),(o,d)=>{const r=n("el-option"),V=n("el-select"),i=n("el-input"),R=n("el-switch"),g=n("el-button");return s(),p("div",null,[(s(!0),p(h,null,k(t.value,(e,v)=>(s(),p("div",{key:v,class:"param-row"},[l(V,{modelValue:e.ParametersType,"onUpdate:modelValue":a=>e.ParametersType=a,style:{width:"120px"}},{default:m(()=>[l(r,{label:"字符串",value:"string"}),l(r,{label:"整数",value:"int"}),l(r,{label:"布尔值",value:"bool"}),l(r,{label:"文件",value:"file"}),l(r,{label:"选择",value:"select"}),l(r,{label:"资源选择",value:"ResourceSelection"}),l(r,{label:"RPA账号凭证",value:"RpaCredentials"})]),_:2},1032,["modelValue","onUpdate:modelValue"]),l(i,{modelValue:e.ParametersName,"onUpdate:modelValue":a=>e.ParametersName=a,placeholder:"参数名称",style:{width:"150px",margin:"0 10px"}},null,8,["modelValue","onUpdate:modelValue"]),l(i,{modelValue:e.ParametersDescription,"onUpdate:modelValue":a=>e.ParametersDescription=a,placeholder:"参数描述",style:{width:"150px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"]),e.ParametersType==="RpaCredentials"?(s(),y(i,{key:0,modelValue:e.DefaultValue,"onUpdate:modelValue":a=>e.DefaultValue=a,placeholder:"默认账号名称",style:{width:"120px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"])):e.ParametersType!=="select"&&e.ParametersType!=="bool"?(s(),y(i,{key:1,modelValue:e.DefaultValue,"onUpdate:modelValue":a=>e.DefaultValue=a,placeholder:"默认值",style:{width:"120px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"])):e.ParametersType==="bool"?(s(),y(V,{key:2,modelValue:e.DefaultValue,"onUpdate:modelValue":a=>e.DefaultValue=a,style:{width:"120px","margin-right":"10px"}},{default:m(()=>[l(r,{label:"是",value:"true"}),l(r,{label:"否",value:"false"})]),_:2},1032,["modelValue","onUpdate:modelValue"])):_("",!0),e.ParametersType==="select"?(s(),p(h,{key:3},[l(i,{modelValue:e.ParametersSelectValue,"onUpdate:modelValue":a=>e.ParametersSelectValue=a,placeholder:"选项值(用|分隔)",style:{width:"200px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"]),l(V,{modelValue:e.ParametersOptions,"onUpdate:modelValue":a=>e.ParametersOptions=a,style:{width:"100px","margin-right":"10px"}},{default:m(()=>[l(r,{label:"单选",value:"1"}),l(r,{label:"多选",value:"2"})]),_:2},1032,["modelValue","onUpdate:modelValue"]),l(i,{modelValue:e.DefaultValue,"onUpdate:modelValue":a=>e.DefaultValue=a,placeholder:"默认值",style:{width:"120px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"])],64)):_("",!0),l(R,{modelValue:e.IsRequired,"onUpdate:modelValue":a=>e.IsRequired=a,"active-text":"必填","inactive-text":"选填",style:{"margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"]),l(g,{type:"danger",onClick:a=>N(v)},{default:m(()=>d[0]||(d[0]=[x("删除")])),_:2},1032,["onClick"])]))),128)),l(g,{type:"primary",onClick:b,style:{"margin-top":"10px"}},{default:m(()=>d[1]||(d[1]=[x("添加参数")])),_:1})])}}},S=w(q,[["__scopeId","data-v-067eb73f"]]);export{S as default};
