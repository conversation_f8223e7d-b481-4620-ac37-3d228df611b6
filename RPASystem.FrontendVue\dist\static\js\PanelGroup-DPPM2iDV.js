import{_ as h,E as k,e as w,J as N,x as a,o as x,p as B,l as n,i as t,j as e,k as p,t as m,D as v}from"./index-D9f5ARRd.js";const S={class:"card-panel-icon-wrapper icon-people"},D={class:"card-panel-description"},V={class:"card-panel-icon-wrapper icon-message"},T={class:"card-panel-description"},G={class:"card-panel-icon-wrapper icon-money"},L={class:"card-panel-description"},P={style:{display:"inline-flex","align-items":"center"}},j={class:"card-panel-icon-wrapper icon-shopping"},E={class:"card-panel-description"},I={style:{display:"inline-flex","align-items":"center"}},J={class:"statistic-footer"},U={class:"footer-item"},b={style:{color:"green"}},q={style:{color:"red"}},z={__name:"PanelGroup",setup(A,{emit:f}){const g=f,l=k({chatNum:1390,onlineNum:w(()=>N().onlineNum),amount:99998,order:1999});function i(o){g("handleSetLineChartData",o)}return(o,s)=>{const c=a("svg-icon"),r=a("el-statistic"),d=a("el-col"),y=a("CaretTop"),_=a("el-icon"),C=a("CaretBottom"),$=a("el-row");return x(),B($,{gutter:40,class:"panel-group"},{default:n(()=>[t(d,{xs:12,sm:12,lg:6,class:"card-panel-col"},{default:n(()=>[e("div",{class:"card-panel",onClick:s[0]||(s[0]=u=>i("newVisitis"))},[e("div",S,[t(c,{name:"peoples","class-name":"card-panel-icon"})]),e("div",D,[t(r,{title:o.$t("layout.onlineUsers"),value:p(l).onlineNum},null,8,["title","value"])])])]),_:1}),t(d,{xs:12,sm:12,lg:6,class:"card-panel-col"},{default:n(()=>[e("div",{class:"card-panel",onClick:s[1]||(s[1]=u=>i("messages"))},[e("div",V,[t(c,{name:"message","class-name":"card-panel-icon"})]),e("div",T,[t(r,{value:p(l).order,title:o.$t("layout.message")},null,8,["value","title"])])])]),_:1}),t(d,{xs:12,sm:12,lg:6,class:"card-panel-col"},{default:n(()=>[e("div",{class:"card-panel",onClick:s[2]||(s[2]=u=>i("purchases"))},[e("div",G,[t(c,{name:"money","class-name":"card-panel-icon"})]),e("div",L,[t(r,{value:p(l).order,title:o.$t("layout.amount")},{title:n(()=>[e("div",P,m(o.$t("layout.amount")),1)]),_:1},8,["value","title"])])])]),_:1}),t(d,{xs:12,sm:12,lg:6,class:"card-panel-col"},{default:n(()=>[e("div",{class:"card-panel",onClick:s[3]||(s[3]=u=>i("shoppings"))},[e("div",j,[t(c,{name:"shopping","class-name":"card-panel-icon"})]),e("div",E,[t(r,{value:p(l).order,title:o.$t("layout.order")},{title:n(()=>[e("div",I,m(o.$t("layout.order")),1)]),_:1},8,["value","title"]),e("div",J,[e("div",U,[s[6]||(s[6]=e("span",null,"环比",-1)),e("span",b,[s[4]||(s[4]=v(" 16% ")),t(_,null,{default:n(()=>[t(y)]),_:1})]),s[7]||(s[7]=e("span",null,"同比",-1)),e("span",q,[s[5]||(s[5]=v(" -16% ")),t(_,null,{default:n(()=>[t(C)]),_:1})])])])])])]),_:1})]),_:1})}}},H=h(z,[["__scopeId","data-v-f0f1c4f1"]]);export{H as default};
