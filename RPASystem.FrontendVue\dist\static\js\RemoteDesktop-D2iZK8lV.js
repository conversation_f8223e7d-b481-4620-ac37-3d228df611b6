import{_ as V,e as W,r as y,M as D,$ as H,a1 as U,a2 as $,P as s,R as j,w as q,x as I,o as v,p as L,l as P,j as m,c as p,A as Y,V as z,t as T,q as X,n as _,am as J}from"./index-D9f5ARRd.js";const F={class:"image-wrapper"},G=["src"],O={key:1,class:"loading-text"},Q={class:"control-buttons"},Z={key:0,class:"control-info"},ee={__name:"RemoteDesktop",props:{visible:{type:Boolean,required:!0},machineName:{type:String,required:!0}},emits:["update:visible"],setup(k,{emit:M}){const c=k,S=M,i=W({get:()=>c.visible,set:e=>S("update:visible",e)}),d=y(""),r=y(!1);let t=null;const u=y(null),h=y(new Set),C=async()=>{try{await t.invoke("StartWatching",c.machineName),_(()=>{u.value&&u.value.focus()})}catch(e){console.error("开始远程失败:",e),s.error("连接远程桌面失败"),i.value=!1}},g=async()=>{try{t&&t.state===J.Connected&&(await t.invoke("StopWatching",c.machineName),d.value="")}catch(e){console.error("停止远程失败:",e)}},w=()=>{g(),i.value=!1},B=()=>{r.value=!r.value,r.value?(s.success("远程控制已启用"),_(()=>{u.value&&u.value.focus()})):s.info("远程控制已禁用")},b=async(e,o)=>{if(r.value)try{const a=e.target.getBoundingClientRect(),l=Math.round((e.clientX-a.left)/a.width*100),f=Math.round((e.clientY-a.top)/a.height*100);await t.invoke("SendMouseCommand",c.machineName,o,l,f)}catch(n){console.error("发送鼠标命令失败:",n),s.error("远程控制操作失败")}},R=async e=>{if(r.value)try{e.preventDefault();const n=e.target.getBoundingClientRect(),a=Math.round((e.clientX-n.left)/n.width*100),l=Math.round((e.clientY-n.top)/n.height*100),f=e.deltaY>0?-1:1;await t.invoke("SendMouseWheelCommand",c.machineName,f,a,l)}catch(o){console.error("发送鼠标滚轮命令失败:",o),s.error("远程控制操作失败")}},x=async e=>{if(r.value)try{e.preventDefault(),h.value.add(e.key);const o=Array.from(h.value);await t.invoke("SendKeyboardCommand",c.machineName,"keydown",e.key,o,e.keyCode||0)}catch(o){console.error("发送键盘按下命令失败:",o),s.error("远程控制键盘操作失败")}},N=async e=>{if(r.value)try{e.preventDefault(),h.value.delete(e.key),await t.invoke("SendKeyboardCommand",c.machineName,"keyup",e.key,Array.from(h.value),e.keyCode||0)}catch(o){console.error("发送键盘释放命令失败:",o),s.error("远程控制键盘操作失败")}},E=()=>{u.value&&u.value.focus()},A=e=>{try{let o="";const n=new Uint8Array(e),a=1024;for(let l=0;l<n.length;l+=a)n.slice(l,Math.min(l+a,n.length)).forEach(K=>{o+=String.fromCharCode(K)});return window.btoa(o)}catch(o){throw console.error("Base64转换失败:",o),o}};return D(async()=>{t=new H().withUrl("/resourceMachineHub").withHubProtocol(new U).withAutomaticReconnect().configureLogging($.Information).build(),t.on("ReceiveScreenshot",e=>{try{if(typeof e=="string"){d.value=`data:image/jpeg;base64,${e}`;return}if(e instanceof ArrayBuffer){const o=A(e);d.value=`data:image/jpeg;base64,${o}`;return}}catch(o){console.error("处理截图数据失败:",o)}}),t.on("MachineDisconnected",e=>{c.machineName===e&&(s.error("远程机器已断开连接"),i.value=!1)}),t.on("ReceiveMouseCommandResult",(e,o)=>{e||s.error(`远程控制操作失败: ${o}`)}),t.on("ReceiveKeyboardCommandResult",(e,o)=>{e||s.error(`远程控制键盘操作失败: ${o}`)});try{await t.start(),i.value&&await C()}catch(e){console.error("SignalR Connection Error: ",e)}}),j(()=>{t&&(g(),t.stop())}),q(()=>i.value,async e=>{e?await C():(await g(),r.value=!1)}),(e,o)=>{const n=I("el-dialog");return v(),L(n,{modelValue:i.value,"onUpdate:modelValue":o[2]||(o[2]=a=>i.value=a),title:"远程桌面 - "+k.machineName,fullscreen:"",modal:!0,"close-on-click-modal":!1,"before-close":w,"destroy-on-close":"",class:"remote-dialog"},{default:P(()=>[m("div",{class:"remote-desktop-container",tabindex:"0",ref_key:"desktopContainer",ref:u,onKeydown:x,onKeyup:N,onClick:E},[m("div",F,[d.value?(v(),p("img",{key:0,src:d.value,class:"desktop-image",onClick:o[0]||(o[0]=a=>b(a,"left")),onContextmenu:o[1]||(o[1]=Y(a=>b(a,"right"),["prevent"])),onWheel:R},null,40,G)):(v(),p("div",O,"正在连接远程桌面..."))]),m("div",Q,[m("div",{class:z(["remote-control-button",{active:r.value}]),onClick:B}," 远程控制 "+T(r.value?"(已启用)":"(未启用)"),3),m("div",{class:"close-button",onClick:w},"关闭 (ESC)")]),r.value?(v(),p("div",Z," 远程控制已启用: 点击鼠标左键或右键可控制远程计算机，滚轮可滚动页面，键盘按键也将传输至远程计算机 ")):X("",!0)],544)]),_:1},8,["modelValue","title"])}}},te=V(ee,[["__scopeId","data-v-3e82da19"]]);export{te as default};
