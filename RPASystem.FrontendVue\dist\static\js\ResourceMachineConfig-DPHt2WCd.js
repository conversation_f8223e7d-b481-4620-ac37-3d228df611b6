import{_ as E,r as d,M as G,x as n,o as p,c as T,i as t,l as r,j as c,D as v,t as b,F as U,K as j,p as S,z as N,s as q,P as g,n as $}from"./index-D9f5ARRd.js";import{a as I,l as A,b as L}from"./SystemConfigManager-DIcLld2v.js";const O={class:"resource-machine-config"},H={class:"card-header"},J={class:"header-actions"},Q={class:"slider-value"},W=q({name:"resourcemachineconfig"}),X=Object.assign(W,{setup(Y){const _=d(null),o=d({maxConcurrentTasks:3,memoryThreshold:90,fileStorageServers:[]}),M={maxConcurrentTasks:[{required:!0,message:"请设置最大同时运行任务数量",trigger:"blur"}],memoryThreshold:[{required:!0,message:"请设置内存阈值百分比",trigger:"change"}]},h=d(!1),i=d(""),C=d(null),x=async()=>{try{const e=(await I("ResourceMachine")).data||[],m=(await I("FileStorage")).data||[];[...e,...m].forEach(l=>{l.configKey==="ResourceMachine.MaxConcurrentTasks"?o.value.maxConcurrentTasks=parseInt(l.configValue)||3:l.configKey==="ResourceMachine.MemoryThreshold"?o.value.memoryThreshold=parseInt(l.configValue)||90:l.configKey==="FileStorage.ServerIPs"&&(o.value.fileStorageServers=l.configValue.split(",").filter(y=>y.trim()!==""))})}catch(s){g.error("获取配置失败"),console.error("获取配置失败:",s)}},R=async()=>{if(_.value)try{await _.value.validate();const s=[{configKey:"ResourceMachine.MaxConcurrentTasks",configValue:o.value.maxConcurrentTasks.toString(),configGroup:"ResourceMachine",description:"资源机最大同时运行任务数量"},{configKey:"ResourceMachine.MemoryThreshold",configValue:o.value.memoryThreshold.toString(),configGroup:"ResourceMachine",description:"内存使用率阈值（百分比），超过此值将触发重启"},{configKey:"FileStorage.ServerIPs",configValue:o.value.fileStorageServers.join(","),configGroup:"FileStorage",description:"文件存储服务器IP列表，多个IP用逗号分隔"}],f=(await A()).data||[];s.forEach(m=>{const u=f.find(l=>l.configKey===m.configKey);u&&(m.ID=u.ID)}),await L({configs:s}),g.success("保存配置成功")}catch(s){g.error("保存配置失败"),console.error("保存配置失败:",s)}},w=()=>{x()},K=()=>{h.value=!0,$(()=>{var s;(s=C.value)==null||s.focus()})},k=()=>{i.value&&(F(i.value)?o.value.fileStorageServers.includes(i.value)?g.warning("该服务器IP已存在"):o.value.fileStorageServers.push(i.value):g.error("请输入有效的IP地址")),h.value=!1,i.value=""},P=s=>{o.value.fileStorageServers.splice(s,1)},F=s=>/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(s);return G(()=>{x()}),(s,e)=>{const f=n("el-button"),m=n("el-input-number"),u=n("el-form-item"),l=n("el-slider"),y=n("el-tag"),B=n("el-input"),z=n("el-form"),D=n("el-card");return p(),T("div",O,[t(D,{class:"config-card"},{header:r(()=>[c("div",H,[e[5]||(e[5]=c("span",null,"资源机配置",-1)),c("div",J,[t(f,{type:"primary",size:"small",onClick:R},{default:r(()=>e[3]||(e[3]=[v("保存配置")])),_:1}),t(f,{size:"small",onClick:w},{default:r(()=>e[4]||(e[4]=[v("重置")])),_:1})])])]),default:r(()=>[t(z,{model:o.value,"label-width":"200px",rules:M,ref_key:"formRef",ref:_},{default:r(()=>[t(u,{label:"最大同时运行任务数量",prop:"maxConcurrentTasks"},{default:r(()=>[t(m,{modelValue:o.value.maxConcurrentTasks,"onUpdate:modelValue":e[0]||(e[0]=a=>o.value.maxConcurrentTasks=a),min:1,max:20,style:{width:"180px"}},null,8,["modelValue"]),e[6]||(e[6]=c("div",{class:"form-item-help"},"设置资源机可同时运行的最大任务数量",-1))]),_:1}),t(u,{label:"内存阈值重启百分比",prop:"memoryThreshold"},{default:r(()=>[t(l,{modelValue:o.value.memoryThreshold,"onUpdate:modelValue":e[1]||(e[1]=a=>o.value.memoryThreshold=a),min:50,max:95,step:5,"show-stops":"",style:{width:"300px"}},{default:r(({modelValue:a})=>[c("div",Q,b(a)+"%",1)]),_:1},8,["modelValue"]),e[7]||(e[7]=c("div",{class:"form-item-help"},"当资源机内存使用率超过此阈值时将触发重启",-1))]),_:1}),t(u,{label:"文件存储服务器IP",prop:"fileStorageServers"},{default:r(()=>[(p(!0),T(U,null,j(o.value.fileStorageServers,(a,V)=>(p(),S(y,{key:V,class:"server-tag",closable:"",onClose:Z=>P(V)},{default:r(()=>[v(b(a),1)]),_:2},1032,["onClose"]))),128)),h.value?(p(),S(B,{key:0,ref_key:"serverInputRef",ref:C,modelValue:i.value,"onUpdate:modelValue":e[2]||(e[2]=a=>i.value=a),class:"server-input",size:"small",onKeyup:N(k,["enter"]),onBlur:k},null,8,["modelValue"])):(p(),S(f,{key:1,class:"button-new-tag",size:"small",onClick:K},{default:r(()=>e[8]||(e[8]=[v(" + 添加服务器 ")])),_:1})),e[9]||(e[9]=c("div",{class:"form-item-help"},"设置任务输出文件存储服务器IP地址，支持多个",-1))]),_:1})]),_:1},8,["model"])]),_:1})])}}}),se=E(X,[["__scopeId","data-v-bb20cdbc"]]);export{se as default};
