import{_ as b,r as c,w as x,M as V,x as u,o as a,p as d,l as i,i as _,c as f,F as h,K as g,Q as C}from"./index-D9f5ARRd.js";const R={name:"ResourceSelector",props:{modelValue:{type:String,default:""}},emits:["update:modelValue"],setup(p,{emit:s}){const n=c([]),l=c([]),t=c([]);x(()=>p.modelValue,e=>{e?t.value=e.split("|"):t.value=[]},{immediate:!0});const m=async()=>{try{const e=await C.get("/api/exeprogram/resources");n.value=e.data.resourcePools,l.value=e.data.resourceMachines}catch(e){console.error("获取资源列表失败:",e)}},r=e=>{s("update:modelValue",e.join("|"))};return V(()=>{m()}),{resourcePools:n,resourceMachines:l,selectedResources:t,handleChange:r}}};function k(p,s,n,l,t,m){const r=u("el-option"),e=u("el-option-group"),v=u("el-select");return a(),d(v,{modelValue:l.selectedResources,"onUpdate:modelValue":s[0]||(s[0]=o=>l.selectedResources=o),multiple:"",filterable:"",placeholder:"请选择资源",class:"resource-selector",onChange:l.handleChange},{default:i(()=>[_(e,{label:"资源池"},{default:i(()=>[(a(!0),f(h,null,g(l.resourcePools,o=>(a(),d(r,{key:"pool_"+o.id,label:o.poolName,value:o.poolName},null,8,["label","value"]))),128))]),_:1}),_(e,{label:"资源机"},{default:i(()=>[(a(!0),f(h,null,g(l.resourceMachines,o=>(a(),d(r,{key:"machine_"+o.id,label:o.machineName,value:o.machineName},null,8,["label","value"]))),128))]),_:1})]),_:1},8,["modelValue","onChange"])}const M=b(R,[["render",k],["__scopeId","data-v-b5264986"]]);export{M as default};
