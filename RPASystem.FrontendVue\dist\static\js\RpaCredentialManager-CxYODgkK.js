import{_ as z,r as u,e as L,M as N,x as d,o as R,c as j,j as m,i as a,l as t,D as p,z as K,t as P,s as Q,Q as _,P as f,H as A}from"./index-D9f5ARRd.js";const H={class:"credential-manager"},I={class:"operation-bar"},O={class:"left-section"},F={class:"right-section"},G={class:"dialog-footer"},J=Q({name:"rpacredentialmanager"}),W=Object.assign(J,{setup(X){const g=u([]),i=u(!1),n=u(!1),y=u(null),v=u(""),r=u({username:"",password:"",description:""}),x=L(()=>{if(!v.value)return g.value;const l=v.value.toLowerCase();return g.value.filter(e=>e.username.toLowerCase().includes(l))}),k={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密文",trigger:"blur"},{min:6,message:"密文长度不能小于6个字符",trigger:"blur"}]},b=async()=>{try{const l=await _.get("/api/rpacredential");g.value=l.data}catch(l){f.error("获取凭证列表失败"),console.error("获取凭证列表失败:",l)}},C=()=>{},h=()=>{n.value=!1,r.value={username:"",password:"",description:""},i.value=!0},B=l=>{n.value=!0,r.value={...l},i.value=!0},D=async l=>{try{await A.confirm("确定要删除该凭证吗？","提示",{type:"warning",confirmButtonText:"确定",cancelButtonText:"取消"}),await _.delete(`/api/rpacredential/${l.id}`),f.success("删除成功"),await b()}catch(e){e!=="cancel"&&(f.error("删除失败"),console.error("删除失败:",e))}},E=async()=>{if(y.value)try{await y.value.validate(),n.value?await _.put("/api/rpacredential",r.value):await _.post("/api/rpacredential",r.value),f.success(n.value?"更新成功":"创建成功"),i.value=!1,await b()}catch(l){l!=="cancel"&&(f.error(n.value?"更新失败":"创建失败"),console.error(n.value?"更新失败:":"创建失败:",l))}},M=(l,e,s)=>s?new Date(s).toLocaleString():"",T=l=>l?"••••••••":"";return N(async()=>{await b()}),(l,e)=>{const s=d("el-button"),w=d("el-input"),c=d("el-table-column"),U=d("el-table"),V=d("el-form-item"),S=d("el-form"),$=d("el-dialog");return R(),j("div",H,[m("div",I,[m("div",O,[a(w,{modelValue:v.value,"onUpdate:modelValue":e[0]||(e[0]=o=>v.value=o),placeholder:"请输入用户名搜索",style:{width:"300px"},clearable:"",onKeyup:K(C,["enter"])},{append:t(()=>[a(s,{onClick:C},{default:t(()=>e[6]||(e[6]=[p("搜索")])),_:1})]),_:1},8,["modelValue"])]),m("div",F,[a(s,{type:"primary",onClick:h},{default:t(()=>e[7]||(e[7]=[p("新增凭证")])),_:1})])]),a(U,{data:x.value,style:{width:"100%"}},{default:t(()=>[a(c,{prop:"username",label:"用户名",width:"180"}),a(c,{prop:"password",label:"密文"},{default:t(o=>[m("span",null,P(T(o.row.password)),1)]),_:1}),a(c,{prop:"description",label:"描述"}),a(c,{prop:"createdTime",label:"创建时间",width:"180",formatter:M}),a(c,{label:"操作",width:"180",fixed:"right"},{default:t(o=>[a(s,{size:"small",onClick:q=>B(o.row)},{default:t(()=>e[8]||(e[8]=[p("编辑")])),_:2},1032,["onClick"]),a(s,{size:"small",type:"danger",onClick:q=>D(o.row)},{default:t(()=>e[9]||(e[9]=[p("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),a($,{modelValue:i.value,"onUpdate:modelValue":e[5]||(e[5]=o=>i.value=o),title:n.value?"编辑凭证":"新增凭证",width:"500px","destroy-on-close":""},{footer:t(()=>[m("span",G,[a(s,{onClick:e[4]||(e[4]=o=>i.value=!1)},{default:t(()=>e[10]||(e[10]=[p("取消")])),_:1}),a(s,{type:"primary",onClick:E},{default:t(()=>e[11]||(e[11]=[p("确定")])),_:1})])]),default:t(()=>[a(S,{model:r.value,"label-width":"100px",rules:k,ref_key:"formRef",ref:y},{default:t(()=>[a(V,{label:"用户名",prop:"username"},{default:t(()=>[a(w,{modelValue:r.value.username,"onUpdate:modelValue":e[1]||(e[1]=o=>r.value.username=o),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),a(V,{label:"密文",prop:"password"},{default:t(()=>[a(w,{modelValue:r.value.password,"onUpdate:modelValue":e[2]||(e[2]=o=>r.value.password=o),type:"password",placeholder:"请输入密文","show-password":""},null,8,["modelValue"])]),_:1}),a(V,{label:"描述"},{default:t(()=>[a(w,{modelValue:r.value.description,"onUpdate:modelValue":e[3]||(e[3]=o=>r.value.description=o),type:"textarea",rows:2,placeholder:"请输入描述信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),Z=z(W,[["__scopeId","data-v-7d1c7765"]]);export{Z as default};
