import{Q as T,_ as K,e as Z,r as R,M as ee,x as c,o as p,p as g,l as n,i as o,D as x,j as y,t as h,c as S,k as F,W as te,q as V,X as oe,P as _,H as P}from"./index-D9f5ARRd.js";import{c as ae}from"./clipboard-yJaGoOU6.js";function ne(d){return T.get("/api/jobtask/subtasks",{params:d})}function le(d){return T.post(`/api/jobtask/retry/${d}`)}function re(d){return T.post(`/api/jobtask/stop/${d}`)}function se(d){return T.get(`/api/filestorage/${d}`,{responseType:"blob",headers:{Accept:"application/octet-stream"}})}const ie={key:1},ue=["onClick"],ce={key:1},pe=["onClick"],de={key:1},me={class:"clickable-text"},fe={key:1},we={class:"pagination-container"},ge={__name:"SubTaskDialog",props:{parentTaskId:{type:Number,required:!0},parentTaskName:{type:String,required:!0},visible:{type:Boolean,required:!0}},emits:["update:visible"],setup(d,{emit:D}){const N=d,j=D,M=Z({get:()=>N.visible,set:a=>j("update:visible",a)}),f=R({resourceMachine:"",status:[]}),i=R({pageNumber:1,pageSize:100,total:0}),$=R([]),b=async()=>{try{const a={parentTaskId:N.parentTaskId,resourceMachine:f.value.resourceMachine,status:f.value.status,pageNumber:i.value.pageNumber,pageSize:i.value.pageSize},t=await ne(a);$.value=t.data.items,i.value.total=t.data.totalCount}catch(a){console.error("获取子任务列表失败:",a),_.error("获取子任务列表失败")}},I=()=>{i.value.pageNumber=1,b()},B=()=>{f.value={resourceMachine:"",status:[]},I()},U=a=>{i.value.pageSize=a,i.value.pageNumber=1,b()},J=a=>{i.value.pageNumber=a,b()},O=async a=>{try{await P.confirm("确定要重试该任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await le(a),_.success("已开始重试任务"),await b()}catch(t){t!=="cancel"&&(console.error("重试任务失败:",t),_.error("重试任务失败"))}},q=async a=>{try{await P.confirm("确定要停止该任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await re(a),_.success("已发送停止指令"),await b()}catch(t){t!=="cancel"&&(console.error("停止任务失败:",t),_.error("停止任务失败"))}},E=a=>({Pending:"info",Running:"primary",Success:"success",Failed:"danger",Cancelled:"warning"})[a]||"info",z=(a,t)=>{if(!a||!t)return"";const k=new Date(a),w=new Date(t),s=Math.floor((w-k)/1e3);if(s<0)return"";const u=Math.floor(s/(24*3600)),r=Math.floor(s%(24*3600)/3600),m=Math.floor(s%3600/60),l=s%60;return u===0&&r===0&&m===0?`${l}秒`:u===0&&r===0?m===0?`${l}秒`:`${m}分${l}秒`:u===0?`${r}时${m}分${l}秒`:`${u}天${r}时${m}分${l}秒`},L=a=>{try{const t=JSON.parse(a||"{}");return t.InputFile&&t.InputFile!==""}catch{return!1}},A=async a=>{try{const t=JSON.parse(a||"{}");if(t.InputFile){const k=t.InputFile,w=await se(k),s=window.URL.createObjectURL(new Blob([w.data])),u=document.createElement("a");u.href=s;const r=new Date,m=r.getFullYear()+String(r.getMonth()+1).padStart(2,"0")+String(r.getDate()).padStart(2,"0")+String(r.getHours()).padStart(2,"0")+String(r.getMinutes()).padStart(2,"0")+String(r.getSeconds()).padStart(2,"0")+".xlsx";u.setAttribute("download",m),document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(s)}}catch(t){console.error("下载文件失败:",t),_.error("下载文件失败，请重试")}},H=a=>{try{return JSON.parse(a||"{}").ReturnResult!==void 0}catch{return!1}},Q=a=>{try{const t=JSON.parse(a||"{}");t.ReturnResult!==void 0&&ae(t.ReturnResult)}catch(t){console.error("解析输出结果失败:",t),_.error("解析输出结果失败")}};return ee(()=>{b()}),(a,t)=>{const k=c("el-input"),w=c("el-form-item"),s=c("el-option"),u=c("el-select"),r=c("el-button"),m=c("el-form"),l=c("el-table-column"),v=c("el-tooltip"),W=c("el-tag"),X=c("el-table"),Y=c("el-pagination"),G=c("el-dialog");return p(),g(G,{modelValue:M.value,"onUpdate:modelValue":t[4]||(t[4]=e=>M.value=e),title:"子任务列表 - "+d.parentTaskName,width:"90%","destroy-on-close":""},{default:n(()=>[o(m,{inline:!0,model:f.value,class:"search-form"},{default:n(()=>[o(w,{label:"资源机"},{default:n(()=>[o(k,{modelValue:f.value.resourceMachine,"onUpdate:modelValue":t[0]||(t[0]=e=>f.value.resourceMachine=e),placeholder:"请输入资源机",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),o(w,{label:"状态"},{default:n(()=>[o(u,{modelValue:f.value.status,"onUpdate:modelValue":t[1]||(t[1]=e=>f.value.status=e),multiple:"",placeholder:"请选择状态",clearable:"",style:{width:"300px"}},{default:n(()=>[o(s,{label:"待运行",value:"Pending"}),o(s,{label:"运行中",value:"Running"}),o(s,{label:"成功",value:"Success"}),o(s,{label:"失败",value:"Failed"}),o(s,{label:"已取消",value:"Cancelled"})]),_:1},8,["modelValue"])]),_:1}),o(w,null,{default:n(()=>[o(r,{type:"primary",onClick:I},{default:n(()=>t[5]||(t[5]=[x("搜索")])),_:1}),o(r,{onClick:B},{default:n(()=>t[6]||(t[6]=[x("重置")])),_:1})]),_:1})]),_:1},8,["model"]),o(X,{data:$.value,style:{width:"100%"}},{default:n(()=>[o(l,{prop:"jobTaskId",label:"任务ID",width:"120"}),o(l,{prop:"jobTaskName",label:"任务名称","min-width":"120","show-overflow-tooltip":""}),o(l,{prop:"exeProgramName",label:"程序名称","min-width":"150","show-overflow-tooltip":""}),o(l,{prop:"priority",label:"优先级",width:"70",align:"center"}),o(l,{prop:"createdAt",label:"创建时间",width:"85","show-overflow-tooltip":""}),o(l,{prop:"startTime",label:"开始时间",width:"85","show-overflow-tooltip":""}),o(l,{label:"运行时长",width:"90","show-overflow-tooltip":""},{default:n(e=>[e.row.endTime?(p(),g(v,{key:0,content:`结束时间: ${e.row.endTime}`,placement:"top",effect:"light"},{default:n(()=>[y("span",null,h(z(e.row.startTime,e.row.endTime)),1)]),_:2},1032,["content"])):(p(),S("span",ie,h(z(e.row.startTime,e.row.endTime)),1))]),_:1}),o(l,{prop:"resourceSelection",label:"资源选择",width:"100","show-overflow-tooltip":""}),o(l,{prop:"assignedResourceMachine",label:"资源机",width:"120","show-overflow-tooltip":""}),o(l,{prop:"inputParameters",label:"输入参数",width:"100","show-overflow-tooltip":""},{default:n(e=>[L(e.row.inputParameters)?(p(),g(v,{key:0,content:e.row.inputParameters,placement:"top",effect:"light"},{default:n(()=>[y("span",{class:"clickable-text",onClick:C=>A(e.row.inputParameters)}," 下载输入件 ",8,ue)]),_:2},1032,["content"])):(p(),S("span",ce,h(e.row.inputParameters),1))]),_:1}),o(l,{prop:"outputResults",label:"输出结果",width:"80","show-overflow-tooltip":""},{default:n(e=>[H(e.row.outputResults)?(p(),g(v,{key:0,content:e.row.outputResults,placement:"top",effect:"light"},{default:n(()=>[y("span",{class:"clickable-text",onClick:C=>Q(e.row.outputResults)}," 复制结果 ",8,pe)]),_:2},1032,["content"])):(p(),S("span",de,h(e.row.outputResults),1))]),_:1}),o(l,{prop:"outputFile",label:"输出文件",width:"80","show-overflow-tooltip":""},{default:n(e=>[e.row.outputFile?(p(),g(v,{key:0,content:e.row.outputFile,placement:"top",effect:"light"},{default:n(()=>[y("span",me,h(e.row.outputFile),1)]),_:2},1032,["content"])):(p(),S("span",fe,"-"))]),_:1}),o(l,{prop:"retryCount",label:"重试次数",width:"80",align:"center"},{default:n(e=>[y("span",null,h(e.row.retryCount||0),1)]),_:1}),o(l,{prop:"status",label:"状态",width:"100"},{default:n(e=>[o(W,{type:E(e.row.status)},{default:n(()=>[x(h(e.row.status),1)]),_:2},1032,["type"])]),_:1}),o(l,{prop:"notes",label:"备注","min-width":"100","show-overflow-tooltip":""}),o(l,{label:"操作",width:"120",fixed:"right"},{default:n(e=>[o(v,{content:"重试",placement:"top",effect:"light"},{default:n(()=>[e.row.status!=="Pending"&&e.row.status!=="Running"?(p(),g(r,{key:0,type:"primary",icon:F(te),circle:"",size:"small",onClick:C=>O(e.row.jobTaskId)},null,8,["icon","onClick"])):V("",!0)]),_:2},1024),o(v,{content:"停止",placement:"top",effect:"light"},{default:n(()=>[e.row.status==="Running"||e.row.status==="Pending"?(p(),g(r,{key:0,type:"warning",icon:F(oe),circle:"",size:"small",onClick:C=>q(e.row.jobTaskId)},null,8,["icon","onClick"])):V("",!0)]),_:2},1024)]),_:1})]),_:1},8,["data"]),y("div",we,[o(Y,{"current-page":i.value.pageNumber,"onUpdate:currentPage":t[2]||(t[2]=e=>i.value.pageNumber=e),"page-size":i.value.pageSize,"onUpdate:pageSize":t[3]||(t[3]=e=>i.value.pageSize=e),"page-sizes":[50,100,200],total:i.value.total,layout:"total, sizes, prev, pager, next, jumper",background:!0,onSizeChange:U,onCurrentChange:J},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue","title"])}}},be=K(ge,[["__scopeId","data-v-3f67da11"]]);export{be as default};
