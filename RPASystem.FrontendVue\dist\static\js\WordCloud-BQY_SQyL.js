import{i as c}from"./index-BtiuLXK9.js";import{r as l,aL as d,M as u,aa as m,o as h,c as f,V as p,B as y,v as g}from"./index-D9f5ARRd.js";const w={__name:"WordCloud",props:{name:{type:String,default:""},min:{type:[Object,Number],default:0},max:{type:[Object,Number],default:0},data:{type:Array,default:()=>[{}]},className:{type:String,default:"chart"},width:{type:String,default:"100%"},height:{type:String,default:"300px"}},setup(a){const{proxy:n}=g(),e=a;let t=null;const o=l(),s=()=>{t.resize()};function i(){t=c(n.$refs.chartRef,"macarons"),t.setOption({tooltip:{formatter:"{a} <br/>{b} : {c}"},series:[{type:"wordCloud",shape:"circle",gridSize:1,sizeRange:[12,55],rotationRange:[-45,0,45,90],textStyle:{normal:{color:function(){return"rgb("+Math.round(Math.random()*255)+", "+Math.round(Math.random()*255)+", "+Math.round(Math.random()*255)+")"}}},left:"center",top:"center",right:null,bottom:null,data:e.data}]})}const r=d(()=>{s()},500);return document.addEventListener("resize",r),u(()=>{i()}),m(()=>{window.removeEventListener("resize",r)}),(b,z)=>(h(),f("div",{ref_key:"chartRef",ref:o,class:p(a.className),style:y({height:e.height,width:e.width})},null,6))}};export{w as default};
