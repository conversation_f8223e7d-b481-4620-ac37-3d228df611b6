import{r as d,E as J,a5 as Ue,x as u,N as W,o as i,c as N,O as _,S as P,k as n,i as e,l,D as y,F as X,K as Y,p as v,t as g,A as Re,m as Z,q as O,j,s as Ae,v as Be,n as qe}from"./index-D9f5ARRd.js";import{t as De,d as Le,g as Ee,u as Fe,a as Pe,e as Oe,c as je}from"./articlecategory-Bm4F1q31.js";import{I as ze}from"./index-B8u0KKcX.js";import"./requireIcons-CAZP3y3p.js";const Qe={class:"app-container"},Ke=["onClick"],Me={key:0},Ge={class:"dialog-footer"},He=<PERSON><PERSON>({name:"articlecategory"}),et=Object.assign(He,{setup(Je){const{proxy:s}=Be(),U=d(!1),R=d(!0);function ee(){R.value=!1,U.value=!U.value,qe(()=>{R.value=!0})}const A=d([]),te=d(!0),z=d(!0),B=d(!1),k=d(!0),c=J({pageNum:1,pageSize:10,sort:void 0,sortType:void 0,categoryType:""}),q=d(""),D=d(0),b=d(!1),le=J({form:{},rules:{name:[{required:!0,message:"目录名不能为空",trigger:"blur"}],categoryType:[{required:!0,message:"目录分类不能为空",trigger:"blur"}]}}),{form:r,rules:oe}=Ue(le);d(0);const L=d([]),ae=d(),ne=d();function I(){B.value=!0,De(c).then(o=>{o.code==200&&(L.value=o.data,B.value=!1)})}function re(){b.value=!1,E()}function E(){r.value={name:void 0,parentId:0,icon:"",orderNum:0,categoryType:void 0},s.resetForm("formRef")}function V(){c.pageNum=1,I()}function ue(){E(),b.value=!0,q.value="添加",D.value=1,c.categoryType&&(r.value.categoryType=parseInt(c.categoryType))}function Q(o){const a=o.categoryId||A.value;s.$confirm('是否确认删除参数编号为"'+a+'"的数据项？').then(function(){return Le(a)}).then(()=>{V(),s.$modal.msgSuccess("删除成功")}).catch(()=>{})}function de(o){E();const a=o.categoryId||A.value;Ee(a).then($=>{const{code:F,data:f}=$;F==200&&(b.value=!0,q.value="修改数据",D.value=2,r.value={...f})})}function ie(){s.$refs.formRef.validate(o=>{o&&(r.value.categoryId!=null&&D.value===2?Fe(r.value).then(a=>{s.$modal.msgSuccess("修改成功"),b.value=!1,I()}).catch(()=>{}):Pe(r.value).then(a=>{s.$modal.msgSuccess("新增成功"),b.value=!1,I()}).catch(a=>{}))})}function ce(){s.$confirm("是否确认导出所有文章目录数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return Oe(c)}).then(o=>{s.download(o.data.path)})}function se(o){A.value=o.map(a=>a.categoryId),te.value=o.length!=1,z.value=!o.length}function pe(o){o.prop==null||o.order==null?(c.sort=void 0,c.sortType=void 0):(c.sort=o.prop,c.sortType=o.order),V()}function me(o){r.value.icon=o,document.body.click()}const K=d([]),fe=o=>{o&&K.value.push(o)},T=d(-1);function ge(o){T.value=o,setTimeout(()=>{K.value[o].focus()},100)}function _e(o){T.value=-1,s.$confirm("是否保存数据?").then(function(){return je({value:o.orderNum,id:o.categoryId})}).then(()=>{V(),s.$modal.msgSuccess("修改成功")}).catch(()=>{V()})}const S=d([]);return s.getDicts("article_category_type").then(o=>{S.value=o.data}),V(),(o,a)=>{const $=u("el-radio-button"),F=u("el-radio-group"),f=u("el-form-item"),M=u("el-form"),h=u("el-button"),p=u("el-col"),ye=u("right-toolbar"),G=u("el-row"),m=u("el-table-column"),H=u("svg-icon"),ve=u("image-preview"),be=u("dict-tag"),x=u("el-input"),he=u("el-table"),we=u("el-cascader"),Ve=u("el-option"),Ce=u("el-select"),ke=u("search"),Ie=u("el-icon"),Te=u("el-popover"),Se=u("el-input-number"),$e=u("UploadImage"),xe=u("el-dialog"),C=W("hasPermi"),Ne=W("loading");return i(),N("div",Qe,[_(e(M,{model:n(c),"label-position":"right",inline:"",ref_key:"queryRef",ref:ae,onSubmit:a[2]||(a[2]=Re(()=>{},["prevent"]))},{default:l(()=>[e(f,null,{default:l(()=>[e(F,{modelValue:n(c).categoryType,"onUpdate:modelValue":a[0]||(a[0]=t=>n(c).categoryType=t),onChange:a[1]||(a[1]=t=>V())},{default:l(()=>[e($,{value:""},{default:l(()=>a[12]||(a[12]=[y("全部")])),_:1}),(i(!0),N(X,null,Y(n(S),t=>(i(),v($,{key:t.dictValue,value:t.dictValue},{default:l(()=>[y(g(t.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"]),[[P,n(k)]]),e(G,{gutter:10,class:"mb8"},{default:l(()=>[e(p,{span:1.5},{default:l(()=>[_((i(),v(h,{type:"primary",plain:"",icon:"plus",onClick:ue},{default:l(()=>[y(g(o.$t("btn.add")),1)]),_:1})),[[C,["articlecategory:add"]]])]),_:1}),e(p,{span:1.5},{default:l(()=>[e(h,{type:"info",plain:"",icon:"sort",onClick:ee},{default:l(()=>a[13]||(a[13]=[y("展开/折叠")])),_:1})]),_:1}),e(p,{span:1.5},{default:l(()=>[_((i(),v(h,{type:"danger",disabled:n(z),plain:"",icon:"delete",onClick:Q},{default:l(()=>[y(g(o.$t("btn.delete")),1)]),_:1},8,["disabled"])),[[C,["articlecategory:delete"]]])]),_:1}),e(p,{span:1.5},{default:l(()=>[_((i(),v(h,{type:"warning",plain:"",icon:"download",onClick:ce},{default:l(()=>[y(g(o.$t("btn.export")),1)]),_:1})),[[C,["articlecategory:export"]]])]),_:1}),e(ye,{showSearch:n(k),"onUpdate:showSearch":a[3]||(a[3]=t=>Z(k)?k.value=t:null),onQueryTable:I},null,8,["showSearch"])]),_:1}),n(R)?_((i(),v(he,{key:0,data:n(L),ref:"tableRef",border:"","highlight-current-row":"",onSortChange:pe,onSelectionChange:se,"default-expand-all":n(U),"row-key":"categoryId","tree-props":{children:"children",hasChildren:"hasChildren"}},{default:l(()=>[e(m,{type:"selection",width:"50"}),e(m,{prop:"name",label:"目录名","show-overflow-tooltip":!0}),e(m,{prop:"icon",label:"图标","show-overflow-tooltip":!0},{default:l(({row:t})=>[t.icon?(i(),v(H,{key:0,name:t.icon},null,8,["name"])):O("",!0),y(" "+g(t.icon),1)]),_:1}),e(m,{prop:"bgImg",label:"背景","show-overflow-tooltip":!0},{default:l(({row:t})=>[e(ve,{src:t.bgImg,split:","},null,8,["src"])]),_:1}),e(m,{prop:"categoryType",label:"分类",align:"center"},{default:l(({row:t})=>[e(be,{options:n(S),value:t.categoryType},null,8,["options","value"])]),_:1}),e(m,{prop:"categoryId",label:"目录id",sortable:"",align:"center"}),e(m,{prop:"orderNum",label:"排序",sortable:"",align:"center"},{default:l(t=>[_(j("span",{onClick:w=>ge(t.row.categoryId)},g(t.row.orderNum),9,Ke),[[P,n(T)!=t.row.categoryId]]),_(e(x,{ref:fe,modelValue:t.row.orderNum,"onUpdate:modelValue":w=>t.row.orderNum=w,onBlur:w=>_e(t.row)},null,8,["modelValue","onUpdate:modelValue","onBlur"]),[[P,n(T)==t.row.categoryId]])]),_:1}),e(m,{prop:"introduce",label:"介绍","show-overflow-tooltip":!0}),e(m,{prop:"createTime",label:"添加时间",align:"center","show-overflow-tooltip":!0}),e(m,{prop:"parentId",label:"父级id",align:"center"}),e(m,{label:"操作",align:"center",width:"140"},{default:l(t=>[_(e(h,{type:"success",icon:"edit",title:"编辑",onClick:w=>de(t.row)},null,8,["onClick"]),[[C,["articlecategory:edit"]]]),_(e(h,{type:"danger",icon:"delete",title:"删除",onClick:w=>Q(t.row)},null,8,["onClick"]),[[C,["articlecategory:delete"]]])]),_:1})]),_:1},8,["data","default-expand-all"])),[[Ne,n(B)]]):O("",!0),e(xe,{title:n(q),"lock-scroll":!1,modelValue:n(b),"onUpdate:modelValue":a[11]||(a[11]=t=>Z(b)?b.value=t:null),width:"550px"},{footer:l(()=>[j("div",Ge,[e(h,{text:"",onClick:re},{default:l(()=>[y(g(o.$t("btn.cancel")),1)]),_:1}),e(h,{type:"primary",onClick:ie},{default:l(()=>[y(g(o.$t("btn.submit")),1)]),_:1})])]),default:l(()=>[e(M,{ref_key:"formRef",ref:ne,model:n(r),rules:n(oe),"label-width":"80px"},{default:l(()=>[e(G,{gutter:20},{default:l(()=>[e(p,{lg:24},{default:l(()=>[e(f,{label:"父级id",prop:"parentId"},{default:l(()=>[e(we,{class:"w100",options:n(L),props:{checkStrictly:!0,value:"categoryId",label:"name",emitPath:!1},placeholder:"请选择上级菜单",clearable:"",modelValue:n(r).parentId,"onUpdate:modelValue":a[4]||(a[4]=t=>n(r).parentId=t)},{default:l(({node:t,data:w})=>[j("span",null,g(w.name),1),t.isLeaf?O("",!0):(i(),N("span",Me," ("+g(w.children.length)+") ",1))]),_:1},8,["options","modelValue"])]),_:1})]),_:1}),e(p,{lg:24},{default:l(()=>[e(f,{label:"目录分类",prop:"categoryType"},{default:l(()=>[e(Ce,{modelValue:n(r).categoryType,"onUpdate:modelValue":a[5]||(a[5]=t=>n(r).categoryType=t),placeholder:"请选择分类",clearable:""},{default:l(()=>[(i(!0),N(X,null,Y(n(S),t=>(i(),v(Ve,{key:t.dictValue,label:t.dictLabel,value:parseInt(t.dictValue)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(p,{lg:24},{default:l(()=>[e(f,{label:"目录名",prop:"name"},{default:l(()=>[e(x,{modelValue:n(r).name,"onUpdate:modelValue":a[6]||(a[6]=t=>n(r).name=t),placeholder:"请输入目录名"},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{lg:24},{default:l(()=>[e(f,{label:"图标",prop:"icon"},{default:l(()=>[e(Te,{placement:"bottom",width:540,trigger:"click"},{reference:l(()=>[e(x,{modelValue:n(r).icon,"onUpdate:modelValue":a[7]||(a[7]=t=>n(r).icon=t),placeholder:"点击选择图标",readonly:""},{prefix:l(()=>[n(r).icon?(i(),v(H,{key:0,name:n(r).icon},null,8,["name"])):(i(),v(Ie,{key:1},{default:l(()=>[e(ke)]),_:1}))]),_:1},8,["modelValue"])]),default:l(()=>[e(n(ze),{ref:"iconSelectRef",onSelected:me},null,512)]),_:1})]),_:1})]),_:1}),e(p,{lg:24},{default:l(()=>[e(f,{label:"排序",prop:"orderNum"},{default:l(()=>[e(Se,{modelValue:n(r).orderNum,"onUpdate:modelValue":a[8]||(a[8]=t=>n(r).orderNum=t),placeholder:"请输入排序值"},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{lg:24},{default:l(()=>[e(f,{label:"介绍",prop:"introduce"},{default:l(()=>[e(x,{modelValue:n(r).introduce,"onUpdate:modelValue":a[9]||(a[9]=t=>n(r).introduce=t),placeholder:"请输入介绍"},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{lg:24},{default:l(()=>[e(f,{label:"背景图",prop:"bgImg"},{default:l(()=>[e($e,{ref:"uploadRef",modelValue:n(r).bgImg,"onUpdate:modelValue":a[10]||(a[10]=t=>n(r).bgImg=t),limit:1,fileSize:15},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{et as default};
