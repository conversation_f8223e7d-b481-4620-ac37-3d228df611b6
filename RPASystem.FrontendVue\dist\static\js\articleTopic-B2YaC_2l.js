import{r,E as j,a5 as ne,x as u,N as z,o as d,c as ae,O as b,S as ie,k as t,i as o,l,D as h,t as w,A as re,p as f,m as O,q as C,a6 as se,s as ue,v as ce}from"./index-D9f5ARRd.js";import{l as pe,g as de,u as me,a as fe,d as ge}from"./articletopic-_-hN04re.js";const ve=ue({name:"articletopic"}),Ce=Object.assign(ve,{setup(_e){const{proxy:s}=ce(),R=r([]),I=r(!1),T=r(!0),c=j({pageNum:1,pageSize:10,sort:"topicid",sortType:"desc"}),g=r([{visible:!0,align:"left",type:"",prop:"topicId",label:"话题ID"},{visible:!0,align:"left",type:"",prop:"topicName",label:"话题名",showOverflowTooltip:!0},{visible:!0,align:"left",type:"",prop:"topicDescription",label:"话题描述",showOverflowTooltip:!0},{visible:!0,align:"left",type:"",prop:"joinNum",label:"参与次数"},{visible:!0,align:"left",type:"",prop:"addTime",label:"创建时间",showOverflowTooltip:!0}]),A=r(0),q=r([]),F=r();r([new Date(2e3,1,1,0,0,0),new Date(2e3,2,1,23,59,59)]);function v(){I.value=!0,pe(c).then(n=>{const{code:e,data:a}=n;e==200&&(q.value=a.result,A.value=a.totalNum,I.value=!1)})}function N(){c.pageNum=1,v()}function P(){s.resetForm("queryRef"),N()}function E(n){var e=void 0,a=void 0;n.prop!=null&&n.order!=null&&(e=n.prop,a=n.order),c.sort=e,c.sortType=a,N()}const Q=r(),V=r(""),y=r(0),m=r(!1),L=j({single:!0,multiple:!0,form:{},rules:{topicId:[{required:!0,message:"话题ID不能为空",trigger:"blur",type:"number"}],topicName:[{required:!0,message:"话题名不能为空",trigger:"blur"}]},options:{}}),{form:p,rules:M,options:be,single:he,multiple:we}=ne(L);function G(){m.value=!1,x()}function x(){p.value={topicId:null,topicName:null,topicDescription:null,joinNum:null,addTime:null},s.resetForm("formRef")}function H(){x(),m.value=!0,V.value="添加文章话题",y.value=1}function J(n){x();const e=n.topicId||R.value;de(e).then(a=>{const{code:k,data:D}=a;k==200&&(m.value=!0,V.value="修改文章话题",y.value=2,p.value={...D})})}function K(){s.$refs.formRef.validate(n=>{n&&(p.value.topicId!=null&&y.value===2?me(p.value).then(e=>{s.$modal.msgSuccess("修改成功"),m.value=!1,v()}):fe(p.value).then(e=>{s.$modal.msgSuccess("新增成功"),m.value=!1,v()}))})}function W(n){const e=n.topicId||R.value;s.$confirm('是否确认删除参数编号为"'+e+'"的数据项？',"警告",{confirmButtonText:s.$t("common.ok"),cancelButtonText:s.$t("common.cancel"),type:"warning"}).then(function(){return ge(e)}).then(()=>{v(),s.$modal.msgSuccess("删除成功")})}function X(){s.$confirm("是否确认导出文章话题数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await s.downFile("/article/ArticleTopic/export",{...c})})}return N(),(n,e)=>{const a=u("el-button"),k=u("el-form-item"),D=u("el-form"),S=u("el-col"),Y=u("right-toolbar"),B=u("el-row"),_=u("el-table-column"),Z=u("el-table"),ee=u("pagination"),U=u("el-input"),te=u("el-dialog"),$=z("hasPermi"),oe=z("loading");return d(),ae("div",null,[b(o(D,{model:t(c),"label-position":"right",inline:"",ref_key:"queryRef",ref:F,onSubmit:e[0]||(e[0]=re(()=>{},["prevent"]))},{default:l(()=>[o(k,null,{default:l(()=>[o(a,{icon:"search",type:"primary",onClick:N},{default:l(()=>[h(w(n.$t("btn.search")),1)]),_:1}),o(a,{icon:"refresh",onClick:P},{default:l(()=>[h(w(n.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[ie,t(T)]]),o(B,{gutter:15,class:"mb10"},{default:l(()=>[o(S,{span:1.5},{default:l(()=>[b((d(),f(a,{type:"primary",plain:"",icon:"plus",onClick:H},{default:l(()=>[h(w(n.$t("btn.add")),1)]),_:1})),[[$,["articletopic:add"]]])]),_:1}),o(S,{span:1.5},{default:l(()=>[b((d(),f(a,{type:"warning",plain:"",icon:"download",onClick:X},{default:l(()=>[h(w(n.$t("btn.export")),1)]),_:1})),[[$,["articletopic:export"]]])]),_:1}),o(Y,{showSearch:t(T),"onUpdate:showSearch":e[1]||(e[1]=i=>O(T)?T.value=i:null),onQueryTable:v,columns:t(g)},null,8,["showSearch","columns"])]),_:1}),b((d(),f(Z,{data:t(q),ref:"table",border:"","header-cell-class-name":"el-table-header-cell","highlight-current-row":"",onSortChange:E},{default:l(()=>[t(g).showColumn("topicId")?(d(),f(_,{key:0,prop:"topicId",label:"话题ID",align:"center"})):C("",!0),t(g).showColumn("topicName")?(d(),f(_,{key:1,prop:"topicName",label:"话题名",align:"center","show-overflow-tooltip":!0})):C("",!0),t(g).showColumn("topicDescription")?(d(),f(_,{key:2,prop:"topicDescription",label:"话题描述",align:"center","show-overflow-tooltip":!0})):C("",!0),t(g).showColumn("joinNum")?(d(),f(_,{key:3,prop:"joinNum",label:"参与次数",align:"center"})):C("",!0),t(g).showColumn("addTime")?(d(),f(_,{key:4,prop:"addTime",label:"创建时间","show-overflow-tooltip":!0})):C("",!0),o(_,{label:"操作",width:"160"},{default:l(i=>[b(o(a,{type:"success",size:"small",icon:"edit",title:"编辑",onClick:le=>J(i.row)},null,8,["onClick"]),[[$,["articletopic:edit"]]]),b(o(a,{type:"danger",size:"small",icon:"delete",title:"删除",onClick:le=>W(i.row)},null,8,["onClick"]),[[$,["articletopic:delete"]]])]),_:1})]),_:1},8,["data"])),[[oe,t(I)]]),o(ee,{total:t(A),page:t(c).pageNum,"onUpdate:page":e[2]||(e[2]=i=>t(c).pageNum=i),limit:t(c).pageSize,"onUpdate:limit":e[3]||(e[3]=i=>t(c).pageSize=i),onPagination:v},null,8,["total","page","limit"]),o(te,{title:t(V),"lock-scroll":!1,modelValue:t(m),"onUpdate:modelValue":e[6]||(e[6]=i=>O(m)?m.value=i:null),width:"400"},se({default:l(()=>[o(D,{ref_key:"formRef",ref:Q,model:t(p),rules:t(M),"label-width":"90px"},{default:l(()=>[o(B,{gutter:20},{default:l(()=>[o(S,{lg:24},{default:l(()=>[o(k,{label:"话题名",prop:"topicName"},{default:l(()=>[o(U,{modelValue:t(p).topicName,"onUpdate:modelValue":e[4]||(e[4]=i=>t(p).topicName=i),placeholder:"请输入话题名",disabled:t(y)==2},null,8,["modelValue","disabled"])]),_:1})]),_:1}),o(S,{lg:24},{default:l(()=>[o(k,{label:"话题描述",prop:"topicDescription"},{default:l(()=>[o(U,{type:"textarea",modelValue:t(p).topicDescription,"onUpdate:modelValue":e[5]||(e[5]=i=>t(p).topicDescription=i),placeholder:"请输入话题描述"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:2},[t(y)!=3?{name:"footer",fn:l(()=>[o(a,{text:"",onClick:G},{default:l(()=>[h(w(n.$t("btn.cancel")),1)]),_:1}),o(a,{type:"primary",onClick:K},{default:l(()=>[h(w(n.$t("btn.submit")),1)]),_:1})]),key:"0"}:void 0]),1032,["title","modelValue"])])}}});export{Ce as default};
