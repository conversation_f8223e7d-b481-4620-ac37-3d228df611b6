import{E as s,x as m,o as b,p as c,l,i as e,k as g,s as p}from"./index-D9f5ARRd.js";const V=p({name:"BasicInfoForm"}),k=Object.assign(V,{props:{info:{type:Object,default:null}},setup(o){const d=s({tableName:[{required:!0,message:"请输入表名称",trigger:"blur"}],tableComment:[{required:!0,message:"请输入表描述",trigger:"blur"}],className:[{required:!0,message:"请输入实体类名称",trigger:"blur"}],functionAuthor:[{required:!0,message:"请输入作者",trigger:"blur"}]});return(N,t)=>{const n=m("el-input"),u=m("el-form-item"),r=m("el-col"),f=m("el-row"),i=m("el-form");return b(),c(i,{ref:"basicInfoForm",model:o.info,rules:g(d),"label-width":"100px"},{default:l(()=>[e(f,null,{default:l(()=>[e(r,{lg:6},{default:l(()=>[e(u,{label:"表名称",prop:"tableName"},{default:l(()=>[e(n,{placeholder:"请输入仓库名称",modelValue:o.info.tableName,"onUpdate:modelValue":t[0]||(t[0]=a=>o.info.tableName=a)},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{lg:6},{default:l(()=>[e(u,{label:"表描述",prop:"tableComment"},{default:l(()=>[e(n,{placeholder:"请输入",modelValue:o.info.tableComment,"onUpdate:modelValue":t[1]||(t[1]=a=>o.info.tableComment=a)},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{lg:6},{default:l(()=>[e(u,{label:"实体类名称",prop:"className"},{default:l(()=>[e(n,{placeholder:"请输入",modelValue:o.info.className,"onUpdate:modelValue":t[2]||(t[2]=a=>o.info.className=a)},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{lg:6},{default:l(()=>[e(u,{label:"作者",prop:"functionAuthor"},{default:l(()=>[e(n,{placeholder:"请输入",modelValue:o.info.functionAuthor,"onUpdate:modelValue":t[3]||(t[3]=a=>o.info.functionAuthor=a)},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{lg:24},{default:l(()=>[e(u,{label:"备注",prop:"remark"},{default:l(()=>[e(n,{type:"textarea",rows:3,modelValue:o.info.remark,"onUpdate:modelValue":t[4]||(t[4]=a=>o.info.remark=a)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])}}});export{k as default};
