import{w as ge,r as m,E,a5 as be,ax as ve,x as d,N as _e,o as s,c as v,i as l,l as a,k as o,F as x,K as w,p as V,D as k,O as S,V as L,t as D,q as ye,S as Ve,j as q,m as Ce,s as xe,v as ke,ay as we,az as Te,aA as Ue,aB as Se,aC as Le}from"./index-D9f5ARRd.js";import{g as De,l as Ne}from"./type-BoO7AbZI.js";const $e={key:0},Be={style:{float:"right"}},ze={class:"dialog-footer"},he=xe({name:"dictData"}),Ke=Object.assign(he,{props:{dictId:{type:Number,default:0}},setup(Q){const{proxy:c}=ke(),Z=Q;ge(()=>Z.dictId,(u,t)=>{u&&(X(u),Y(),c.getDicts("sys_normal_disable").then(g=>{B.value=g.data}))},{immediate:!0,deep:!0});const F=m(),K=m(!1),N=m(0),O=m([]),R=m(""),$=m(""),f=m(!1),G=m([{value:"default",label:"默认"},{value:"primary",label:"主要"},{value:"success",label:"成功"},{value:"info",label:"信息"},{value:"warning",label:"警告"},{value:"danger",label:"危险"}]),H=m([{value:"text-primary",label:"主要"},{value:"text-success",label:"成功"},{value:"text-info",label:"信息"},{value:"text-warning",label:"警告"},{value:"text-danger",label:"危险"},{value:"text-orange",label:"橘红色"},{value:"text-hotpink",label:"粉红色"},{value:"text-green",label:"绿色"},{value:"text-greenyellow",label:"黄绿色"},{value:"text-purple",label:"紫色"}]),B=m([]),I=m([]),r=E({pageNum:1,pageSize:10,dictName:void 0,dictType:void 0,status:void 0}),J=m(),M=E({form:{},rules:{dictLabel:[{required:!0,message:"数据标签不能为空",trigger:"blur"}],dictValue:[{required:!0,message:"数据键值不能为空",trigger:"blur"}],dictSort:[{required:!0,message:"数据顺序不能为空",trigger:"blur"}],langKey:[{pattern:/^[A-Za-z].+$/,message:"输入格式不正确,格式：login.ok",trigger:"blur"}]}}),{form:n,rules:W}=be(M);function X(u){De(u).then(t=>{r.dictType=t.data.dictType,R.value=t.data.dictType,C()})}function Y(){Ne().then(u=>{I.value=u.data.result})}function C(){K.value=!0,ve(r).then(u=>{O.value=u.data.result,N.value=u.data.totalNum,K.value=!1})}function ee(){f.value=!1,z()}function z(){n.value={dictCode:void 0,dictLabel:void 0,dictValue:void 0,dictSort:0,status:"0",remark:void 0},c.resetForm("formRef")}function P(){r.pageNum=1,C()}function le(){c.resetForm("queryForm"),r.dictType=R.value,P()}function te(){z(),f.value=!0,$.value="添加字典数据",n.value.dictType=r.dictType}function ae(u){z();const t=u.dictCode||F.value;we(t).then(g=>{n.value=g.data,f.value=!0,$.value="修改字典数据"})}function oe(){c.$refs.formRef.validate(u=>{u&&(n.value.dictCode!=null?Te(n.value).then(t=>{c.$modal.msgSuccess("修改成功"),f.value=!1,C()}):Ue(n.value).then(t=>{c.$modal.msgSuccess("新增成功"),f.value=!1,C()}))})}function ne(u){const t=u.dictCode||F.value;c.$confirm('是否确认删除字典编码为"'+t+'"的数据项?',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return Se(t)}).then(()=>{C(),c.$modal.msgSuccess("删除成功")})}function ue(u){const t=u.status=="0"?"启用":"停用";c.$confirm(`确认要${t} [${u.dictLabel}]吗?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return Le(u.dictCode,u.status)}).then(()=>{c.$modal.msgSuccess(t+"成功")}).catch(function(){u.status=u.status==0?1:0})}return(u,t)=>{const g=d("el-option"),T=d("el-select"),i=d("el-form-item"),_=d("el-button"),A=d("el-form"),p=d("el-col"),j=d("el-row"),b=d("el-table-column"),de=d("el-tag"),se=d("el-switch"),re=d("el-table"),ie=d("pagination"),y=d("el-input"),pe=d("el-input-number"),me=d("el-radio"),ce=d("el-radio-group"),fe=d("el-dialog"),h=_e("hasPermi");return s(),v(x,null,[l(A,{model:o(r),ref:"queryForm",inline:!0},{default:a(()=>[l(i,{label:"字典名称",prop:"dictType"},{default:a(()=>[l(T,{modelValue:o(r).dictType,"onUpdate:modelValue":t[0]||(t[0]=e=>o(r).dictType=e)},{default:a(()=>[(s(!0),v(x,null,w(o(I),e=>(s(),V(g,{key:e.dictId,label:e.dictName,value:e.dictType},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(i,{label:"状态",prop:"status"},{default:a(()=>[l(T,{modelValue:o(r).status,"onUpdate:modelValue":t[1]||(t[1]=e=>o(r).status=e),placeholder:"数据状态",clearable:""},{default:a(()=>[(s(!0),v(x,null,w(o(B),e=>(s(),V(g,{key:e.dictValue,label:e.dictLabel,value:e.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(i,null,{default:a(()=>[l(_,{type:"primary",icon:"search",onClick:P},{default:a(()=>t[16]||(t[16]=[k("搜索")])),_:1}),l(_,{icon:"refresh",onClick:le},{default:a(()=>t[17]||(t[17]=[k("重置")])),_:1})]),_:1})]),_:1},8,["model"]),l(j,{gutter:10,class:"mb8"},{default:a(()=>[l(p,{span:1.5},{default:a(()=>[S((s(),V(_,{type:"primary",plain:"",icon:"plus",onClick:te},{default:a(()=>t[18]||(t[18]=[k("新增数据")])),_:1})),[[h,["system:dict:add"]]])]),_:1})]),_:1}),l(re,{data:o(O),border:""},{default:a(()=>[l(b,{label:"字典编码",align:"center",prop:"dictCode"}),l(b,{label:"字典标签",align:"center",prop:"dictLabel"},{default:a(e=>[e.row.listClass==""||e.row.listClass=="default"?(s(),v("span",{key:0,class:L(e.row.cssClass)},D(e.row.dictLabel),3)):(s(),V(de,{key:1,type:e.row.listClass=="primary"?"":e.row.listClass,class:L(e.row.cssClass)},{default:a(()=>[k(D(e.row.dictLabel),1)]),_:2},1032,["type","class"]))]),_:1}),l(b,{label:"翻译键值",align:"center",prop:"langKey"}),l(b,{label:"字典键值",align:"center",prop:"dictValue",sortable:""}),l(b,{label:"字典排序",align:"center",prop:"dictSort",sortable:""}),l(b,{label:"状态",align:"center",prop:"status",width:"120"},{default:a(e=>[l(se,{modelValue:e.row.status,"onUpdate:modelValue":U=>e.row.status=U,"active-value":"0","inactive-value":"1","active-text":"启用","inactive-text":"停用",onClick:U=>ue(e.row)},null,8,["modelValue","onUpdate:modelValue","onClick"])]),_:1}),l(b,{label:"备注",align:"center",prop:"remark","show-overflow-tooltip":!0}),l(b,{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"130px"},{default:a(e=>[e.row.dictCode>0?(s(),v("div",$e,[S(l(_,{text:"",size:"default",icon:"edit",onClick:U=>ae(e.row)},null,8,["onClick"]),[[h,["system:dict:edit"]]]),S(l(_,{text:"",size:"default",icon:"delete",onClick:U=>ne(e.row)},null,8,["onClick"]),[[h,["system:dict:remove"]]])])):ye("",!0)]),_:1})]),_:1},8,["data"]),S(l(ie,{total:o(N),page:o(r).pageNum,"onUpdate:page":t[2]||(t[2]=e=>o(r).pageNum=e),limit:o(r).pageSize,"onUpdate:limit":t[3]||(t[3]=e=>o(r).pageSize=e),onPagination:C},null,8,["total","page","limit"]),[[Ve,o(N)>0]]),l(fe,{title:o($),modelValue:o(f),"onUpdate:modelValue":t[15]||(t[15]=e=>Ce(f)?f.value=e:null),draggable:"",width:"500px","append-to-body":""},{footer:a(()=>[q("div",ze,[l(_,{text:"",onClick:ee},{default:a(()=>t[19]||(t[19]=[k("取 消")])),_:1}),l(_,{type:"primary",onClick:oe},{default:a(()=>t[20]||(t[20]=[k("确 定")])),_:1})])]),default:a(()=>[l(A,{ref_key:"formRef",ref:J,model:o(n),rules:o(W),"label-width":"80px"},{default:a(()=>[l(j,{gutter:20},{default:a(()=>[l(p,{lg:24},{default:a(()=>[l(i,{label:"字典类型"},{default:a(()=>[l(y,{modelValue:o(n).dictType,"onUpdate:modelValue":t[4]||(t[4]=e=>o(n).dictType=e),disabled:!0},null,8,["modelValue"])]),_:1})]),_:1}),l(p,{lg:12},{default:a(()=>[l(i,{label:"字典标签",prop:"dictLabel"},{default:a(()=>[l(y,{modelValue:o(n).dictLabel,"onUpdate:modelValue":t[5]||(t[5]=e=>o(n).dictLabel=e),placeholder:"请输入字典标签"},null,8,["modelValue"])]),_:1})]),_:1}),l(p,{lg:12},{default:a(()=>[l(i,{label:"翻译键值",prop:"langKey"},{default:a(()=>[l(y,{modelValue:o(n).langKey,"onUpdate:modelValue":t[6]||(t[6]=e=>o(n).langKey=e),placeholder:"请输入翻译键值"},null,8,["modelValue"])]),_:1})]),_:1}),l(p,{lg:24},{default:a(()=>[l(i,{label:"数据键值",prop:"dictValue"},{default:a(()=>[l(y,{modelValue:o(n).dictValue,"onUpdate:modelValue":t[7]||(t[7]=e=>o(n).dictValue=e),placeholder:"请输入数据键值"},null,8,["modelValue"])]),_:1})]),_:1}),l(p,{lg:12},{default:a(()=>[l(i,{label:"样式属性",prop:"cssClass"},{default:a(()=>[l(T,{modelValue:o(n).cssClass,"onUpdate:modelValue":t[8]||(t[8]=e=>o(n).cssClass=e),"allow-create":"",filterable:"",clearable:""},{default:a(()=>[(s(!0),v(x,null,w(o(H),e=>(s(),V(g,{class:L(e.value),key:e.value,label:e.label,value:e.value},{default:a(()=>[q("span",{style:{float:"left"},class:L(e.value)},D(e.label),3),q("span",Be,D(e.value),1)]),_:2},1032,["class","label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(p,{lg:12},{default:a(()=>[l(i,{label:"回显样式",prop:"listClass"},{default:a(()=>[l(T,{modelValue:o(n).listClass,"onUpdate:modelValue":t[9]||(t[9]=e=>o(n).listClass=e)},{default:a(()=>[(s(!0),v(x,null,w(o(G),e=>(s(),V(g,{key:e.value,label:e.label+"("+e.value+")",value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(p,{lg:12},{default:a(()=>[l(i,{label:"显示排序",prop:"dictSort"},{default:a(()=>[l(pe,{modelValue:o(n).dictSort,"onUpdate:modelValue":t[10]||(t[10]=e=>o(n).dictSort=e),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1})]),_:1}),l(p,{lg:12},{default:a(()=>[l(i,{label:"状态",prop:"status"},{default:a(()=>[l(ce,{modelValue:o(n).status,"onUpdate:modelValue":t[11]||(t[11]=e=>o(n).status=e)},{default:a(()=>[(s(!0),v(x,null,w(o(B),e=>(s(),V(me,{key:e.dictValue,value:e.dictValue,label:e.dictLabel},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(p,{lg:12},{default:a(()=>[l(i,{label:"扩展1",prop:"extend1"},{default:a(()=>[l(y,{modelValue:o(n).extend1,"onUpdate:modelValue":t[12]||(t[12]=e=>o(n).extend1=e),placeholder:"请输入扩展内容"},null,8,["modelValue"])]),_:1})]),_:1}),l(p,{lg:12},{default:a(()=>[l(i,{label:"扩展2",prop:"extend2"},{default:a(()=>[l(y,{modelValue:o(n).extend2,"onUpdate:modelValue":t[13]||(t[13]=e=>o(n).extend2=e),placeholder:"请输入扩展内容"},null,8,["modelValue"])]),_:1})]),_:1}),l(p,{lg:24},{default:a(()=>[l(i,{label:"备注",prop:"remark"},{default:a(()=>[l(y,{modelValue:o(n).remark,"onUpdate:modelValue":t[14]||(t[14]=e=>o(n).remark=e),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])],64)}}});export{Ke as default};
