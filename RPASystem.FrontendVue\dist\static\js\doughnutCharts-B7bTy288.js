import{i as s}from"./index-BtiuLXK9.js";import{_ as n,r as l,M as i,o as c,c as f}from"./index-D9f5ARRd.js";const u={__name:"doughnutCharts",setup(d){const a=l(null),o={tooltip:{trigger:"item"},title:{text:"数据来源",textStyle:{color:"#00e4ff"}},legend:{top:"5%",left:"center",textStyle:{color:"#fff"},itemStyle:{}},series:[{name:"访问来源",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:[{value:1048,name:"搜索引擎"},{value:735,name:"直接访问"},{value:580,name:"电子邮件"},{value:484,name:"其他"},{value:300,name:"广告点击"}]}]};let e=null;const r=()=>{const t=s(a.value);return t.setOption(o),t};return i(()=>{e=r(),window.addEventListener("resize",function(){e&&e.resize()})}),(t,p)=>(c(),f("div",{class:"echarts",ref_key:"chartsRef",ref:a},null,512))}},_=n(u,[["__scopeId","data-v-622d250d"]]);export{_ as default};
