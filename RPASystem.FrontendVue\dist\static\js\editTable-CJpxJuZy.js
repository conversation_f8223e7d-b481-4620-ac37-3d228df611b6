import{g as Wt,u as $t}from"./gen-DoJy3V-s.js";import{l as jt}from"./type-BoO7AbZI.js";import qt from"./basicInfoForm-CxefLcal.js";import Kt from"./genInfoForm-Ci_jqjcQ.js";import{r as ae,u as Qt,M as Zt,x as G,N as Jt,o as ye,c as _t,i as d,l as y,k as Z,D as Ee,j as ge,O as en,p as Ye,q as yt,F as tn,K as nn,t as Et,m as on,s as an,v as rn}from"./index-D9f5ARRd.js";/**!
 * Sortable 1.15.6
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function Dt(o,e){var n=Object.keys(o);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(o);e&&(t=t.filter(function(a){return Object.getOwnPropertyDescriptor(o,a).enumerable})),n.push.apply(n,t)}return n}function re(o){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Dt(Object(n),!0).forEach(function(t){ln(o,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(n)):Dt(Object(n)).forEach(function(t){Object.defineProperty(o,t,Object.getOwnPropertyDescriptor(n,t))})}return o}function ze(o){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?ze=function(e){return typeof e}:ze=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ze(o)}function ln(o,e,n){return e in o?Object.defineProperty(o,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):o[e]=n,o}function se(){return se=Object.assign||function(o){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(o[t]=n[t])}return o},se.apply(this,arguments)}function sn(o,e){if(o==null)return{};var n={},t=Object.keys(o),a,i;for(i=0;i<t.length;i++)a=t[i],!(e.indexOf(a)>=0)&&(n[a]=o[a]);return n}function un(o,e){if(o==null)return{};var n=sn(o,e),t,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(o);for(a=0;a<i.length;a++)t=i[a],!(e.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(o,t)&&(n[t]=o[t])}return n}var dn="1.15.6";function le(o){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(o)}var ue=le(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Ue=le(/Edge/i),St=le(/firefox/i),Fe=le(/safari/i)&&!le(/chrome/i)&&!le(/android/i),mt=le(/iP(ad|od|hone)/i),At=le(/chrome/i)&&le(/android/i),Ft={capture:!1,passive:!1};function T(o,e,n){o.addEventListener(e,n,!ue&&Ft)}function E(o,e,n){o.removeEventListener(e,n,!ue&&Ft)}function Ke(o,e){if(e){if(e[0]===">"&&(e=e.substring(1)),o)try{if(o.matches)return o.matches(e);if(o.msMatchesSelector)return o.msMatchesSelector(e);if(o.webkitMatchesSelector)return o.webkitMatchesSelector(e)}catch{return!1}return!1}}function Vt(o){return o.host&&o!==document&&o.host.nodeType?o.host:o.parentNode}function ne(o,e,n,t){if(o){n=n||document;do{if(e!=null&&(e[0]===">"?o.parentNode===n&&Ke(o,e):Ke(o,e))||t&&o===n)return o;if(o===n)break}while(o=Vt(o))}return null}var Tt=/\s+/g;function q(o,e,n){if(o&&e)if(o.classList)o.classList[n?"add":"remove"](e);else{var t=(" "+o.className+" ").replace(Tt," ").replace(" "+e+" "," ");o.className=(t+(n?" "+e:"")).replace(Tt," ")}}function m(o,e,n){var t=o&&o.style;if(t){if(n===void 0)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(o,""):o.currentStyle&&(n=o.currentStyle),e===void 0?n:n[e];!(e in t)&&e.indexOf("webkit")===-1&&(e="-webkit-"+e),t[e]=n+(typeof n=="string"?"":"px")}}function Ie(o,e){var n="";if(typeof o=="string")n=o;else do{var t=m(o,"transform");t&&t!=="none"&&(n=t+" "+n)}while(!e&&(o=o.parentNode));var a=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return a&&new a(n)}function kt(o,e,n){if(o){var t=o.getElementsByTagName(e),a=0,i=t.length;if(n)for(;a<i;a++)n(t[a],a);return t}return[]}function ie(){var o=document.scrollingElement;return o||document.documentElement}function M(o,e,n,t,a){if(!(!o.getBoundingClientRect&&o!==window)){var i,r,l,s,u,h,c;if(o!==window&&o.parentNode&&o!==ie()?(i=o.getBoundingClientRect(),r=i.top,l=i.left,s=i.bottom,u=i.right,h=i.height,c=i.width):(r=0,l=0,s=window.innerHeight,u=window.innerWidth,h=window.innerHeight,c=window.innerWidth),(e||n)&&o!==window&&(a=a||o.parentNode,!ue))do if(a&&a.getBoundingClientRect&&(m(a,"transform")!=="none"||n&&m(a,"position")!=="static")){var _=a.getBoundingClientRect();r-=_.top+parseInt(m(a,"border-top-width")),l-=_.left+parseInt(m(a,"border-left-width")),s=r+i.height,u=l+i.width;break}while(a=a.parentNode);if(t&&o!==window){var x=Ie(a||o),D=x&&x.a,I=x&&x.d;x&&(r/=I,l/=D,c/=D,h/=I,s=r+h,u=l+c)}return{top:r,left:l,bottom:s,right:u,width:c,height:h}}}function Ct(o,e,n){for(var t=he(o,!0),a=M(o)[e];t;){var i=M(t)[n],r=void 0;if(r=a>=i,!r)return t;if(t===ie())break;t=he(t,!1)}return!1}function Oe(o,e,n,t){for(var a=0,i=0,r=o.children;i<r.length;){if(r[i].style.display!=="none"&&r[i]!==g.ghost&&(t||r[i]!==g.dragged)&&ne(r[i],n.draggable,o,!1)){if(a===e)return r[i];a++}i++}return null}function gt(o,e){for(var n=o.lastElementChild;n&&(n===g.ghost||m(n,"display")==="none"||e&&!Ke(n,e));)n=n.previousElementSibling;return n||null}function J(o,e){var n=0;if(!o||!o.parentNode)return-1;for(;o=o.previousElementSibling;)o.nodeName.toUpperCase()!=="TEMPLATE"&&o!==g.clone&&(!e||Ke(o,e))&&n++;return n}function It(o){var e=0,n=0,t=ie();if(o)do{var a=Ie(o),i=a.a,r=a.d;e+=o.scrollLeft*i,n+=o.scrollTop*r}while(o!==t&&(o=o.parentNode));return[e,n]}function fn(o,e){for(var n in o)if(o.hasOwnProperty(n)){for(var t in e)if(e.hasOwnProperty(t)&&e[t]===o[n][t])return Number(n)}return-1}function he(o,e){if(!o||!o.getBoundingClientRect)return ie();var n=o,t=!1;do if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var a=m(n);if(n.clientWidth<n.scrollWidth&&(a.overflowX=="auto"||a.overflowX=="scroll")||n.clientHeight<n.scrollHeight&&(a.overflowY=="auto"||a.overflowY=="scroll")){if(!n.getBoundingClientRect||n===document.body)return ie();if(t||e)return n;t=!0}}while(n=n.parentNode);return ie()}function cn(o,e){if(o&&e)for(var n in e)e.hasOwnProperty(n)&&(o[n]=e[n]);return o}function nt(o,e){return Math.round(o.top)===Math.round(e.top)&&Math.round(o.left)===Math.round(e.left)&&Math.round(o.height)===Math.round(e.height)&&Math.round(o.width)===Math.round(e.width)}var Ve;function Mt(o,e){return function(){if(!Ve){var n=arguments,t=this;n.length===1?o.call(t,n[0]):o.apply(t,n),Ve=setTimeout(function(){Ve=void 0},e)}}}function hn(){clearTimeout(Ve),Ve=void 0}function Rt(o,e,n){o.scrollLeft+=e,o.scrollTop+=n}function Ut(o){var e=window.Polymer,n=window.jQuery||window.Zepto;return e&&e.dom?e.dom(o).cloneNode(!0):n?n(o).clone(!0)[0]:o.cloneNode(!0)}function Xt(o,e,n){var t={};return Array.from(o.children).forEach(function(a){var i,r,l,s;if(!(!ne(a,e.draggable,o,!1)||a.animated||a===n)){var u=M(a);t.left=Math.min((i=t.left)!==null&&i!==void 0?i:1/0,u.left),t.top=Math.min((r=t.top)!==null&&r!==void 0?r:1/0,u.top),t.right=Math.max((l=t.right)!==null&&l!==void 0?l:-1/0,u.right),t.bottom=Math.max((s=t.bottom)!==null&&s!==void 0?s:-1/0,u.bottom)}}),t.width=t.right-t.left,t.height=t.bottom-t.top,t.x=t.left,t.y=t.top,t}var W="Sortable"+new Date().getTime();function pn(){var o=[],e;return{captureAnimationState:function(){if(o=[],!!this.options.animation){var t=[].slice.call(this.el.children);t.forEach(function(a){if(!(m(a,"display")==="none"||a===g.ghost)){o.push({target:a,rect:M(a)});var i=re({},o[o.length-1].rect);if(a.thisAnimationDuration){var r=Ie(a,!0);r&&(i.top-=r.f,i.left-=r.e)}a.fromRect=i}})}},addAnimationState:function(t){o.push(t)},removeAnimationState:function(t){o.splice(fn(o,{target:t}),1)},animateAll:function(t){var a=this;if(!this.options.animation){clearTimeout(e),typeof t=="function"&&t();return}var i=!1,r=0;o.forEach(function(l){var s=0,u=l.target,h=u.fromRect,c=M(u),_=u.prevFromRect,x=u.prevToRect,D=l.rect,I=Ie(u,!0);I&&(c.top-=I.f,c.left-=I.e),u.toRect=c,u.thisAnimationDuration&&nt(_,c)&&!nt(h,c)&&(D.top-c.top)/(D.left-c.left)===(h.top-c.top)/(h.left-c.left)&&(s=gn(D,_,x,a.options)),nt(c,h)||(u.prevFromRect=h,u.prevToRect=c,s||(s=a.options.animation),a.animate(u,D,c,s)),s&&(i=!0,r=Math.max(r,s),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},s),u.thisAnimationDuration=s)}),clearTimeout(e),i?e=setTimeout(function(){typeof t=="function"&&t()},r):typeof t=="function"&&t(),o=[]},animate:function(t,a,i,r){if(r){m(t,"transition",""),m(t,"transform","");var l=Ie(this.el),s=l&&l.a,u=l&&l.d,h=(a.left-i.left)/(s||1),c=(a.top-i.top)/(u||1);t.animatingX=!!h,t.animatingY=!!c,m(t,"transform","translate3d("+h+"px,"+c+"px,0)"),this.forRepaintDummy=mn(t),m(t,"transition","transform "+r+"ms"+(this.options.easing?" "+this.options.easing:"")),m(t,"transform","translate3d(0,0,0)"),typeof t.animated=="number"&&clearTimeout(t.animated),t.animated=setTimeout(function(){m(t,"transition",""),m(t,"transform",""),t.animated=!1,t.animatingX=!1,t.animatingY=!1},r)}}}}function mn(o){return o.offsetWidth}function gn(o,e,n,t){return Math.sqrt(Math.pow(e.top-o.top,2)+Math.pow(e.left-o.left,2))/Math.sqrt(Math.pow(e.top-n.top,2)+Math.pow(e.left-n.left,2))*t.animation}var De=[],ot={initializeByDefault:!0},Xe={mount:function(e){for(var n in ot)ot.hasOwnProperty(n)&&!(n in e)&&(e[n]=ot[n]);De.forEach(function(t){if(t.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),De.push(e)},pluginEvent:function(e,n,t){var a=this;this.eventCanceled=!1,t.cancel=function(){a.eventCanceled=!0};var i=e+"Global";De.forEach(function(r){n[r.pluginName]&&(n[r.pluginName][i]&&n[r.pluginName][i](re({sortable:n},t)),n.options[r.pluginName]&&n[r.pluginName][e]&&n[r.pluginName][e](re({sortable:n},t)))})},initializePlugins:function(e,n,t,a){De.forEach(function(l){var s=l.pluginName;if(!(!e.options[s]&&!l.initializeByDefault)){var u=new l(e,n,e.options);u.sortable=e,u.options=e.options,e[s]=u,se(t,u.defaults)}});for(var i in e.options)if(e.options.hasOwnProperty(i)){var r=this.modifyOption(e,i,e.options[i]);typeof r<"u"&&(e.options[i]=r)}},getEventProperties:function(e,n){var t={};return De.forEach(function(a){typeof a.eventProperties=="function"&&se(t,a.eventProperties.call(n[a.pluginName],e))}),t},modifyOption:function(e,n,t){var a;return De.forEach(function(i){e[i.pluginName]&&i.optionListeners&&typeof i.optionListeners[n]=="function"&&(a=i.optionListeners[n].call(e[i.pluginName],t))}),a}};function vn(o){var e=o.sortable,n=o.rootEl,t=o.name,a=o.targetEl,i=o.cloneEl,r=o.toEl,l=o.fromEl,s=o.oldIndex,u=o.newIndex,h=o.oldDraggableIndex,c=o.newDraggableIndex,_=o.originalEvent,x=o.putSortable,D=o.extraEventProperties;if(e=e||n&&n[W],!!e){var I,Y=e.options,ee="on"+t.charAt(0).toUpperCase()+t.substr(1);window.CustomEvent&&!ue&&!Ue?I=new CustomEvent(t,{bubbles:!0,cancelable:!0}):(I=document.createEvent("Event"),I.initEvent(t,!0,!0)),I.to=r||n,I.from=l||n,I.item=a||n,I.clone=i,I.oldIndex=s,I.newIndex=u,I.oldDraggableIndex=h,I.newDraggableIndex=c,I.originalEvent=_,I.pullMode=x?x.lastPutMode:void 0;var R=re(re({},D),Xe.getEventProperties(t,e));for(var $ in R)I[$]=R[$];n&&n.dispatchEvent(I),Y[ee]&&Y[ee].call(e,I)}}var bn=["evt"],z=function(e,n){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},a=t.evt,i=un(t,bn);Xe.pluginEvent.bind(g)(e,n,re({dragEl:f,parentEl:F,ghostEl:w,rootEl:P,nextEl:we,lastDownEl:We,cloneEl:A,cloneHidden:ce,dragStarted:Ne,putSortable:U,activeSortable:g.active,originalEvent:a,oldIndex:Ce,oldDraggableIndex:ke,newIndex:K,newDraggableIndex:fe,hideGhostForTarget:Ht,unhideGhostForTarget:Gt,cloneNowHidden:function(){ce=!0},cloneNowShown:function(){ce=!1},dispatchSortableEvent:function(l){L({sortable:n,name:l,originalEvent:a})}},i))};function L(o){vn(re({putSortable:U,cloneEl:A,targetEl:f,rootEl:P,oldIndex:Ce,oldDraggableIndex:ke,newIndex:K,newDraggableIndex:fe},o))}var f,F,w,P,we,We,A,ce,Ce,K,ke,fe,Be,U,Te=!1,Qe=!1,Ze=[],ve,te,at,it,Ot,xt,Ne,Se,Me,Re=!1,Le=!1,$e,X,rt=[],ft=!1,Je=[],tt=typeof document<"u",He=mt,Nt=Ue||ue?"cssFloat":"float",wn=tt&&!At&&!mt&&"draggable"in document.createElement("div"),Yt=function(){if(tt){if(ue)return!1;var o=document.createElement("x");return o.style.cssText="pointer-events:auto",o.style.pointerEvents==="auto"}}(),Bt=function(e,n){var t=m(e),a=parseInt(t.width)-parseInt(t.paddingLeft)-parseInt(t.paddingRight)-parseInt(t.borderLeftWidth)-parseInt(t.borderRightWidth),i=Oe(e,0,n),r=Oe(e,1,n),l=i&&m(i),s=r&&m(r),u=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+M(i).width,h=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+M(r).width;if(t.display==="flex")return t.flexDirection==="column"||t.flexDirection==="column-reverse"?"vertical":"horizontal";if(t.display==="grid")return t.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(i&&l.float&&l.float!=="none"){var c=l.float==="left"?"left":"right";return r&&(s.clear==="both"||s.clear===c)?"vertical":"horizontal"}return i&&(l.display==="block"||l.display==="flex"||l.display==="table"||l.display==="grid"||u>=a&&t[Nt]==="none"||r&&t[Nt]==="none"&&u+h>a)?"vertical":"horizontal"},_n=function(e,n,t){var a=t?e.left:e.top,i=t?e.right:e.bottom,r=t?e.width:e.height,l=t?n.left:n.top,s=t?n.right:n.bottom,u=t?n.width:n.height;return a===l||i===s||a+r/2===l+u/2},yn=function(e,n){var t;return Ze.some(function(a){var i=a[W].options.emptyInsertThreshold;if(!(!i||gt(a))){var r=M(a),l=e>=r.left-i&&e<=r.right+i,s=n>=r.top-i&&n<=r.bottom+i;if(l&&s)return t=a}}),t},Lt=function(e){function n(i,r){return function(l,s,u,h){var c=l.options.group.name&&s.options.group.name&&l.options.group.name===s.options.group.name;if(i==null&&(r||c))return!0;if(i==null||i===!1)return!1;if(r&&i==="clone")return i;if(typeof i=="function")return n(i(l,s,u,h),r)(l,s,u,h);var _=(r?l:s).options.group.name;return i===!0||typeof i=="string"&&i===_||i.join&&i.indexOf(_)>-1}}var t={},a=e.group;(!a||ze(a)!="object")&&(a={name:a}),t.name=a.name,t.checkPull=n(a.pull,!0),t.checkPut=n(a.put),t.revertClone=a.revertClone,e.group=t},Ht=function(){!Yt&&w&&m(w,"display","none")},Gt=function(){!Yt&&w&&m(w,"display","")};tt&&!At&&document.addEventListener("click",function(o){if(Qe)return o.preventDefault(),o.stopPropagation&&o.stopPropagation(),o.stopImmediatePropagation&&o.stopImmediatePropagation(),Qe=!1,!1},!0);var be=function(e){if(f){e=e.touches?e.touches[0]:e;var n=yn(e.clientX,e.clientY);if(n){var t={};for(var a in e)e.hasOwnProperty(a)&&(t[a]=e[a]);t.target=t.rootEl=n,t.preventDefault=void 0,t.stopPropagation=void 0,n[W]._onDragOver(t)}}},En=function(e){f&&f.parentNode[W]._isOutsideThisEl(e.target)};function g(o,e){if(!(o&&o.nodeType&&o.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(o));this.el=o,this.options=e=se({},e),o[W]=this;var n={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(o.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Bt(o,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(r,l){r.setData("Text",l.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:g.supportPointer!==!1&&"PointerEvent"in window&&(!Fe||mt),emptyInsertThreshold:5};Xe.initializePlugins(this,o,n);for(var t in n)!(t in e)&&(e[t]=n[t]);Lt(e);for(var a in this)a.charAt(0)==="_"&&typeof this[a]=="function"&&(this[a]=this[a].bind(this));this.nativeDraggable=e.forceFallback?!1:wn,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?T(o,"pointerdown",this._onTapStart):(T(o,"mousedown",this._onTapStart),T(o,"touchstart",this._onTapStart)),this.nativeDraggable&&(T(o,"dragover",this),T(o,"dragenter",this)),Ze.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),se(this,pn())}g.prototype={constructor:g,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(Se=null)},_getDirection:function(e,n){return typeof this.options.direction=="function"?this.options.direction.call(this,e,n,f):this.options.direction},_onTapStart:function(e){if(e.cancelable){var n=this,t=this.el,a=this.options,i=a.preventOnFilter,r=e.type,l=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,s=(l||e).target,u=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||s,h=a.filter;if(Nn(t),!f&&!(/mousedown|pointerdown/.test(r)&&e.button!==0||a.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&Fe&&s&&s.tagName.toUpperCase()==="SELECT")&&(s=ne(s,a.draggable,t,!1),!(s&&s.animated)&&We!==s)){if(Ce=J(s),ke=J(s,a.draggable),typeof h=="function"){if(h.call(this,e,s,this)){L({sortable:n,rootEl:u,name:"filter",targetEl:s,toEl:t,fromEl:t}),z("filter",n,{evt:e}),i&&e.preventDefault();return}}else if(h&&(h=h.split(",").some(function(c){if(c=ne(u,c.trim(),t,!1),c)return L({sortable:n,rootEl:c,name:"filter",targetEl:s,fromEl:t,toEl:t}),z("filter",n,{evt:e}),!0}),h)){i&&e.preventDefault();return}a.handle&&!ne(u,a.handle,t,!1)||this._prepareDragStart(e,l,s)}}},_prepareDragStart:function(e,n,t){var a=this,i=a.el,r=a.options,l=i.ownerDocument,s;if(t&&!f&&t.parentNode===i){var u=M(t);if(P=i,f=t,F=f.parentNode,we=f.nextSibling,We=t,Be=r.group,g.dragged=f,ve={target:f,clientX:(n||e).clientX,clientY:(n||e).clientY},Ot=ve.clientX-u.left,xt=ve.clientY-u.top,this._lastX=(n||e).clientX,this._lastY=(n||e).clientY,f.style["will-change"]="all",s=function(){if(z("delayEnded",a,{evt:e}),g.eventCanceled){a._onDrop();return}a._disableDelayedDragEvents(),!St&&a.nativeDraggable&&(f.draggable=!0),a._triggerDragStart(e,n),L({sortable:a,name:"choose",originalEvent:e}),q(f,r.chosenClass,!0)},r.ignore.split(",").forEach(function(h){kt(f,h.trim(),lt)}),T(l,"dragover",be),T(l,"mousemove",be),T(l,"touchmove",be),r.supportPointer?(T(l,"pointerup",a._onDrop),!this.nativeDraggable&&T(l,"pointercancel",a._onDrop)):(T(l,"mouseup",a._onDrop),T(l,"touchend",a._onDrop),T(l,"touchcancel",a._onDrop)),St&&this.nativeDraggable&&(this.options.touchStartThreshold=4,f.draggable=!0),z("delayStart",this,{evt:e}),r.delay&&(!r.delayOnTouchOnly||n)&&(!this.nativeDraggable||!(Ue||ue))){if(g.eventCanceled){this._onDrop();return}r.supportPointer?(T(l,"pointerup",a._disableDelayedDrag),T(l,"pointercancel",a._disableDelayedDrag)):(T(l,"mouseup",a._disableDelayedDrag),T(l,"touchend",a._disableDelayedDrag),T(l,"touchcancel",a._disableDelayedDrag)),T(l,"mousemove",a._delayedDragTouchMoveHandler),T(l,"touchmove",a._delayedDragTouchMoveHandler),r.supportPointer&&T(l,"pointermove",a._delayedDragTouchMoveHandler),a._dragStartTimer=setTimeout(s,r.delay)}else s()}},_delayedDragTouchMoveHandler:function(e){var n=e.touches?e.touches[0]:e;Math.max(Math.abs(n.clientX-this._lastX),Math.abs(n.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){f&&lt(f),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;E(e,"mouseup",this._disableDelayedDrag),E(e,"touchend",this._disableDelayedDrag),E(e,"touchcancel",this._disableDelayedDrag),E(e,"pointerup",this._disableDelayedDrag),E(e,"pointercancel",this._disableDelayedDrag),E(e,"mousemove",this._delayedDragTouchMoveHandler),E(e,"touchmove",this._delayedDragTouchMoveHandler),E(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,n){n=n||e.pointerType=="touch"&&e,!this.nativeDraggable||n?this.options.supportPointer?T(document,"pointermove",this._onTouchMove):n?T(document,"touchmove",this._onTouchMove):T(document,"mousemove",this._onTouchMove):(T(f,"dragend",this),T(P,"dragstart",this._onDragStart));try{document.selection?je(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(e,n){if(Te=!1,P&&f){z("dragStarted",this,{evt:n}),this.nativeDraggable&&T(document,"dragover",En);var t=this.options;!e&&q(f,t.dragClass,!1),q(f,t.ghostClass,!0),g.active=this,e&&this._appendGhost(),L({sortable:this,name:"start",originalEvent:n})}else this._nulling()},_emulateDragOver:function(){if(te){this._lastX=te.clientX,this._lastY=te.clientY,Ht();for(var e=document.elementFromPoint(te.clientX,te.clientY),n=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(te.clientX,te.clientY),e!==n);)n=e;if(f.parentNode[W]._isOutsideThisEl(e),n)do{if(n[W]){var t=void 0;if(t=n[W]._onDragOver({clientX:te.clientX,clientY:te.clientY,target:e,rootEl:n}),t&&!this.options.dragoverBubble)break}e=n}while(n=Vt(n));Gt()}},_onTouchMove:function(e){if(ve){var n=this.options,t=n.fallbackTolerance,a=n.fallbackOffset,i=e.touches?e.touches[0]:e,r=w&&Ie(w,!0),l=w&&r&&r.a,s=w&&r&&r.d,u=He&&X&&It(X),h=(i.clientX-ve.clientX+a.x)/(l||1)+(u?u[0]-rt[0]:0)/(l||1),c=(i.clientY-ve.clientY+a.y)/(s||1)+(u?u[1]-rt[1]:0)/(s||1);if(!g.active&&!Te){if(t&&Math.max(Math.abs(i.clientX-this._lastX),Math.abs(i.clientY-this._lastY))<t)return;this._onDragStart(e,!0)}if(w){r?(r.e+=h-(at||0),r.f+=c-(it||0)):r={a:1,b:0,c:0,d:1,e:h,f:c};var _="matrix(".concat(r.a,",").concat(r.b,",").concat(r.c,",").concat(r.d,",").concat(r.e,",").concat(r.f,")");m(w,"webkitTransform",_),m(w,"mozTransform",_),m(w,"msTransform",_),m(w,"transform",_),at=h,it=c,te=i}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!w){var e=this.options.fallbackOnBody?document.body:P,n=M(f,!0,He,!0,e),t=this.options;if(He){for(X=e;m(X,"position")==="static"&&m(X,"transform")==="none"&&X!==document;)X=X.parentNode;X!==document.body&&X!==document.documentElement?(X===document&&(X=ie()),n.top+=X.scrollTop,n.left+=X.scrollLeft):X=ie(),rt=It(X)}w=f.cloneNode(!0),q(w,t.ghostClass,!1),q(w,t.fallbackClass,!0),q(w,t.dragClass,!0),m(w,"transition",""),m(w,"transform",""),m(w,"box-sizing","border-box"),m(w,"margin",0),m(w,"top",n.top),m(w,"left",n.left),m(w,"width",n.width),m(w,"height",n.height),m(w,"opacity","0.8"),m(w,"position",He?"absolute":"fixed"),m(w,"zIndex","100000"),m(w,"pointerEvents","none"),g.ghost=w,e.appendChild(w),m(w,"transform-origin",Ot/parseInt(w.style.width)*100+"% "+xt/parseInt(w.style.height)*100+"%")}},_onDragStart:function(e,n){var t=this,a=e.dataTransfer,i=t.options;if(z("dragStart",this,{evt:e}),g.eventCanceled){this._onDrop();return}z("setupClone",this),g.eventCanceled||(A=Ut(f),A.removeAttribute("id"),A.draggable=!1,A.style["will-change"]="",this._hideClone(),q(A,this.options.chosenClass,!1),g.clone=A),t.cloneId=je(function(){z("clone",t),!g.eventCanceled&&(t.options.removeCloneOnHide||P.insertBefore(A,f),t._hideClone(),L({sortable:t,name:"clone"}))}),!n&&q(f,i.dragClass,!0),n?(Qe=!0,t._loopId=setInterval(t._emulateDragOver,50)):(E(document,"mouseup",t._onDrop),E(document,"touchend",t._onDrop),E(document,"touchcancel",t._onDrop),a&&(a.effectAllowed="move",i.setData&&i.setData.call(t,a,f)),T(document,"drop",t),m(f,"transform","translateZ(0)")),Te=!0,t._dragStartId=je(t._dragStarted.bind(t,n,e)),T(document,"selectstart",t),Ne=!0,window.getSelection().removeAllRanges(),Fe&&m(document.body,"user-select","none")},_onDragOver:function(e){var n=this.el,t=e.target,a,i,r,l=this.options,s=l.group,u=g.active,h=Be===s,c=l.sort,_=U||u,x,D=this,I=!1;if(ft)return;function Y(me,xe){z(me,D,re({evt:e,isOwner:h,axis:x?"vertical":"horizontal",revert:r,dragRect:a,targetRect:i,canSort:c,fromSortable:_,target:t,completed:R,onMove:function(p,S){return Ge(P,n,f,a,p,M(p),e,S)},changed:$},xe))}function ee(){Y("dragOverAnimationCapture"),D.captureAnimationState(),D!==_&&_.captureAnimationState()}function R(me){return Y("dragOverCompleted",{insertion:me}),me&&(h?u._hideClone():u._showClone(D),D!==_&&(q(f,U?U.options.ghostClass:u.options.ghostClass,!1),q(f,l.ghostClass,!0)),U!==D&&D!==g.active?U=D:D===g.active&&U&&(U=null),_===D&&(D._ignoreWhileAnimating=t),D.animateAll(function(){Y("dragOverAnimationComplete"),D._ignoreWhileAnimating=null}),D!==_&&(_.animateAll(),_._ignoreWhileAnimating=null)),(t===f&&!f.animated||t===n&&!t.animated)&&(Se=null),!l.dragoverBubble&&!e.rootEl&&t!==document&&(f.parentNode[W]._isOutsideThisEl(e.target),!me&&be(e)),!l.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),I=!0}function $(){K=J(f),fe=J(f,l.draggable),L({sortable:D,name:"change",toEl:n,newIndex:K,newDraggableIndex:fe,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),t=ne(t,l.draggable,n,!0),Y("dragOver"),g.eventCanceled)return I;if(f.contains(e.target)||t.animated&&t.animatingX&&t.animatingY||D._ignoreWhileAnimating===t)return R(!1);if(Qe=!1,u&&!l.disabled&&(h?c||(r=F!==P):U===this||(this.lastPutMode=Be.checkPull(this,u,f,e))&&s.checkPut(this,u,f,e))){if(x=this._getDirection(e,t)==="vertical",a=M(f),Y("dragOverValid"),g.eventCanceled)return I;if(r)return F=P,ee(),this._hideClone(),Y("revert"),g.eventCanceled||(we?P.insertBefore(f,we):P.appendChild(f)),R(!0);var O=gt(n,l.draggable);if(!O||Cn(e,x,this)&&!O.animated){if(O===f)return R(!1);if(O&&n===e.target&&(t=O),t&&(i=M(t)),Ge(P,n,f,a,t,i,e,!!t)!==!1)return ee(),O&&O.nextSibling?n.insertBefore(f,O.nextSibling):n.appendChild(f),F=n,$(),R(!0)}else if(O&&Tn(e,x,this)){var v=Oe(n,0,l,!0);if(v===f)return R(!1);if(t=v,i=M(t),Ge(P,n,f,a,t,i,e,!1)!==!1)return ee(),n.insertBefore(f,v),F=n,$(),R(!0)}else if(t.parentNode===n){i=M(t);var N=0,B,C=f.parentNode!==n,V=!_n(f.animated&&f.toRect||a,t.animated&&t.toRect||i,x),b=x?"top":"left",H=Ct(t,"top","top")||Ct(f,"top","top"),pe=H?H.scrollTop:void 0;Se!==t&&(B=i[b],Re=!1,Le=!V&&l.invertSwap||C),N=In(e,t,i,x,V?1:l.swapThreshold,l.invertedSwapThreshold==null?l.swapThreshold:l.invertedSwapThreshold,Le,Se===t);var Q;if(N!==0){var oe=J(f);do oe-=N,Q=F.children[oe];while(Q&&(m(Q,"display")==="none"||Q===w))}if(N===0||Q===t)return R(!1);Se=t,Me=N;var de=t.nextElementSibling,j=!1;j=N===1;var _e=Ge(P,n,f,a,t,i,e,j);if(_e!==!1)return(_e===1||_e===-1)&&(j=_e===1),ft=!0,setTimeout(Sn,30),ee(),j&&!de?n.appendChild(f):t.parentNode.insertBefore(f,j?de:t),H&&Rt(H,0,pe-H.scrollTop),F=f.parentNode,B!==void 0&&!Le&&($e=Math.abs(B-M(t)[b])),$(),R(!0)}if(n.contains(f))return R(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){E(document,"mousemove",this._onTouchMove),E(document,"touchmove",this._onTouchMove),E(document,"pointermove",this._onTouchMove),E(document,"dragover",be),E(document,"mousemove",be),E(document,"touchmove",be)},_offUpEvents:function(){var e=this.el.ownerDocument;E(e,"mouseup",this._onDrop),E(e,"touchend",this._onDrop),E(e,"pointerup",this._onDrop),E(e,"pointercancel",this._onDrop),E(e,"touchcancel",this._onDrop),E(document,"selectstart",this)},_onDrop:function(e){var n=this.el,t=this.options;if(K=J(f),fe=J(f,t.draggable),z("drop",this,{evt:e}),F=f&&f.parentNode,K=J(f),fe=J(f,t.draggable),g.eventCanceled){this._nulling();return}Te=!1,Le=!1,Re=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),ct(this.cloneId),ct(this._dragStartId),this.nativeDraggable&&(E(document,"drop",this),E(n,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Fe&&m(document.body,"user-select",""),m(f,"transform",""),e&&(Ne&&(e.cancelable&&e.preventDefault(),!t.dropBubble&&e.stopPropagation()),w&&w.parentNode&&w.parentNode.removeChild(w),(P===F||U&&U.lastPutMode!=="clone")&&A&&A.parentNode&&A.parentNode.removeChild(A),f&&(this.nativeDraggable&&E(f,"dragend",this),lt(f),f.style["will-change"]="",Ne&&!Te&&q(f,U?U.options.ghostClass:this.options.ghostClass,!1),q(f,this.options.chosenClass,!1),L({sortable:this,name:"unchoose",toEl:F,newIndex:null,newDraggableIndex:null,originalEvent:e}),P!==F?(K>=0&&(L({rootEl:F,name:"add",toEl:F,fromEl:P,originalEvent:e}),L({sortable:this,name:"remove",toEl:F,originalEvent:e}),L({rootEl:F,name:"sort",toEl:F,fromEl:P,originalEvent:e}),L({sortable:this,name:"sort",toEl:F,originalEvent:e})),U&&U.save()):K!==Ce&&K>=0&&(L({sortable:this,name:"update",toEl:F,originalEvent:e}),L({sortable:this,name:"sort",toEl:F,originalEvent:e})),g.active&&((K==null||K===-1)&&(K=Ce,fe=ke),L({sortable:this,name:"end",toEl:F,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){z("nulling",this),P=f=F=w=we=A=We=ce=ve=te=Ne=K=fe=Ce=ke=Se=Me=U=Be=g.dragged=g.ghost=g.clone=g.active=null,Je.forEach(function(e){e.checked=!0}),Je.length=at=it=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":f&&(this._onDragOver(e),Dn(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],n,t=this.el.children,a=0,i=t.length,r=this.options;a<i;a++)n=t[a],ne(n,r.draggable,this.el,!1)&&e.push(n.getAttribute(r.dataIdAttr)||xn(n));return e},sort:function(e,n){var t={},a=this.el;this.toArray().forEach(function(i,r){var l=a.children[r];ne(l,this.options.draggable,a,!1)&&(t[i]=l)},this),n&&this.captureAnimationState(),e.forEach(function(i){t[i]&&(a.removeChild(t[i]),a.appendChild(t[i]))}),n&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,n){return ne(e,n||this.options.draggable,this.el,!1)},option:function(e,n){var t=this.options;if(n===void 0)return t[e];var a=Xe.modifyOption(this,e,n);typeof a<"u"?t[e]=a:t[e]=n,e==="group"&&Lt(t)},destroy:function(){z("destroy",this);var e=this.el;e[W]=null,E(e,"mousedown",this._onTapStart),E(e,"touchstart",this._onTapStart),E(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(E(e,"dragover",this),E(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(n){n.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Ze.splice(Ze.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!ce){if(z("hideClone",this),g.eventCanceled)return;m(A,"display","none"),this.options.removeCloneOnHide&&A.parentNode&&A.parentNode.removeChild(A),ce=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(ce){if(z("showClone",this),g.eventCanceled)return;f.parentNode==P&&!this.options.group.revertClone?P.insertBefore(A,f):we?P.insertBefore(A,we):P.appendChild(A),this.options.group.revertClone&&this.animate(f,A),m(A,"display",""),ce=!1}}};function Dn(o){o.dataTransfer&&(o.dataTransfer.dropEffect="move"),o.cancelable&&o.preventDefault()}function Ge(o,e,n,t,a,i,r,l){var s,u=o[W],h=u.options.onMove,c;return window.CustomEvent&&!ue&&!Ue?s=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(s=document.createEvent("Event"),s.initEvent("move",!0,!0)),s.to=e,s.from=o,s.dragged=n,s.draggedRect=t,s.related=a||e,s.relatedRect=i||M(e),s.willInsertAfter=l,s.originalEvent=r,o.dispatchEvent(s),h&&(c=h.call(u,s,r)),c}function lt(o){o.draggable=!1}function Sn(){ft=!1}function Tn(o,e,n){var t=M(Oe(n.el,0,n.options,!0)),a=Xt(n.el,n.options,w),i=10;return e?o.clientX<a.left-i||o.clientY<t.top&&o.clientX<t.right:o.clientY<a.top-i||o.clientY<t.bottom&&o.clientX<t.left}function Cn(o,e,n){var t=M(gt(n.el,n.options.draggable)),a=Xt(n.el,n.options,w),i=10;return e?o.clientX>a.right+i||o.clientY>t.bottom&&o.clientX>t.left:o.clientY>a.bottom+i||o.clientX>t.right&&o.clientY>t.top}function In(o,e,n,t,a,i,r,l){var s=t?o.clientY:o.clientX,u=t?n.height:n.width,h=t?n.top:n.left,c=t?n.bottom:n.right,_=!1;if(!r){if(l&&$e<u*a){if(!Re&&(Me===1?s>h+u*i/2:s<c-u*i/2)&&(Re=!0),Re)_=!0;else if(Me===1?s<h+$e:s>c-$e)return-Me}else if(s>h+u*(1-a)/2&&s<c-u*(1-a)/2)return On(e)}return _=_||r,_&&(s<h+u*i/2||s>c-u*i/2)?s>h+u/2?1:-1:0}function On(o){return J(f)<J(o)?1:-1}function xn(o){for(var e=o.tagName+o.className+o.src+o.href+o.textContent,n=e.length,t=0;n--;)t+=e.charCodeAt(n);return t.toString(36)}function Nn(o){Je.length=0;for(var e=o.getElementsByTagName("input"),n=e.length;n--;){var t=e[n];t.checked&&Je.push(t)}}function je(o){return setTimeout(o,0)}function ct(o){return clearTimeout(o)}tt&&T(document,"touchmove",function(o){(g.active||Te)&&o.cancelable&&o.preventDefault()});g.utils={on:T,off:E,css:m,find:kt,is:function(e,n){return!!ne(e,n,e,!1)},extend:cn,throttle:Mt,closest:ne,toggleClass:q,clone:Ut,index:J,nextTick:je,cancelNextTick:ct,detectDirection:Bt,getChild:Oe,expando:W};g.get=function(o){return o[W]};g.mount=function(){for(var o=arguments.length,e=new Array(o),n=0;n<o;n++)e[n]=arguments[n];e[0].constructor===Array&&(e=e[0]),e.forEach(function(t){if(!t.prototype||!t.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(t));t.utils&&(g.utils=re(re({},g.utils),t.utils)),Xe.mount(t)})};g.create=function(o,e){return new g(o,e)};g.version=dn;var k=[],Pe,ht,pt=!1,st,ut,et,Ae;function Pn(){function o(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this))}return o.prototype={dragStarted:function(n){var t=n.originalEvent;this.sortable.nativeDraggable?T(document,"dragover",this._handleAutoScroll):this.options.supportPointer?T(document,"pointermove",this._handleFallbackAutoScroll):t.touches?T(document,"touchmove",this._handleFallbackAutoScroll):T(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(n){var t=n.originalEvent;!this.options.dragOverBubble&&!t.rootEl&&this._handleAutoScroll(t)},drop:function(){this.sortable.nativeDraggable?E(document,"dragover",this._handleAutoScroll):(E(document,"pointermove",this._handleFallbackAutoScroll),E(document,"touchmove",this._handleFallbackAutoScroll),E(document,"mousemove",this._handleFallbackAutoScroll)),Pt(),qe(),hn()},nulling:function(){et=ht=Pe=pt=Ae=st=ut=null,k.length=0},_handleFallbackAutoScroll:function(n){this._handleAutoScroll(n,!0)},_handleAutoScroll:function(n,t){var a=this,i=(n.touches?n.touches[0]:n).clientX,r=(n.touches?n.touches[0]:n).clientY,l=document.elementFromPoint(i,r);if(et=n,t||this.options.forceAutoScrollFallback||Ue||ue||Fe){dt(n,this.options,l,t);var s=he(l,!0);pt&&(!Ae||i!==st||r!==ut)&&(Ae&&Pt(),Ae=setInterval(function(){var u=he(document.elementFromPoint(i,r),!0);u!==s&&(s=u,qe()),dt(n,a.options,u,t)},10),st=i,ut=r)}else{if(!this.options.bubbleScroll||he(l,!0)===ie()){qe();return}dt(n,this.options,he(l,!1),!1)}}},se(o,{pluginName:"scroll",initializeByDefault:!0})}function qe(){k.forEach(function(o){clearInterval(o.pid)}),k=[]}function Pt(){clearInterval(Ae)}var dt=Mt(function(o,e,n,t){if(e.scroll){var a=(o.touches?o.touches[0]:o).clientX,i=(o.touches?o.touches[0]:o).clientY,r=e.scrollSensitivity,l=e.scrollSpeed,s=ie(),u=!1,h;ht!==n&&(ht=n,qe(),Pe=e.scroll,h=e.scrollFn,Pe===!0&&(Pe=he(n,!0)));var c=0,_=Pe;do{var x=_,D=M(x),I=D.top,Y=D.bottom,ee=D.left,R=D.right,$=D.width,O=D.height,v=void 0,N=void 0,B=x.scrollWidth,C=x.scrollHeight,V=m(x),b=x.scrollLeft,H=x.scrollTop;x===s?(v=$<B&&(V.overflowX==="auto"||V.overflowX==="scroll"||V.overflowX==="visible"),N=O<C&&(V.overflowY==="auto"||V.overflowY==="scroll"||V.overflowY==="visible")):(v=$<B&&(V.overflowX==="auto"||V.overflowX==="scroll"),N=O<C&&(V.overflowY==="auto"||V.overflowY==="scroll"));var pe=v&&(Math.abs(R-a)<=r&&b+$<B)-(Math.abs(ee-a)<=r&&!!b),Q=N&&(Math.abs(Y-i)<=r&&H+O<C)-(Math.abs(I-i)<=r&&!!H);if(!k[c])for(var oe=0;oe<=c;oe++)k[oe]||(k[oe]={});(k[c].vx!=pe||k[c].vy!=Q||k[c].el!==x)&&(k[c].el=x,k[c].vx=pe,k[c].vy=Q,clearInterval(k[c].pid),(pe!=0||Q!=0)&&(u=!0,k[c].pid=setInterval((function(){t&&this.layer===0&&g.active._onTouchMove(et);var de=k[this.layer].vy?k[this.layer].vy*l:0,j=k[this.layer].vx?k[this.layer].vx*l:0;typeof h=="function"&&h.call(g.dragged.parentNode[W],j,de,o,et,k[this.layer].el)!=="continue"||Rt(k[this.layer].el,j,de)}).bind({layer:c}),24))),c++}while(e.bubbleScroll&&_!==s&&(_=he(_,!1)));pt=u}},30),zt=function(e){var n=e.originalEvent,t=e.putSortable,a=e.dragEl,i=e.activeSortable,r=e.dispatchSortableEvent,l=e.hideGhostForTarget,s=e.unhideGhostForTarget;if(n){var u=t||i;l();var h=n.changedTouches&&n.changedTouches.length?n.changedTouches[0]:n,c=document.elementFromPoint(h.clientX,h.clientY);s(),u&&!u.el.contains(c)&&(r("spill"),this.onSpill({dragEl:a,putSortable:t}))}};function vt(){}vt.prototype={startIndex:null,dragStart:function(e){var n=e.oldDraggableIndex;this.startIndex=n},onSpill:function(e){var n=e.dragEl,t=e.putSortable;this.sortable.captureAnimationState(),t&&t.captureAnimationState();var a=Oe(this.sortable.el,this.startIndex,this.options);a?this.sortable.el.insertBefore(n,a):this.sortable.el.appendChild(n),this.sortable.animateAll(),t&&t.animateAll()},drop:zt};se(vt,{pluginName:"revertOnSpill"});function bt(){}bt.prototype={onSpill:function(e){var n=e.dragEl,t=e.putSortable,a=t||this.sortable;a.captureAnimationState(),n.parentNode&&n.parentNode.removeChild(n),a.animateAll()},drop:zt};se(bt,{pluginName:"removeOnSpill"});g.mount(new Pn);g.mount(bt,vt);const An={style:{float:"left"}},Fn={style:{float:"right",color:"#8492a6","font-size":"13px"}},Vn={class:"mt20",style:{"text-align":"center"}},kn=an({name:"genedit"}),Bn=Object.assign(kn,{setup(o){const e=ae("basic"),n=ae(document.documentElement.scrollHeight-275+"px"),t=ae([]),a=ae([]),i=ae([]),r=ae({}),l=ae(!0),s=ae(),u=Qt(),{proxy:h}=rn(),c=ae(!1);function _(){const O=u.query&&u.query.tableId;O&&Wt(O).then(v=>{l.value=!1,a.value=v.data.info.columns,r.value={...v.data.info,...v.data.info.options},t.value=v.data.tables})}function x(){c.value=!0;const O=h.$refs.basicInfo.$refs.basicInfoForm,v=h.$refs.genInfo.$refs.genInfoForm;Promise.all([O,v].map(D)).then(N=>{if(N.every(C=>!!C)){const C=Object.assign({},r.value);C.columns=a.value,C.params=r.value,$t(C).then(V=>{h.$modal.msgSuccess(V.msg),V.code===200&&I()}).catch(()=>{c.value=!1})}else c.value=!1,h.$modal.msgError("表单校验未通过，请重新检查提交内容")})}function D(O){return new Promise(v=>{O.validate(N=>{v(N)})})}jt({pageSize:100}).then(O=>{i.value=O.data.result});function I(){const O={path:"/tool/gen",query:{t:Date.now(),pageNum:u.query.pageNum}};h.$tab.closeOpenPage(O)}const Y=ae([]),ee=O=>{O&&Y.value.push(O)};function R(O,v,N){var B=Y.value.length,C=N.keyCode||N.which||N.charCode;C===13&&(B-1==v?console.log("到最后一行了"):Y.value[v+1].focus())}const $=()=>{const O=document.querySelector(".el-table__body > tbody");g.create(O,{dragClass:"sortable-ghost",onEnd:v=>{const N=a.value.splice(v.oldIndex,1)[0];a.value.splice(v.newIndex,0,N);for(const B in a.value)a.value[B].sort=parseInt(B)+1}})};return Zt(()=>{$()}),_(),(O,v)=>{const N=G("el-tab-pane"),B=G("el-alert"),C=G("el-table-column"),V=G("el-input"),b=G("el-option"),H=G("el-select"),pe=G("el-switch"),Q=G("questionFilled"),oe=G("el-icon"),de=G("el-tooltip"),j=G("el-checkbox"),_e=G("el-table"),me=G("el-tabs"),xe=G("el-button"),wt=Jt("loading");return ye(),_t("div",null,[d(me,{modelValue:Z(e),"onUpdate:modelValue":v[0]||(v[0]=p=>on(e)?e.value=p:null),"tab-position":"top"},{default:y(()=>[d(N,{label:"基本信息",name:"basic"},{default:y(()=>[d(Z(qt),{ref:"basicInfo",info:Z(r)},null,8,["info"])]),_:1}),d(N,{label:"生成信息",name:"genInfo"},{default:y(()=>[d(Z(Kt),{ref:"genInfo",info:Z(r),tables:Z(t),columns:Z(a)},null,8,["info","tables","columns"])]),_:1}),d(N,{label:"字段信息",name:"cloum"},{default:y(()=>[d(B,{type:"success"},{default:y(()=>v[4]||(v[4]=[Ee("查看文档："),ge("a",{target:"_blank",href:"http://www.izhaorui.cn/doc/backend/code.html"},"https://www.izhaorui.cn/doc/backend/code.html",-1)])),_:1}),en((ye(),Ye(_e,{ref_key:"dragTableRef",ref:s,data:Z(a),"row-key":"columnId","min-height":"80px","max-height":Z(n)},{default:y(()=>[d(C,{label:"#",type:"index","class-name":"allowDrag",width:"60",fixed:""}),d(C,{label:"字段列名",prop:"columnName","show-overflow-tooltip":!0,width:"90",fixed:""}),d(C,{label:"字段描述",fixed:"",width:"120"},{default:y(p=>[d(V,{modelValue:p.row.columnComment,"onUpdate:modelValue":S=>p.row.columnComment=S,ref:ee,onKeydown:S=>R(p.row,p.$index,S)},null,8,["modelValue","onUpdate:modelValue","onKeydown"])]),_:1}),d(C,{label:"物理类型",prop:"columnType","show-overflow-tooltip":!0,width:"90"}),d(C,{label:"C#类型",width:"100"},{default:y(p=>[d(H,{modelValue:p.row.csharpType,"onUpdate:modelValue":S=>p.row.csharpType=S},{default:y(()=>[d(b,{label:"int",value:"int"}),d(b,{label:"long",value:"long"}),d(b,{label:"string",value:"string"}),d(b,{label:"double",value:"double"}),d(b,{label:"decimal",value:"decimal"}),d(b,{label:"DateTime",value:"DateTime"}),d(b,{label:"bool",value:"bool"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),d(C,{label:"C#属性",width:"100"},{default:y(p=>[d(V,{modelValue:p.row.csharpField,"onUpdate:modelValue":S=>p.row.csharpField=S},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),d(C,{label:"必填",width:"60",align:"center"},{default:y(p=>[d(pe,{modelValue:p.row.isRequired,"onUpdate:modelValue":S=>p.row.isRequired=S},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),d(C,{label:"前端",align:"center","label-class-name":"text-info"},{header:y(()=>[ge("span",null,[d(de,{content:"前端表单会显示所有列，如不需要请手动删除即可",placement:"top"},{default:y(()=>[d(oe,{size:15},{default:y(()=>[d(Q)]),_:1})]),_:1}),v[5]||(v[5]=Ee(" 前端 "))])]),default:y(()=>[d(C,{label:"列表",width:"60",align:"center"},{default:y(p=>[d(j,{modelValue:p.row.isList,"onUpdate:modelValue":S=>p.row.isList=S},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),d(C,{label:"排序",width:"60",align:"center"},{default:y(p=>[d(j,{modelValue:p.row.isSort,"onUpdate:modelValue":S=>p.row.isSort=S,disabled:p.row.htmlType=="imageUpload"||p.row.htmlType=="fileUpload"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1})]),_:1}),d(C,{label:"后端",align:"center","label-class-name":"text-hotpink"},{default:y(()=>[d(C,{label:"自动填充",width:"90",align:"center"},{header:y(()=>[ge("span",null,[d(de,{content:"如果数据库有默认值，选择后会自动插入默认值",placement:"top"},{default:y(()=>[d(oe,{size:15},{default:y(()=>[d(Q)]),_:1})]),_:1}),v[6]||(v[6]=Ee(" 自动填充 "))])]),default:y(p=>[d(H,{modelValue:p.row.autoFillType,"onUpdate:modelValue":S=>p.row.autoFillType=S},{default:y(()=>[d(b,{label:" ",value:0}),d(b,{label:"插入",value:1}),d(b,{label:"编辑",value:2}),d(b,{label:"插入编辑",value:3})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),d(C,{label:"导出",width:"60",align:"center"},{default:y(p=>[d(j,{modelValue:p.row.isExport,"onUpdate:modelValue":S=>p.row.isExport=S},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1}),d(C,{label:"查询",align:"center","label-class-name":"text-green"},{default:y(()=>[d(C,{label:"查询",width:"60",align:"center"},{default:y(p=>[d(j,{modelValue:p.row.isQuery,"onUpdate:modelValue":S=>p.row.isQuery=S,disabled:p.row.htmlType=="imageUpload"||p.row.htmlType=="fileUpload"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),d(C,{label:"查询方式",width:"90",align:"center"},{default:y(p=>[p.row.isQuery?(ye(),Ye(H,{key:0,modelValue:p.row.queryType,"onUpdate:modelValue":S=>p.row.queryType=S,disabled:p.row.htmlType=="datetime"},{default:y(()=>[d(b,{label:"=",value:"EQ"}),d(b,{label:"!=",value:"NE"}),d(b,{label:">",value:"GT"}),d(b,{label:">=",value:"GTE"}),d(b,{label:"<",value:"LT"}),d(b,{label:"<=",value:"LTE"}),d(b,{label:"LIKE",value:"LIKE"}),d(b,{label:"BETWEEN",value:"BETWEEN"})]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])):yt("",!0)]),_:1})]),_:1}),d(C,{label:"表单显示类型",width:"140"},{default:y(p=>[d(H,{modelValue:p.row.htmlType,"onUpdate:modelValue":S=>p.row.htmlType=S},{default:y(()=>[d(b,{label:"文本框",value:"input"}),d(b,{label:"数字框",value:"inputNumber"}),d(b,{label:"文本域",value:"textarea"}),d(b,{label:"下拉框",value:"select"}),d(b,{label:"下拉框多选",value:"selectMulti"}),d(b,{label:"单选框",value:"radio"}),d(b,{label:"复选框",value:"checkbox"}),d(b,{label:"日期时间范围控件",value:"datetime"}),d(b,{label:"日期范围控件",value:"datePicker"}),d(b,{label:"图片上传",value:"imageUpload"}),d(b,{label:"文件上传",value:"fileUpload"}),d(b,{label:"富文本控件",value:"editor"}),d(b,{label:"自定义输入框",value:"customInput"}),d(b,{label:"下拉单选组合",title:"查询下拉,表单单选",value:"selectRadio"}),d(b,{label:"颜色选择器",value:"colorPicker"}),d(b,{label:"月份选择器(查询)",value:"month"}),d(b,{label:"switch开关",value:"switch"}),d(b,{label:"slider滑块",value:"slider"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),d(C,{label:"字典类型","min-width":"140"},{default:y(p=>[p.row.htmlType=="selectMulti"||p.row.htmlType=="select"||p.row.htmlType=="radio"||p.row.htmlType=="checkbox"||p.row.htmlType=="selectRadio"?(ye(),Ye(H,{key:0,modelValue:p.row.dictType,"onUpdate:modelValue":S=>p.row.dictType=S,clearable:"",filterable:"",placeholder:"请选择字典类型"},{default:y(()=>[(ye(!0),_t(tn,null,nn(Z(i),S=>(ye(),Ye(b,{key:S.dictType,label:S.dictName,value:S.dictType},{default:y(()=>[ge("span",An,Et(S.dictName),1),ge("span",Fn,Et(S.dictType),1)]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):yt("",!0)]),_:1}),d(C,{label:"备注",align:"center",width:"200"},{default:y(p=>[d(V,{modelValue:p.row.remark,"onUpdate:modelValue":S=>p.row.remark=S},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data","max-height"])),[[wt,Z(l)]]),v[7]||(v[7]=ge("div",{style:{"font-size":"12px",color:"#8492a6"}},"提示：拖动单元格可排序",-1))]),_:1})]),_:1},8,["modelValue"]),ge("footer",Vn,[d(xe,{type:"primary",icon:"check",loading:Z(c),onClick:v[1]||(v[1]=p=>x())},{default:y(()=>v[8]||(v[8]=[Ee("提交")])),_:1},8,["loading"]),d(xe,{type:"success",icon:"refresh",onClick:v[2]||(v[2]=p=>_())},{default:y(()=>v[9]||(v[9]=[Ee("刷新")])),_:1}),d(xe,{icon:"back",onClick:v[3]||(v[3]=p=>I())},{default:y(()=>v[10]||(v[10]=[Ee("返回")])),_:1})])])}}});export{Bn as default};
