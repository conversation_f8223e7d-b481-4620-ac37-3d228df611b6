import{ai as U,a as fe,r as d,E as B,a5 as ce,x as s,N as H,o as r,c as be,O as _,S as ge,k as l,i as t,l as o,m as z,D as f,t as T,A as _e,p as u,q as v,a6 as ve,j as ye,s as he,v as we}from"./index-D9f5ARRd.js";function Ce(y){return U({url:"system/EmailLog/list",method:"get",params:y})}function ke(y){return U({url:"system/EmailLog/sendEmail",method:"post",data:y})}function Se(y){return U({url:"system/EmailLog/"+y,method:"delete"})}const Ve=["innerHTML"],Ee=he({name:"emaillog"}),De=Object.assign(Ee,{setup(y){const{proxy:b}=we(),Y=fe(),D=d([]),L=d(!1),V=d(!0),i=B({pageNum:1,pageSize:10,sort:"id",sortType:"desc",fromEmail:void 0,subject:void 0,addTime:void 0,isSend:""}),g=d([{visible:!1,prop:"id",label:"Id"},{visible:!0,prop:"fromEmail",label:"发送邮箱"},{visible:!0,prop:"subject",label:"邮件主题"},{visible:!0,prop:"toEmails",label:"接收邮箱"},{visible:!1,prop:"emailContent",label:"邮件内容"},{visible:!0,prop:"addTime",label:"发送时间"}]),q=d(0),x=d([]),Q=d(),F=d([new Date(2e3,1,1,0,0,0),new Date(2e3,2,1,23,59,59)]),h=d([]);function w(){b.addDateRange(i,h.value,"AddTime"),L.value=!0,Ce(i).then(n=>{const{code:e,data:c}=n;e==200&&(x.value=c.result,q.value=c.totalNum,L.value=!1)})}function E(){i.pageNum=1,w()}function G(){h.value=[],b.resetForm("queryRef"),E()}function J(n){D.value=n.map(e=>e.id),le.value=n.length!=1,N.value=!n.length}function K(n){var e=void 0,c=void 0;n.prop!=null&&n.order!=null&&(e=n.prop,c=n.order),i.sort=e,i.sortType=c,E()}const W=d(),X=d(0),C=d(!1),Z=B({single:!0,multiple:!0,form:{},rules:{id:[{required:!0,message:"Id不能为空",trigger:"blur"}]},options:{isSendOptions:[{dictLabel:"已送出",dictValue:"1",listClass:"info"},{dictLabel:"未发送",dictValue:"0"}]}}),{form:$,options:ee,single:le,multiple:N}=ce(Z);function I(){C.value=!1}function te(n){$.value=n,C.value=!0}function oe(){Y.push({name:"sendemail"})}function M(n){const e=n.id!=null?[n.id]:D.value;b.$modal.loading("发送中..."),ke({idArr:e}).then(()=>{w(),b.$modal.msgSuccess("发送成功")}).finally(()=>{b.$modal.closeLoading()})}function O(n){const e=n.id||D.value;b.$confirm('是否确认删除参数编号为"'+e+'"的数据项？').then(function(){return Se(e)}).then(()=>{w(),b.$modal.msgSuccess("删除成功")})}return E(),(n,e)=>{const c=s("el-input"),k=s("el-form-item"),R=s("el-radio"),ne=s("el-radio-group"),ae=s("el-date-picker"),p=s("el-button"),P=s("el-form"),j=s("el-col"),ie=s("right-toolbar"),se=s("el-row"),m=s("el-table-column"),re=s("DictTag"),de=s("el-table"),ue=s("pagination"),me=s("zr-dialog"),S=H("hasPermi"),pe=H("loading");return r(),be("div",null,[_(t(P,{model:l(i),"label-position":"right",inline:"",ref_key:"queryRef",ref:Q,onSubmit:e[4]||(e[4]=_e(()=>{},["prevent"]))},{default:o(()=>[t(k,{label:"发送邮箱",prop:"fromEmail"},{default:o(()=>[t(c,{modelValue:l(i).fromEmail,"onUpdate:modelValue":e[0]||(e[0]=a=>l(i).fromEmail=a),placeholder:"请输入发送邮箱"},null,8,["modelValue"])]),_:1}),t(k,{label:"邮件主题",prop:"subject"},{default:o(()=>[t(c,{modelValue:l(i).subject,"onUpdate:modelValue":e[1]||(e[1]=a=>l(i).subject=a),placeholder:"请输入邮件主题"},null,8,["modelValue"])]),_:1}),t(k,{label:"是否送出",prop:"isSend"},{default:o(()=>[t(ne,{modelValue:l(i).isSend,"onUpdate:modelValue":e[2]||(e[2]=a=>l(i).isSend=a)},{default:o(()=>[t(R,{label:"全部",value:""}),t(R,{label:"是",value:1}),t(R,{label:"否",value:0})]),_:1},8,["modelValue"])]),_:1}),t(k,{label:"发送时间"},{default:o(()=>[t(ae,{modelValue:l(h),"onUpdate:modelValue":e[3]||(e[3]=a=>z(h)?h.value=a:null),type:"datetimerange","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD HH:mm:ss","default-time":l(F),shortcuts:n.dateOptions},null,8,["modelValue","default-time","shortcuts"])]),_:1}),t(k,null,{default:o(()=>[t(p,{icon:"search",type:"primary",onClick:E},{default:o(()=>[f(T(n.$t("btn.search")),1)]),_:1}),t(p,{icon:"refresh",onClick:G},{default:o(()=>[f(T(n.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[ge,l(V)]]),t(se,{gutter:15,class:"mb10"},{default:o(()=>[t(j,{span:1.5},{default:o(()=>[_((r(),u(p,{type:"primary",plain:"",icon:"plus",onClick:oe},{default:o(()=>e[9]||(e[9]=[f(" 发送邮件 ")])),_:1})),[[S,["tool:email:send"]]])]),_:1}),t(j,{span:1.5},{default:o(()=>[_((r(),u(p,{type:"primary",disabled:l(N),plain:"",icon:"upload",onClick:M},{default:o(()=>e[10]||(e[10]=[f(" 批量发送 ")])),_:1},8,["disabled"])),[[S,["tool:email:send"]]])]),_:1}),t(j,{span:1.5},{default:o(()=>[_((r(),u(p,{type:"danger",disabled:l(N),plain:"",icon:"delete",onClick:O},{default:o(()=>[f(T(n.$t("btn.delete")),1)]),_:1},8,["disabled"])),[[S,["emaillog:delete"]]])]),_:1}),t(ie,{showSearch:l(V),"onUpdate:showSearch":e[5]||(e[5]=a=>z(V)?V.value=a:null),onQueryTable:w,columns:l(g)},null,8,["showSearch","columns"])]),_:1}),_((r(),u(de,{data:l(x),ref:"table",border:"","header-cell-class-name":"el-table-header-cell","highlight-current-row":"",onSortChange:K,onSelectionChange:J},{default:o(()=>[t(m,{type:"selection",width:"50",align:"center"}),l(g).showColumn("id")?(r(),u(m,{key:0,prop:"id",label:"Id",align:"center"})):v("",!0),t(m,{prop:"fromName",label:"发送人"}),l(g).showColumn("fromEmail")?(r(),u(m,{key:1,prop:"fromEmail",label:"发送邮箱","show-overflow-tooltip":!0})):v("",!0),l(g).showColumn("subject")?(r(),u(m,{key:2,prop:"subject",label:"邮件主题","show-overflow-tooltip":!0})):v("",!0),l(g).showColumn("toEmails")?(r(),u(m,{key:3,prop:"toEmails",label:"接收邮箱","show-overflow-tooltip":!0})):v("",!0),l(g).showColumn("emailContent")?(r(),u(m,{key:4,prop:"emailContent",label:"邮件内容","show-overflow-tooltip":!0})):v("",!0),t(m,{prop:"sendResult",label:"发送结果","show-overflow-tooltip":!0}),l(g).showColumn("addTime")?(r(),u(m,{key:5,prop:"addTime",label:"发送时间","show-overflow-tooltip":!0})):v("",!0),t(m,{prop:"isSend",label:"是否发送",align:"center"},{default:o(({row:a})=>[t(re,{options:l(ee).isSendOptions,value:a.isSend},null,8,["options","value"])]),_:1}),t(m,{label:"操作",width:"200"},{default:o(a=>[t(p,{type:"primary",size:"small",icon:"view",text:"",onClick:A=>te(a.row)},{default:o(()=>e[11]||(e[11]=[f(" 预览 ")])),_:2},1032,["onClick"]),_((r(),u(p,{type:"danger",size:"small",icon:"delete",text:"",onClick:A=>O(a.row)},{default:o(()=>e[12]||(e[12]=[f(" 删除 ")])),_:2},1032,["onClick"])),[[S,["emaillog:delete"]]]),a.row.isSend==0?_((r(),u(p,{key:0,type:"primary",size:"small",icon:"upload",text:"",onClick:A=>M(a.row)},{default:o(()=>e[13]||(e[13]=[f(" 发送 ")])),_:2},1032,["onClick"])),[[S,["tool:email:send"]]]):v("",!0)]),_:1})]),_:1},8,["data"])),[[pe,l(L)]]),t(ue,{total:l(q),page:l(i).pageNum,"onUpdate:page":e[6]||(e[6]=a=>l(i).pageNum=a),limit:l(i).pageSize,"onUpdate:limit":e[7]||(e[7]=a=>l(i).pageSize=a),onPagination:w},null,8,["total","page","limit"]),t(me,{title:"预览","lock-scroll":!1,modelValue:l(C),"onUpdate:modelValue":e[8]||(e[8]=a=>z(C)?C.value=a:null),onClose:I},ve({default:o(()=>[t(P,{ref_key:"formRef",ref:W,model:l($),"label-width":"100px"},{default:o(()=>[ye("div",{innerHTML:l($).emailContent},null,8,Ve)]),_:1},8,["model"])]),_:2},[l(X)!=3?{name:"footer",fn:o(()=>[t(p,{text:"",onClick:I},{default:o(()=>[f(T(n.$t("btn.cancel")),1)]),_:1})]),key:"0"}:void 0]),1032,["modelValue"])])}}});export{De as default};
