import{r,E as O,a5 as ue,x as s,N as Q,o as p,c as j,O as T,S as se,k as e,i as t,l as o,D as k,t as _,A as de,p as v,m as E,q as b,a6 as me,F as pe,j as A,s as ce,v as fe}from"./index-D9f5ARRd.js";import{l as ve,g as ge,u as _e,a as be,d as ye}from"./emailtpl-BOQems10.js";import{_ as he}from"./index-salnjS8b.js";const we={style:{"text-align":"center"},class:"mb10"},ke=["innerHTML"],Ce=ce({name:"emailtpl"}),Be=Object.assign(Ce,{setup(Te){const{proxy:g}=fe(),P=r([]),N=r(!1),V=r(!0),i=O({pageNum:1,pageSize:10,sort:"Id",sortType:"desc",name:void 0}),y=r([{visible:!0,prop:"content",label:"模板内容"},{visible:!0,prop:"createBy",label:"创建人"},{visible:!0,prop:"createTime",label:"创建时间"},{visible:!1,prop:"updateBy",label:"更新人"},{visible:!1,prop:"updateTime",label:"更新时间"}]),D=r(0),F=r([]),H=r();function h(){N.value=!0,ve(i).then(n=>{const{code:l,data:m}=n;l==200&&(F.value=m.result,D.value=m.totalNum,N.value=!1)})}function S(){i.pageNum=1,h()}function G(){g.resetForm("queryRef"),S()}function J(n){var l=void 0,m=void 0;n.prop!=null&&n.order!=null&&(l=n.prop,m=n.order),i.sort=l,i.sortType=m,S()}const K=r(),R=r(""),$=r(0),c=r(!1),W=O({single:!0,multiple:!0,form:{},rules:{name:[{required:!0,message:"Name不能为空",trigger:"blur"}],content:[{required:!0,message:"模板内容不能为空",trigger:"blur"}]},options:{}}),{form:d,rules:X}=ue(W);function Y(){c.value=!1,U()}function U(){d.value={id:null,name:null,content:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},g.resetForm("formRef")}function Z(){U(),c.value=!0,R.value="添加邮件模板",$.value=1}function ee(n){U();const l=n.id||P.value;ge(l).then(m=>{const{code:w,data:u}=m;w==200&&(c.value=!0,R.value="修改邮件模板",$.value=2,d.value={...u})})}function le(){g.$refs.formRef.validate(n=>{n&&(d.value.id!=null&&$.value===2?_e(d.value).then(l=>{g.$modal.msgSuccess("修改成功"),c.value=!1,h()}):be(d.value).then(l=>{g.$modal.msgSuccess("新增成功"),c.value=!1,h()}))})}function te(n){const l=n.id||P.value;g.$confirm('是否确认删除参数编号为"'+l+'"的数据项？').then(function(){return ye(l)}).then(()=>{h(),g.$modal.msgSuccess("删除成功")})}const C=r(!1),B=r(void 0),oe=function(n){C.value=!0,B.value={...n}};return S(),(n,l)=>{const m=s("el-input"),w=s("el-form-item"),u=s("el-button"),x=s("el-form"),z=s("el-col"),ne=s("right-toolbar"),I=s("el-row"),f=s("el-table-column"),ae=s("el-table"),re=s("pagination"),L=s("zr-dialog"),q=Q("hasPermi"),ie=Q("loading");return p(),j("div",null,[T(t(x,{model:e(i),"label-position":"right",inline:"",ref_key:"queryRef",ref:H,onSubmit:l[1]||(l[1]=de(()=>{},["prevent"]))},{default:o(()=>[t(w,{label:"名称",prop:"name"},{default:o(()=>[t(m,{modelValue:e(i).name,"onUpdate:modelValue":l[0]||(l[0]=a=>e(i).name=a),placeholder:"请输入名称"},null,8,["modelValue"])]),_:1}),t(w,null,{default:o(()=>[t(u,{icon:"search",type:"primary",onClick:S},{default:o(()=>[k(_(n.$t("btn.search")),1)]),_:1}),t(u,{icon:"refresh",onClick:G},{default:o(()=>[k(_(n.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[se,e(V)]]),t(I,{gutter:15,class:"mb10"},{default:o(()=>[t(z,{span:1.5},{default:o(()=>[T((p(),v(u,{type:"primary",plain:"",icon:"plus",onClick:Z},{default:o(()=>[k(_(n.$t("btn.add")),1)]),_:1})),[[q,["tool:emailtpl:add"]]])]),_:1}),t(ne,{showSearch:e(V),"onUpdate:showSearch":l[2]||(l[2]=a=>E(V)?V.value=a:null),onQueryTable:h,columns:e(y)},null,8,["showSearch","columns"])]),_:1}),T((p(),v(ae,{data:e(F),ref:"table",border:"","header-cell-class-name":"el-table-header-cell","highlight-current-row":"",onSortChange:J},{default:o(()=>[t(f,{prop:"id",label:"Id",align:"center"}),t(f,{prop:"name",label:"名称",align:"center","show-overflow-tooltip":!0}),e(y).showColumn("content")?(p(),v(f,{key:0,prop:"content",label:"模板内容",align:"center","show-overflow-tooltip":!0})):b("",!0),e(y).showColumn("createBy")?(p(),v(f,{key:1,prop:"createBy",label:"创建人",align:"center","show-overflow-tooltip":!0})):b("",!0),e(y).showColumn("createTime")?(p(),v(f,{key:2,prop:"createTime",label:"创建时间","show-overflow-tooltip":!0})):b("",!0),e(y).showColumn("updateBy")?(p(),v(f,{key:3,prop:"updateBy",label:"更新人",align:"center","show-overflow-tooltip":!0})):b("",!0),e(y).showColumn("updateTime")?(p(),v(f,{key:4,prop:"updateTime",label:"更新时间","show-overflow-tooltip":!0})):b("",!0),t(f,{label:"操作",width:"180"},{default:o(a=>[t(u,{icon:"view",size:"small",type:"primary",onClick:M=>oe(a.row)},null,8,["onClick"]),T(t(u,{type:"success",size:"small",icon:"edit",title:"编辑",onClick:M=>ee(a.row)},null,8,["onClick"]),[[q,["tool:emailtpl:edit"]]]),T(t(u,{type:"danger",size:"small",icon:"delete",title:"删除",onClick:M=>te(a.row)},null,8,["onClick"]),[[q,["tool:emailtpl:delete"]]])]),_:1})]),_:1},8,["data"])),[[ie,e(N)]]),t(re,{total:e(D),page:e(i).pageNum,"onUpdate:page":l[3]||(l[3]=a=>e(i).pageNum=a),limit:e(i).pageSize,"onUpdate:limit":l[4]||(l[4]=a=>e(i).pageSize=a),onPagination:h},null,8,["total","page","limit"]),t(L,{title:e(R),"lock-scroll":!1,modelValue:e(c),"onUpdate:modelValue":l[7]||(l[7]=a=>E(c)?c.value=a:null)},me({default:o(()=>[t(x,{ref_key:"formRef",ref:K,model:e(d),rules:e(X),"label-width":"100px"},{default:o(()=>[t(I,{gutter:20},{default:o(()=>[t(z,{lg:24},{default:o(()=>[t(w,{label:"模板名称",prop:"name"},{default:o(()=>[t(m,{modelValue:e(d).name,"onUpdate:modelValue":l[5]||(l[5]=a=>e(d).name=a),placeholder:"请输入模板名称"},null,8,["modelValue"])]),_:1})]),_:1}),t(z,{lg:24},{default:o(()=>[t(w,{label:"模板内容",prop:"content"},{default:o(()=>[e(c)?(p(),v(e(he),{key:0,modelValue:e(d).content,"onUpdate:modelValue":l[6]||(l[6]=a=>e(d).content=a)},null,8,["modelValue"])):b("",!0)]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:2},[e($)!=3?{name:"footer",fn:o(()=>[t(u,{text:"",onClick:Y},{default:o(()=>[k(_(n.$t("btn.cancel")),1)]),_:1}),t(u,{type:"primary",onClick:le},{default:o(()=>[k(_(n.$t("btn.submit")),1)]),_:1})]),key:"0"}:void 0]),1032,["title","modelValue"]),t(L,{title:"预览",draggable:"",modelValue:e(C),"onUpdate:modelValue":l[9]||(l[9]=a=>E(C)?C.value=a:null),width:"580px"},{footer:o(()=>[t(u,{type:"primary",onClick:l[8]||(l[8]=a=>C.value=!1)},{default:o(()=>[k(_(n.$t("btn.submit")),1)]),_:1})]),default:o(()=>[e(B)?(p(),j(pe,{key:0},[A("div",we,_(e(B).name),1),A("div",{innerHTML:e(B).content},null,8,ke)],64)):b("",!0)]),_:1},8,["modelValue"])])}}});export{Be as default};
