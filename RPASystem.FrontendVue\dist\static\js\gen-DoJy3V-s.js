import{ai as e}from"./index-D9f5ARRd.js";function r(){return e({url:"tool/gen/getDbList",method:"get"})}function a(t){return e({url:"tool/gen/getTableList",method:"get",params:t})}async function l(t){return await e({url:"tool/gen/genCode",method:"POST",data:t})}function u(t){return e({url:"tool/gen/Column/"+t,method:"GET"})}function s(t){return e({url:"tool/gen/list",method:"get",params:t})}function i(t){return e({url:"/tool/gen/"+t,method:"get"})}function d(t){return e({url:"/tool/gen/importTable",method:"post",data:t})}function g(t){return e({url:"/tool/gen/"+t,method:"delete"})}function m(t){return e({url:"/tool/gen/",method:"put",data:t})}function c(t,o){return e({url:"/tool/gen/preview/"+t,method:"post",params:o})}function p(t,o){return e({url:"/tool/gen/synchDb/"+t,method:"get",params:o})}export{s as a,l as b,r as c,g as d,i as g,d as i,a as l,c as p,u as q,p as s,m as u};
