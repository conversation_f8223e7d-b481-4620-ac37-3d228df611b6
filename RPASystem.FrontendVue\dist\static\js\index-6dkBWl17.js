import{_ as x,r as R,w as V,e as N,aw as u,x as n,o as s,c as t,j as _,F as d,K as $,i as a,l as i,q as m,t as F,k as l,p as O}from"./index-D9f5ARRd.js";const S={class:"tool-wrap"},U=["onMouseenter","onMouseleave"],b=["onClick"],j={class:"title"},q={__name:"index",props:{modelValue:{type:Boolean,default:!1}},setup(p){const v=p,f=R(!1);V(()=>v.modelValue,e=>{f.value=e},{immediate:!0});const c=N(()=>u().commonlyUsedRoutes);function h(e){u().removeCommonlyUsedRoutes(e)}function k(e){e.hidden=!0}function C(e){e.hidden=!1}return(e,z)=>{const y=n("CloseBold"),B=n("el-icon"),M=n("svg-icon"),g=n("router-link"),w=n("el-empty");return s(),t(d,null,[_("div",S,[(s(!0),t(d,null,$(l(c),o=>(s(),t("div",{class:"tool-item",onMouseenter:r=>k(o),onMouseleave:r=>C(o)},[o.hidden?(s(),t("span",{key:0,class:"close-used",onClick:r=>h(o)},[a(B,null,{default:i(()=>[a(y)]),_:1})],8,b)):m("",!0),a(g,{to:o.path},{default:i(()=>[a(M,{name:o.icon,"class-name":"card-panel-icon mb10",color:o.color},null,8,["name","color"]),_("div",j,F(o.menuTitle),1)]),_:2},1032,["to"])],40,U))),256))]),l(c)&&l(c).length<=0?(s(),O(w,{key:0,"image-size":80})):m("",!0)],64)}}},E=x(q,[["__scopeId","data-v-5943e96e"]]);export{E as default};
