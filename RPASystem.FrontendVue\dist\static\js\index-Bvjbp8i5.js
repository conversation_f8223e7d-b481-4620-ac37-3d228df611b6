import{ai as S,r as b,aL as z,M as x,aa as R,o as a,c as o,V as B,B as N,v as w,x as y,i as c,l as i,j as e,k as t,t as d,q as n,p as g,s as O}from"./index-D9f5ARRd.js";import{i as C}from"./index-BtiuLXK9.js";function U(){return S({url:"/monitor/cache",method:"get"})}const k={__name:"Gauge",props:{name:{type:String,default:""},min:{type:[Object,Number],default:0},max:{type:[Object,Number],default:0},data:{type:Array,default:()=>[{value:"",name:"占比"}]},className:{type:String,default:"chart"},width:{type:String,default:"100%"},height:{type:String,default:"300px"}},setup(r){const{proxy:v}=w(),l=r;let _=null;const p=b(),h=()=>{_.resize()};function u(){_=C(v.$refs.chartRef,"macarons"),_.setOption({tooltip:{formatter:"{a} <br/>{b} : {c}"},series:[{name:l.name,type:"gauge",min:l.min,max:l.max,detail:{formatter:"{value}%"},data:l.data}]})}const s=z(()=>{h()},500);return document.addEventListener("resize",s),x(()=>{u()}),R(()=>{window.removeEventListener("resize",s)}),(f,m)=>(a(),o("div",{ref_key:"chartRef",ref:p,class:B(r.className),style:N({height:r.height,width:r.width})},null,6))}},j={class:"app-container"},D={class:"el-table el-table--enable-row-hover el-table--medium"},E={cellspacing:"0",style:{width:"100%"}},L={class:"el-table__cell is-leaf"},P={key:0,class:"cell"},V={class:"el-table__cell is-leaf"},$={key:0,class:"cell"},A={class:"el-table__cell is-leaf"},I={key:0,class:"cell"},M={class:"el-table__cell is-leaf"},q={key:0,class:"cell"},G={class:"el-table__cell is-leaf"},H={key:0,class:"cell"},K={class:"el-table__cell is-leaf"},T={key:0,class:"cell"},J={class:"el-table__cell is-leaf"},Q={key:0,class:"cell"},W={class:"el-table__cell is-leaf"},X={key:0,class:"cell"},Y={class:"el-table__cell is-leaf"},Z={key:0,class:"cell"},ee={class:"el-table__cell is-leaf"},le={key:0,class:"cell"},se={class:"el-table__cell is-leaf"},te={key:0,class:"cell"},ae={class:"el-table__cell is-leaf"},ne={key:0,class:"cell"},oe={class:"el-table el-table--enable-row-hover el-table--medium"},ie={class:"el-table el-table--enable-row-hover el-table--medium"},de={class:"el-table el-table--enable-row-hover el-table--medium"},ce=O({name:"cache"}),ue=Object.assign(ce,{setup(r){const v=b(null);b(null);const l=b([]),_=b(null),{proxy:p}=w();function h(){U().then(u=>{l.value=u.data,v.value=C(p.$refs.commandstatsRef),v.value.setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},series:[{name:"命令",type:"pie",roseType:"radius",radius:[15,95],center:["50%","38%"],data:u.data.commandStats,animationEasing:"cubicInOut",animationDuration:1e3}]})})}return x(()=>{h()}),(u,s)=>{const f=y("el-card"),m=y("el-col"),F=y("el-row");return a(),o("div",j,[c(F,null,{default:i(()=>[c(m,{lg:24,class:"card-box"},{default:i(()=>[c(f,null,{header:i(()=>s[0]||(s[0]=[e("div",{class:"card-header"},[e("span",null,"基本信息")],-1)])),default:i(()=>[e("div",D,[e("table",E,[e("tbody",null,[e("tr",null,[s[1]||(s[1]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"Redis版本")],-1)),e("td",L,[t(l).info?(a(),o("div",P,d(t(l).info.redis_version),1)):n("",!0)]),s[2]||(s[2]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"运行模式")],-1)),e("td",V,[t(l).info?(a(),o("div",$,d(t(l).info.redis_mode=="standalone"?"单机":"集群"),1)):n("",!0)]),s[3]||(s[3]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"端口")],-1)),e("td",A,[t(l).info?(a(),o("div",I,d(t(l).info.tcp_port),1)):n("",!0)]),s[4]||(s[4]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"客户端数")],-1)),e("td",M,[t(l).info?(a(),o("div",q,d(t(l).info.connected_clients),1)):n("",!0)])]),e("tr",null,[s[5]||(s[5]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"运行时间(天)")],-1)),e("td",G,[t(l).info?(a(),o("div",H,d(t(l).info.uptime_in_days),1)):n("",!0)]),s[6]||(s[6]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"使用内存")],-1)),e("td",K,[t(l).info?(a(),o("div",T,d(t(l).info.used_memory_human),1)):n("",!0)]),s[7]||(s[7]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"使用CPU")],-1)),e("td",J,[t(l).info?(a(),o("div",Q,d(parseFloat(t(l).info.used_cpu_user_children).toFixed(2)),1)):n("",!0)]),s[8]||(s[8]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"内存配置")],-1)),e("td",W,[t(l).info?(a(),o("div",X,d(t(l).info.maxmemory_human),1)):n("",!0)])]),e("tr",null,[s[9]||(s[9]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"AOF是否开启")],-1)),e("td",Y,[t(l).info?(a(),o("div",Z,d(t(l).info.aof_enabled=="0"?"否":"是"),1)):n("",!0)]),s[10]||(s[10]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"RDB是否成功")],-1)),e("td",ee,[t(l).info?(a(),o("div",le,d(t(l).info.rdb_last_bgsave_status),1)):n("",!0)]),s[11]||(s[11]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"Key数量")],-1)),e("td",se,[t(l).dbSize?(a(),o("div",te,d(t(l).dbSize),1)):n("",!0)]),s[12]||(s[12]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"网络入口/出口")],-1)),e("td",ae,[t(l).info?(a(),o("div",ne,d(t(l).info.instantaneous_input_kbps)+"kps/"+d(t(l).info.instantaneous_output_kbps)+"kps ",1)):n("",!0)])])])])])]),_:1})]),_:1}),c(m,{lg:8,class:"card-box"},{default:i(()=>[c(f,null,{header:i(()=>s[13]||(s[13]=[e("div",{class:"card-header"},[e("span",null,"命令统计")],-1)])),default:i(()=>[e("div",oe,[e("div",{ref_key:"commandstatsRef",ref:_,style:{height:"300px"}},null,512)])]),_:1})]),_:1}),t(l).info?(a(),g(m,{key:0,lg:8,class:"card-box"},{default:i(()=>[c(f,null,{header:i(()=>s[14]||(s[14]=[e("div",{class:"card-header"},[e("span",null,"内存消耗")],-1)])),default:i(()=>[e("div",ie,[c(k,{name:"内存消耗",max:100,data:[{value:(parseFloat(t(l).info.used_memory_human)/parseFloat(t(l).info.total_system_memory_human)).toFixed(2),name:"内存消耗"}]},null,8,["data"])])]),_:1})]),_:1})):n("",!0),t(l).info?(a(),g(m,{key:1,lg:8,class:"card-box"},{default:i(()=>[c(f,null,{header:i(()=>s[15]||(s[15]=[e("div",{class:"card-header"},[e("span",null,"使用CPU")],-1)])),default:i(()=>[e("div",de,[c(k,{name:"CPU",max:100,data:[{value:parseFloat(t(l).info.used_cpu_user_children*100).toFixed(0),name:"CPU消耗"}]},null,8,["data"])])]),_:1})]),_:1})):n("",!0)]),_:1})])}}});export{ue as default};
