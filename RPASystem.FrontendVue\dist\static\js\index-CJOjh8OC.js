import{r as b,aG as H,E as ye,a5 as ve,w as we,x as d,N as G,o as n,c as B,O as V,S as J,k as e,i as o,l,z as j,m as $,D as m,F as W,K as X,p as s,q as c,j as R,t as g,B as he,a8 as Ve,s as ke,v as Te}from"./index-D9f5ARRd.js";import{l as Ce,d as xe,c as Ne}from"./operlog-DdD4w3NX.js";const Pe={class:"app-container"},Me={class:"text-danger"},De=ke({name:"operlog"}),qe=Object.assign(De,{setup(Re){const{proxy:v}=Te(),q=b(!0),L=b([]),z=b(!0),F=b(!0),S=b(0),I=b([]),T=b(!1),C=b([H().format("YYYY-MM-DD 00:00:00"),H().format("YYYY-MM-DD 23:59:59")]),Z=b([new Date(2e3,1,1,0,0,0),new Date(2e3,2,1,23,59,59)]),Y=ye({form:{},queryParams:{pageNum:1,pageSize:20,title:void 0,operName:void 0,businessType:void 0,status:void 0,operParam:void 0,businessTypes:[]},options:{statusOptions:[],sys_oper_type:[]}}),_=b([{visible:!1,prop:"operId",label:"操作id"},{visible:!0,prop:"title",label:"系统模块"},{visible:!0,prop:"businessType",label:"业务类型"},{visible:!0,prop:"requestMethod",label:"请求方式"},{visible:!0,prop:"operName",label:"操作人员"},{visible:!0,prop:"status",label:"操作状态"},{visible:!0,prop:"operTime",label:"操作时间"},{visible:!1,prop:"method",label:"操作方法"},{visible:!1,prop:"operParam",label:"请求参数"},{visible:!1,prop:"jsonResult",label:"返回结果"},{visible:!0,prop:"errorMsg",label:"日志内容"},{visible:!1,prop:"elapsed",label:"操作用时"}]),{form:r,queryParams:u,options:w}=ve(Y);var ee=[{dictType:"sys_oper_type"},{dictType:"sys_common_status"}];v.getDicts(ee).then(p=>{p.data.forEach(t=>{Y.options[t.dictType]=t.list})});function x(){q.value=!0,Ce(v.addDateRange(u.value,C.value)).then(p=>{q.value=!1,p.code==200?(I.value=p.data.result,S.value=p.data.totalNum):(S.value=0,I.value=[])})}function k(){u.value.pageNum=1,x()}function le(){C.value=[],Y.queryParams.businessTypes=[],v.resetForm("queryForm"),k()}function te(p){L.value=p.map(t=>t.operId),z.value=!p.length}const oe=b();function ae(){r.value={operId:void 0,title:void 0,businessType:void 0,method:void 0,requestMethod:void 0,operatorType:void 0,deptName:void 0,operUrl:void 0,operIp:void 0,operLocation:void 0,operParam:void 0,jsonResult:void 0,status:0,errorMsg:void 0,operTime:void 0,elapsed:0},v.resetForm("formRef")}function ne(p){ae(),T.value=!0,r.value=p}function O(p){const t=p.operId||L.value;v.$confirm('是否确认删除日志编号为"'+t+'"的数据项?',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return xe(t)}).then(()=>{x(),v.$modal.msgSuccess("删除成功")})}function se(){v.$confirm("是否确认清空所有操作日志数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return Ne()}).then(()=>{x(),v.$modal.msgSuccess("清空成功")})}function re(){v.$confirm("是否确认导出所有操作日志?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await v.downFile("/monitor/OperLog/export",{...u.value})})}const N=b(!1),P=b(!1);function ue(p){P.value=!1,p?u.value.businessTypes=w.value.sys_oper_type.map(t=>t.dictValue):u.value.businessTypes=[]}return we(()=>u.value.businessTypes,p=>{p.length===0?(N.value=!1,P.value=!1):p.length===w.value.sys_oper_type.length?(N.value=!0,P.value=!1):P.value=!0}),k(),(p,t)=>{const M=d("el-input"),i=d("el-form-item"),pe=d("el-checkbox"),K=d("el-option"),E=d("el-select"),ie=d("el-date-picker"),h=d("el-button"),A=d("el-form"),y=d("el-col"),de=d("right-toolbar"),Q=d("el-row"),f=d("el-table-column"),U=d("dict-tag"),me=d("el-table"),fe=d("pagination"),ce=d("el-tag"),_e=d("el-dialog"),D=G("hasPermi"),be=G("loading");return n(),B("div",Pe,[V(o(A,{model:e(u),ref:"queryForm",inline:!0,"label-width":"68px"},{default:l(()=>[o(i,{label:"系统模块",prop:"title"},{default:l(()=>[o(M,{modelValue:e(u).title,"onUpdate:modelValue":t[0]||(t[0]=a=>e(u).title=a),placeholder:"请输入系统模块",clearable:"",onKeyup:j(k,["enter"])},null,8,["modelValue"])]),_:1}),o(i,{label:"操作人员",prop:"operName"},{default:l(()=>[o(M,{modelValue:e(u).operName,"onUpdate:modelValue":t[1]||(t[1]=a=>e(u).operName=a),placeholder:"请输入操作人员",clearable:"",onKeyup:j(k,["enter"])},null,8,["modelValue"])]),_:1}),o(i,{label:"业务类型"},{default:l(()=>[o(E,{modelValue:e(u).businessTypes,"onUpdate:modelValue":t[3]||(t[3]=a=>e(u).businessTypes=a),"collapse-tags":"","collapse-tags-tooltip":"",multiple:"",placeholder:"操作类型",clearable:""},{header:l(()=>[o(pe,{modelValue:e(N),"onUpdate:modelValue":t[2]||(t[2]=a=>$(N)?N.value=a:null),indeterminate:e(P),onChange:ue},{default:l(()=>t[13]||(t[13]=[m(" All ")])),_:1},8,["modelValue","indeterminate"])]),default:l(()=>[(n(!0),B(W,null,X(e(w).sys_oper_type,a=>(n(),s(K,{key:a.dictValue,label:a.dictLabel,value:a.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(i,{label:"操作状态",prop:"status"},{default:l(()=>[o(E,{modelValue:e(u).status,"onUpdate:modelValue":t[4]||(t[4]=a=>e(u).status=a),placeholder:"操作状态",clearable:""},{default:l(()=>[(n(!0),B(W,null,X(e(w).sys_common_status,a=>(n(),s(K,{key:a.dictValue,label:a.dictLabel,value:a.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(i,{label:"请求参数",prop:"operParam"},{default:l(()=>[o(M,{modelValue:e(u).operParam,"onUpdate:modelValue":t[5]||(t[5]=a=>e(u).operParam=a),placeholder:"请输入请求参数",clearable:"",onKeyup:j(k,["enter"])},null,8,["modelValue"])]),_:1}),o(i,{label:"操作时间"},{default:l(()=>[o(ie,{modelValue:e(C),"onUpdate:modelValue":t[6]||(t[6]=a=>$(C)?C.value=a:null),type:"datetimerange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","default-time":e(Z),"value-format":"YYYY-MM-DD HH:mm:ss",shortcuts:p.dateOptions},null,8,["modelValue","default-time","shortcuts"])]),_:1}),o(i,null,{default:l(()=>[o(h,{type:"primary",icon:"search",onClick:k},{default:l(()=>t[14]||(t[14]=[m("搜索")])),_:1}),o(h,{icon:"refresh",onClick:le},{default:l(()=>t[15]||(t[15]=[m("重置")])),_:1})]),_:1})]),_:1},8,["model"]),[[J,e(F)]]),o(Q,{gutter:10,class:"mb8"},{default:l(()=>[o(y,{span:1.5},{default:l(()=>[V((n(),s(h,{type:"danger",plain:"",icon:"delete",disabled:e(z),onClick:O},{default:l(()=>t[16]||(t[16]=[m(" 删除 ")])),_:1},8,["disabled"])),[[D,["monitor:operlog:remove"]]])]),_:1}),o(y,{span:1.5},{default:l(()=>[V((n(),s(h,{type:"danger",plain:"",icon:"delete",onClick:se},{default:l(()=>t[17]||(t[17]=[m("清空")])),_:1})),[[D,["monitor:operlog:remove"]]])]),_:1}),o(y,{span:1.5},{default:l(()=>[V((n(),s(h,{type:"warning",plain:"",icon:"download",onClick:re},{default:l(()=>t[18]||(t[18]=[m("导出 ")])),_:1})),[[D,["system:operlog:export"]]])]),_:1}),o(de,{showSearch:e(F),columns:e(_),onQueryTable:x},null,8,["showSearch","columns"])]),_:1}),V((n(),s(me,{border:"",data:e(I),onSelectionChange:te},{default:l(()=>[o(f,{type:"selection",width:"50",align:"center"}),e(_).showColumn("operId")?(n(),s(f,{key:0,label:"操作id",align:"center",prop:"operId",width:"60px","show-overflow-tooltip":!0})):c("",!0),e(_).showColumn("title")?(n(),s(f,{key:1,label:"系统模块",align:"center",prop:"title","show-overflow-tooltip":!0})):c("",!0),e(_).showColumn("businessType")?(n(),s(f,{key:2,prop:"businessType",label:"业务类型",align:"center"},{default:l(a=>[o(U,{options:e(w).sys_oper_type,value:a.row.businessType},null,8,["options","value"])]),_:1})):c("",!0),e(_).showColumn("requestMethod")?(n(),s(f,{key:3,label:"请求方法",align:"center",prop:"requestMethod"})):c("",!0),e(_).showColumn("operName")?(n(),s(f,{key:4,label:"操作人员",align:"center",prop:"operName"})):c("",!0),o(f,{label:"操作地址",prop:"operIp",width:"120"},{default:l(({row:a})=>[R("div",null,g(a.operLocation),1),R("div",null,g(a.operIp),1)]),_:1}),e(_).showColumn("status")?(n(),s(f,{key:5,label:"操作状态",align:"center",prop:"status"},{default:l(({row:a})=>[o(U,{options:e(w).sys_common_status,value:a.status},null,8,["options","value"])]),_:1})):c("",!0),e(_).showColumn("elapsed")?(n(),s(f,{key:6,label:"用时",align:"center",prop:"elapsed"},{default:l(a=>[R("span",{style:he(a.row.elapsed<1e3?"color:green":a.row.elapsed<3e3?"color:orange":"color:red")},g(a.row.elapsed)+" ms ",5)]),_:1})):c("",!0),e(_).showColumn("errorMsg")?(n(),s(f,{key:7,label:"日志内容",prop:"errorMsg",width:"220"})):c("",!0),e(_).showColumn("operTime")?(n(),s(f,{key:8,label:"操作日期",prop:"operTime",width:"100"},{default:l(a=>[R("span",null,g(e(Ve)(a.row.operTime)),1)]),_:1})):c("",!0),e(_).showColumn("method")?(n(),s(f,{key:9,prop:"method",label:"操作方法",align:"center","show-overflow-tooltip":!0})):c("",!0),e(_).showColumn("operParam")?(n(),s(f,{key:10,prop:"operParam",label:"请求参数",align:"center","show-overflow-tooltip":!0})):c("",!0),e(_).showColumn("jsonResult")?(n(),s(f,{key:11,prop:"jsonResult",label:"返回结果",align:"center","show-overflow-tooltip":!0})):c("",!0),o(f,{label:"操作",align:"center",width:"130"},{default:l(a=>[V((n(),s(h,{size:"small",text:"",icon:"view",onClick:ge=>ne(a.row,a.index)},{default:l(()=>t[19]||(t[19]=[m(" 详细 ")])),_:2},1032,["onClick"])),[[D,["monitor:operlog:query"]]]),V((n(),s(h,{size:"small",text:"",icon:"delete",onClick:ge=>O(a.row)},{default:l(()=>t[20]||(t[20]=[m(" 删除 ")])),_:2},1032,["onClick"])),[[D,["monitor:operlog:remove"]]])]),_:1})]),_:1},8,["data"])),[[be,e(q)]]),V(o(fe,{total:e(S),page:e(u).pageNum,"onUpdate:page":t[7]||(t[7]=a=>e(u).pageNum=a),limit:e(u).pageSize,"onUpdate:limit":t[8]||(t[8]=a=>e(u).pageSize=a),onPagination:x},null,8,["total","page","limit"]),[[J,e(S)>0]]),o(_e,{title:"操作日志详细",modelValue:e(T),"onUpdate:modelValue":t[12]||(t[12]=a=>$(T)?T.value=a:null),width:"700px","append-to-body":""},{footer:l(()=>[o(h,{text:"",onClick:t[11]||(t[11]=a=>T.value=!1)},{default:l(()=>t[21]||(t[21]=[m("关 闭")])),_:1})]),default:l(()=>[o(A,{ref_key:"formRef",ref:oe,model:e(r),"label-width":"100px"},{default:l(()=>[o(Q,null,{default:l(()=>[o(y,{lg:12},{default:l(()=>[o(i,{label:"操作模块："},{default:l(()=>[m(g(e(r).title),1)]),_:1}),o(i,{label:"登录信息："},{default:l(()=>[o(ce,null,{default:l(()=>[m(g(e(r).operName),1)]),_:1}),m(" "+g(e(r).operIp)+" / "+g(e(r).operLocation),1)]),_:1})]),_:1}),o(y,{lg:12},{default:l(()=>[o(i,{label:"请求地址："},{default:l(()=>[m(g(e(r).operUrl),1)]),_:1}),o(i,{label:"请求方式："},{default:l(()=>[m(g(e(r).requestMethod),1)]),_:1})]),_:1}),o(y,{lg:12},{default:l(()=>[o(i,{label:"操作方法："},{default:l(()=>[m(g(e(r).method),1)]),_:1})]),_:1}),o(y,{lg:12},{default:l(()=>[o(i,{label:"操作类型："},{default:l(()=>[o(U,{options:e(w).sys_oper_type,value:e(r).businessType},null,8,["options","value"])]),_:1})]),_:1}),o(y,{lg:12},{default:l(()=>[o(i,{label:"操作状态："},{default:l(()=>[o(U,{options:e(w).sys_common_status,value:e(r).status},null,8,["options","value"])]),_:1})]),_:1}),o(y,{lg:12},{default:l(()=>[o(i,{label:"操作时间："},{default:l(()=>[m(g(p.parseTime(e(r).operTime)),1)]),_:1})]),_:1}),e(r).operParam?(n(),s(y,{key:0,lg:24},{default:l(()=>[o(i,{label:"请求参数："},{default:l(()=>[o(M,{type:"textarea",rows:"5",modelValue:e(r).operParam,"onUpdate:modelValue":t[9]||(t[9]=a=>e(r).operParam=a)},null,8,["modelValue"])]),_:1})]),_:1})):c("",!0),e(r).jsonResult?(n(),s(y,{key:1,lg:24},{default:l(()=>[o(i,{label:"返回结果："},{default:l(()=>[o(M,{type:"textarea",rows:"10",modelValue:e(r).jsonResult,"onUpdate:modelValue":t[10]||(t[10]=a=>e(r).jsonResult=a)},null,8,["modelValue"])]),_:1})]),_:1})):c("",!0),o(y,{lg:24},{default:l(()=>[e(r).status===1?(n(),s(i,{key:0,label:"异常信息："},{default:l(()=>[R("div",Me,g(e(r).errorMsg),1)]),_:1})):c("",!0)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});export{qe as default};
