import{r as m,E as be,a5 as ye,a$ as D,x as a,N as we,o as s,c as y,O as v,S as E,k as o,i as n,l,j as P,t as r,q as V,z as ge,F as J,K as W,p as h,D as c,m as $e,s as ke,v as Ce,b0 as Te,b1 as Ie,b2 as Ne}from"./index-D9f5ARRd.js";import Ve from"./menuForm-Dpyl6GqY.js";import"./index-B8u0KKcX.js";import"./requireIcons-CAZP3y3p.js";const Se={class:"app-container"},Re={key:0},Fe=["onClick"],Me={class:"mt10"},De=ke({name:"sysmenu"}),Be=Object.assign(De,{setup(Ee){const{proxy:p}=Ce();var X=[{dictType:"sys_show_hide"},{dictType:"sys_normal_disable"}];const U=m([]),k=m(!0),C=m(!0),A=m([]),S=m([]),R=m(!1),z=m(null),B=be({form:{},queryParams:{menuName:void 0,visible:void 0,menuTypeIds:"M,C",parentId:void 0},options:{sys_show_hide:[],sys_normal_disable:[]}}),{queryParams:i,options:w}=ye(B);p.getDicts(X).then(e=>{e.data.forEach(u=>{B.options[u.dictType]=u.list})});const F=m([{label:"添加时间",visible:!1,prop:"createTime"},{label:"排序",visible:!0,prop:"orderNum"}]),Y=m(document.documentElement.scrollHeight-245+"px");function M(e){k.value=!0,i.value.parentId!=null||i.value.menuName!=null?i.value.menuTypeIds="":i.value.menuTypeIds="M,C,F",D(i.value).then(u=>{U.value=u.data,e==1&&(S.value=u.data),k.value=!1})}function Z(){D({menuTypeIds:"M,C,F"}).then(e=>{A.value=e.data})}Z();function b(){M()}function x(){p.resetForm("queryRef"),b()}function L(e){p.$refs.menuFormRef.handleAdd(e)}function ee(){R.value=!R.value;const e=z.value;e&&(R.value?e.setAllTreeExpand(!0):e.clearTreeExpand())}function te(e){p.$refs.menuFormRef.handleUpdate(e)}function ne(e){p.$modal.confirm('是否确认删除名称为"'+e.menuName+'"的数据项?').then(function(){return Te(e.menuId)}).then(()=>{I(e.parentId),p.$modal.msgSuccess("删除成功")})}function le(e){p.$modal.confirm('是否确认删除名称为"'+e.menuName+'"的所有数据项?').then(function(){return Ie(e.menuId)}).then(()=>{I(e.parentId),p.$modal.msgSuccess("删除成功")})}const O=m([]),oe=e=>{e&&O.value.push(e)},T=m(-1);function ae(e){T.value=e,setTimeout(()=>{O.value[e].focus()},100)}function ue(e){T.value=-1,p.$confirm("是否保存数据?").then(function(){return Ne({value:e.orderNum,id:e.menuId})}).then(()=>{b(),I(e.parentId),p.$modal.msgSuccess("修改成功")}).catch(()=>{b()})}function I(e){k.value=!0,M()}return D({menuTypeIds:"M,C"}).then(e=>{S.value=e.data}),b(),(e,u)=>{const se=a("el-cascader"),g=a("el-form-item"),q=a("el-input"),Q=a("el-option"),K=a("el-select"),_=a("el-button"),ie=a("el-form"),j=a("el-col"),re=a("right-toolbar"),de=a("el-row"),d=a("vxe-column"),me=a("svg-icon"),N=a("el-tag"),H=a("dict-tag"),ce=a("arrow-down"),pe=a("el-icon"),G=a("el-dropdown-item"),fe=a("el-dropdown-menu"),_e=a("el-dropdown"),he=a("el-button-group"),ve=a("vxe-table"),$=we("hasPermi");return s(),y("div",Se,[v(n(ie,{model:o(i),ref:"queryRef",inline:!0},{default:l(()=>[n(g,{label:e.$t("m.parentMenu"),prop:"parentId"},{default:l(()=>[n(se,{class:"w100",options:o(S),props:{checkStrictly:!0,value:"menuId",label:"menuName",emitPath:!1},placeholder:"请选择上级菜单",clearable:"",modelValue:o(i).parentId,"onUpdate:modelValue":u[0]||(u[0]=t=>o(i).parentId=t)},{default:l(({node:t,data:f})=>[P("span",null,r(f.menuName),1),t.isLeaf?V("",!0):(s(),y("span",Re," ("+r(f.children.length)+") ",1))]),_:1},8,["options","modelValue"])]),_:1},8,["label"]),n(g,{label:e.$t("m.menuName"),prop:"menuName"},{default:l(()=>[n(q,{modelValue:o(i).menuName,"onUpdate:modelValue":u[1]||(u[1]=t=>o(i).menuName=t),placeholder:"请输入菜单名称",clearable:"",onKeyup:ge(b,["enter"])},null,8,["modelValue"])]),_:1},8,["label"]),n(g,{label:e.$t("m.menuState"),prop:"status"},{default:l(()=>[n(K,{modelValue:o(i).status,"onUpdate:modelValue":u[2]||(u[2]=t=>o(i).status=t),placeholder:"菜单状态",clearable:""},{default:l(()=>[(s(!0),y(J,null,W(o(w).sys_normal_disable,t=>(s(),h(Q,{key:t.dictValue,label:t.dictLabel,value:t.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),n(g,{label:e.$t("m.isShow"),prop:"visible"},{default:l(()=>[n(K,{modelValue:o(i).visible,"onUpdate:modelValue":u[3]||(u[3]=t=>o(i).visible=t),placeholder:"显示状态",clearable:""},{default:l(()=>[(s(!0),y(J,null,W(o(w).sys_show_hide,t=>(s(),h(Q,{key:t.dictValue,label:t.dictLabel,value:t.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),n(g,null,{default:l(()=>[n(_,{type:"primary",icon:"Search",onClick:b},{default:l(()=>[c(r(e.$t("btn.search")),1)]),_:1}),n(_,{icon:"Refresh",onClick:x},{default:l(()=>[c(r(e.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[E,o(C)]]),n(de,{gutter:10,class:"mb8"},{default:l(()=>[n(j,{span:1.5},{default:l(()=>[v((s(),h(_,{type:"primary",plain:"",icon:"Plus",onClick:L},{default:l(()=>[c(r(e.$t("btn.add")),1)]),_:1})),[[$,["system:menu:add"]]])]),_:1}),n(j,{span:1.5},{default:l(()=>[n(_,{type:"info",plain:"",icon:"Sort",onClick:ee},{default:l(()=>[c(r(e.$t("btn.expand"))+"/"+r(e.$t("btn.collapse")),1)]),_:1})]),_:1}),n(re,{showSearch:o(C),"onUpdate:showSearch":u[4]||(u[4]=t=>$e(C)?C.value=t:null),onQueryTable:M,columns:o(F)},null,8,["showSearch","columns"])]),_:1}),n(ve,{height:o(Y),"show-overflow":"",ref_key:"listRef",ref:z,size:"small",loading:o(k),"column-config":{resizable:!0},"tree-config":{parentField:"parentId",reserve:!0},"row-config":{keyField:"menuId"},border:!0,"scroll-y":{enabled:!0,gt:20},data:o(U)},{default:l(()=>[n(d,{field:"menuName",title:e.$t("m.menuName"),"tree-node":"",width:"160"},null,8,["title"]),n(d,{field:"menuId",title:e.$t("m.menuid"),width:"90"},null,8,["title"]),n(d,{field:"icon",title:e.$t("m.icon"),align:"center",width:"80"},{default:l(({row:t})=>[n(me,{name:t.icon},null,8,["name"])]),_:1},8,["title"]),n(d,{field:"menuType",title:e.$t("m.menuType"),align:"center",width:"80"},{default:l(t=>[t.row.menuType=="M"&&t.row.isFrame==1?(s(),h(N,{key:0,"disable-transitions":!0,type:"danger"},{default:l(()=>[c(r(e.$t("m.link")),1)]),_:1})):t.row.menuType=="C"?(s(),h(N,{key:1,"disable-transitions":!0},{default:l(()=>[c(r(e.$t("m.menu")),1)]),_:1})):t.row.menuType=="M"?(s(),h(N,{key:2,"disable-transitions":!0,type:"success"},{default:l(()=>[c(r(e.$t("m.directory")),1)]),_:1})):t.row.menuType=="F"?(s(),h(N,{key:3,"disable-transitions":!0,type:"warning"},{default:l(()=>[c(r(e.$t("m.button")),1)]),_:1})):V("",!0)]),_:1},8,["title"]),o(F).showColumn("orderNum")?(s(),h(d,{key:0,field:"orderNum",title:e.$t("m.sort"),width:"110",sortable:"",align:"center"},{default:l(t=>[v(P("span",{onClick:f=>ae(t.row.menuId)},r(t.row.orderNum),9,Fe),[[E,o(T)!=t.row.menuId]]),v(n(q,{ref:oe,modelValue:t.row.orderNum,"onUpdate:modelValue":f=>t.row.orderNum=f,onBlur:f=>ue(t.row)},null,8,["modelValue","onUpdate:modelValue","onBlur"]),[[E,o(T)==t.row.menuId]])]),_:1},8,["title"])):V("",!0),n(d,{field:"perms",title:e.$t("m.authorityID"),"show-overflow":"title"},null,8,["title"]),n(d,{field:"component",title:e.$t("m.componentPath"),"show-overflow":""},null,8,["title"]),n(d,{field:"visible",title:e.$t("m.isShow"),width:"90",align:"center"},{default:l(t=>[n(H,{options:o(w).sys_show_hide,value:t.row.visible},null,8,["options","value"])]),_:1},8,["title"]),n(d,{field:"status",title:e.$t("m.menuState"),width:"80",align:"center"},{default:l(t=>[n(H,{options:o(w).sys_normal_disable,value:t.row.status},null,8,["options","value"])]),_:1},8,["title"]),o(F).showColumn("createTime")?(s(),h(d,{key:1,title:e.$t("common.addTime"),align:"center",field:"createTime","show-overflow":""},{default:l(t=>[P("span",null,r(e.parseTime(t.row.createTime)),1)]),_:1},8,["title"])):V("",!0),n(d,{title:e.$t("btn.operate"),align:"center",width:"140"},{default:l(t=>[n(he,null,{default:l(()=>[v(n(_,{text:"",size:"small",icon:"Plus",onClick:f=>L(t.row)},null,8,["onClick"]),[[$,["system:menu:add"]]]),v(n(_,{text:"",size:"small",icon:"Edit",onClick:f=>te(t.row)},null,8,["onClick"]),[[$,["system:menu:edit"]]]),n(_e,null,{dropdown:l(()=>[n(fe,null,{default:l(()=>[v((s(),y("div",null,[n(G,null,{default:l(()=>[n(_,{icon:"Delete",link:"",onClick:f=>ne(t.row)},{default:l(()=>u[5]||(u[5]=[c("删除当前")])),_:2},1032,["onClick"])]),_:2},1024)])),[[$,["system:menu:remove"]]]),v((s(),y("div",Me,[n(G,null,{default:l(()=>[n(_,{icon:"Delete",type:"danger",link:"",onClick:f=>le(t.row)},{default:l(()=>u[6]||(u[6]=[c(" 删除所有 ")])),_:2},1032,["onClick"])]),_:2},1024)])),[[$,["system:menu:remove"]]])]),_:2},1024)]),default:l(()=>[n(_,{size:"small",text:""},{default:l(()=>[c(r(e.$t("btn.more"))+" ",1),n(pe,{class:"el-icon--right"},{default:l(()=>[n(ce)]),_:1})]),_:1})]),_:2},1024)]),_:2},1024)]),_:1},8,["title"])]),_:1},8,["height","loading","data"]),n(Ve,{ref:"menuFormRef",options:o(w),menuOptions:o(A),onSuccess:I},null,8,["options","menuOptions"])])}}});export{Be as default};
