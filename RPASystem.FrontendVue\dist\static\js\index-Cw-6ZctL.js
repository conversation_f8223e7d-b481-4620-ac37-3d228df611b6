import{r,E as ke,a5 as he,x as d,N as le,o as s,c as h,O as y,S as ae,k as a,i as t,l,z as oe,F as q,K as U,p as _,D as p,t as c,j as z,m as B,s as Te,v as Ce}from"./index-D9f5ARRd.js";import Ne from"./dictData-5kgeFk6a.js";import{l as $e,g as Se,u as qe,a as Ue,d as Ie,e as xe}from"./type-BoO7AbZI.js";const De={class:"app-container"},Fe=Te({name:"dict"}),Ee=Object.assign(Fe,{setup(Le){const{proxy:m}=Ce(),I=r(!0),x=r([]),E=r(!0),O=r(!0),P=r(!0),D=r(0),K=r([]),F=r(""),g=r(!1),T=r(!1),C=r([]),L=r([]),Q=r([]),ne=r(),N=r(0),de=ke({rules:{dictName:[{required:!0,message:"字典名称不能为空",trigger:"blur"}],dictType:[{required:!0,message:"字典类型不能为空",trigger:"blur"}]},form:{},queryParams:{pageNum:1,pageSize:10,dictName:void 0,dictType:void 0,status:void 0}}),{rules:ue,form:u,queryParams:i}=he(de);function w(){I.value=!0,$e(m.addDateRange(i.value,Q.value)).then(n=>{K.value=n.data.result,D.value=n.data.totalNum,I.value=!1})}function ie(){g.value=!1,R()}function R(){u.value={dictId:void 0,dictName:void 0,dictType:void 0,status:"0",type:"N",remark:void 0},m.resetForm("formRef")}function $(){i.value.pageNum=1,w()}function se(){Q.value=[],m.resetForm("queryForm"),$()}function re(){R(),g.value=!0,F.value="添加字典类型"}function pe(n){x.value=n.map(o=>o.dictId),E.value=n.length!=1,O.value=!n.length}function j(n){R();const o=n.dictId||x.value;Se(o).then(v=>{u.value=v.data,g.value=!0,F.value="修改字典类型"})}function ce(){m.$refs.formRef.validate(n=>{n&&(u.value.dictId!=null?qe(u.value).then(o=>{m.$modal.msgSuccess("修改成功"),g.value=!1,w()}):Ue(u.value).then(o=>{m.$modal.msgSuccess("新增成功"),g.value=!1,w()}))})}function A(n){const o=n.dictId||x.value;m.$confirm('是否确认删除字典编号为"'+o+'"的数据项?',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return Ie(o)}).then(()=>{w(),m.$modal.msgSuccess("删除成功")})}function me(){m.$confirm("是否确认导出所有类型数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return xe(i.value)}).then(n=>{m.download(n.data.path)})}function fe(n){N.value=n.dictId,T.value=!0}return w(),m.getDicts("sys_normal_disable").then(n=>{C.value=n.data}),m.getDicts("sys_yes_no").then(n=>{L.value=n.data}),(n,o)=>{const v=d("el-input"),f=d("el-form-item"),M=d("el-option"),G=d("el-select"),b=d("el-button"),H=d("el-form"),S=d("el-col"),_e=d("right-toolbar"),be=d("el-row"),V=d("el-table-column"),ye=d("el-link"),ge=d("dict-tag"),ve=d("el-table"),Ve=d("pagination"),J=d("questionFilled"),W=d("el-icon"),X=d("el-tooltip"),Y=d("el-radio"),Z=d("el-radio-group"),ee=d("el-dialog"),k=le("hasPermi"),we=le("loading");return s(),h("div",De,[y(t(H,{model:a(i),ref:"queryForm",inline:!0,"label-width":"68px"},{default:l(()=>[t(f,{label:"字典类型",prop:"dictType"},{default:l(()=>[t(v,{modelValue:a(i).dictType,"onUpdate:modelValue":o[0]||(o[0]=e=>a(i).dictType=e),placeholder:"请输入字典类型",clearable:"",onKeyup:oe($,["enter"])},null,8,["modelValue"])]),_:1}),t(f,{label:"字典名称",prop:"dictName"},{default:l(()=>[t(v,{modelValue:a(i).dictName,"onUpdate:modelValue":o[1]||(o[1]=e=>a(i).dictName=e),placeholder:"请输入字典名称",clearable:"",onKeyup:oe($,["enter"])},null,8,["modelValue"])]),_:1}),t(f,{label:"状态",prop:"status"},{default:l(()=>[t(G,{modelValue:a(i).status,"onUpdate:modelValue":o[2]||(o[2]=e=>a(i).status=e),placeholder:"字典状态",clearable:""},{default:l(()=>[(s(!0),h(q,null,U(a(C),e=>(s(),_(M,{key:e.dictValue,label:e.dictLabel,value:e.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"是否内置",prop:"type"},{default:l(()=>[t(G,{modelValue:a(i).type,"onUpdate:modelValue":o[3]||(o[3]=e=>a(i).type=e),placeholder:"是否内置",clearable:""},{default:l(()=>[(s(!0),h(q,null,U(a(L),e=>(s(),_(M,{key:e.dictValue,label:e.dictLabel,value:e.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(f,null,{default:l(()=>[t(b,{type:"primary",icon:"search",onClick:$},{default:l(()=>[p(c(n.$t("btn.search")),1)]),_:1}),t(b,{icon:"refresh",onClick:se},{default:l(()=>[p(c(n.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[ae,a(P)]]),t(be,{gutter:10,class:"mb8"},{default:l(()=>[t(S,{span:1.5},{default:l(()=>[y((s(),_(b,{type:"primary",plain:"",icon:"plus",onClick:re},{default:l(()=>[p(c(n.$t("btn.add")),1)]),_:1})),[[k,["system:dict:add"]]])]),_:1}),t(S,{span:1.5},{default:l(()=>[y((s(),_(b,{type:"success",plain:"",icon:"edit",disabled:a(E),onClick:j},{default:l(()=>[p(c(n.$t("btn.edit")),1)]),_:1},8,["disabled"])),[[k,["system:dict:edit"]]])]),_:1}),t(S,{span:1.5},{default:l(()=>[y((s(),_(b,{type:"danger",plain:"",icon:"delete",disabled:a(O),onClick:A},{default:l(()=>[p(c(n.$t("btn.delete")),1)]),_:1},8,["disabled"])),[[k,["system:dict:remove"]]])]),_:1}),t(S,{span:1.5},{default:l(()=>[y((s(),_(b,{type:"warning",plain:"",icon:"download",onClick:me},{default:l(()=>[p(c(n.$t("btn.export")),1)]),_:1})),[[k,["system:dict:export"]]])]),_:1}),t(_e,{showSearch:a(P),onQueryTable:w},null,8,["showSearch"])]),_:1}),y((s(),_(ve,{data:a(K),border:"",onSelectionChange:pe},{default:l(()=>[t(V,{type:"selection",width:"55",align:"center"}),t(V,{label:"字典编号",align:"center",prop:"dictId",width:"100",sortable:""}),t(V,{label:"字典类型","show-overflow-tooltip":!0},{default:l(e=>[t(ye,{type:"primary",onClick:te=>fe(e.row)},{default:l(()=>[p(c(e.row.dictType),1)]),_:2},1032,["onClick"])]),_:1}),t(V,{label:"字典名称",align:"center",prop:"dictName","show-overflow-tooltip":!0}),t(V,{label:"状态",align:"center",prop:"status"},{default:l(e=>[t(ge,{options:a(C),value:e.row.status},null,8,["options","value"])]),_:1}),t(V,{label:"备注",align:"center",prop:"remark","show-overflow-tooltip":!0}),t(V,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:l(e=>[z("span",null,c(e.row.createTime),1)]),_:1}),t(V,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(e=>[y((s(),_(b,{text:"",size:"small",icon:"edit",onClick:te=>j(e.row)},{default:l(()=>[p(c(n.$t("btn.edit")),1)]),_:2},1032,["onClick"])),[[k,["system:dict:edit"]]]),y((s(),_(b,{text:"",size:"small",icon:"delete",onClick:te=>A(e.row)},{default:l(()=>[p(c(n.$t("btn.delete")),1)]),_:2},1032,["onClick"])),[[k,["system:dict:remove"]]])]),_:1})]),_:1},8,["data"])),[[we,a(I)]]),y(t(Ve,{total:a(D),page:a(i).pageNum,"onUpdate:page":o[4]||(o[4]=e=>a(i).pageNum=e),limit:a(i).pageSize,"onUpdate:limit":o[5]||(o[5]=e=>a(i).pageSize=e),onPagination:w},null,8,["total","page","limit"]),[[ae,a(D)>0]]),t(ee,{title:a(F),modelValue:a(g),"onUpdate:modelValue":o[12]||(o[12]=e=>B(g)?g.value=e:null),draggable:"",width:"500px","append-to-body":""},{footer:l(()=>[t(b,{text:"",onClick:ie},{default:l(()=>[p(c(n.$t("btn.cancel")),1)]),_:1}),t(b,{type:"primary",onClick:ce},{default:l(()=>[p(c(n.$t("btn.submit")),1)]),_:1})]),default:l(()=>[t(H,{ref_key:"formRef",ref:ne,model:a(u),rules:a(ue),"label-width":"100px"},{default:l(()=>[t(f,{label:"字典名称",prop:"dictName"},{default:l(()=>[t(v,{modelValue:a(u).dictName,"onUpdate:modelValue":o[6]||(o[6]=e=>a(u).dictName=e),placeholder:"请输入字典名称"},null,8,["modelValue"])]),_:1}),t(f,{label:"字典类型",prop:"dictType"},{label:l(()=>[z("span",null,[t(X,{content:"如果从数据库加载数据，请使用sql_开头字符串",placement:"top"},{default:l(()=>[t(W,{size:15},{default:l(()=>[t(J)]),_:1})]),_:1}),o[15]||(o[15]=p(" 字典类型 "))])]),default:l(()=>[t(v,{modelValue:a(u).dictType,"onUpdate:modelValue":o[7]||(o[7]=e=>a(u).dictType=e),placeholder:"请输入字典类型"},null,8,["modelValue"])]),_:1}),t(f,{label:"字典状态",prop:"status"},{default:l(()=>[t(Z,{modelValue:a(u).status,"onUpdate:modelValue":o[8]||(o[8]=e=>a(u).status=e)},{default:l(()=>[(s(!0),h(q,null,U(a(C),e=>(s(),_(Y,{key:e.dictValue,value:e.dictValue},{default:l(()=>[p(c(e.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"系统内置",prop:"type"},{default:l(()=>[t(Z,{modelValue:a(u).type,"onUpdate:modelValue":o[9]||(o[9]=e=>a(u).type=e)},{default:l(()=>[(s(!0),h(q,null,U(a(L),e=>(s(),_(Y,{key:e.dictValue,value:e.dictValue},{default:l(()=>[p(c(e.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"备注",prop:"remark"},{default:l(()=>[t(v,{modelValue:a(u).remark,"onUpdate:modelValue":o[10]||(o[10]=e=>a(u).remark=e),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1}),t(f,{label:"自定义sql",prop:"customSql"},{label:l(()=>[z("span",null,[t(X,{content:"如果从数据库加载数据，请按此格式配置sql语句：SELECT userId as dictValue, userName as dictLabel FROM sys_user",placement:"top"},{default:l(()=>[t(W,{size:15},{default:l(()=>[t(J)]),_:1})]),_:1}),o[16]||(o[16]=p(" sql语句 "))])]),default:l(()=>[t(v,{modelValue:a(u).customSql,"onUpdate:modelValue":o[11]||(o[11]=e=>a(u).customSql=e),type:"textarea",placeholder:"请输入sql语句"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),t(ee,{modelValue:a(T),"onUpdate:modelValue":o[14]||(o[14]=e=>B(T)?T.value=e:null),draggable:"",width:"60%","lock-scroll":!1},{default:l(()=>[t(a(Ne),{dictId:a(N),"onUpdate:dictId":o[13]||(o[13]=e=>B(N)?N.value=e:null)},null,8,["dictId"])]),_:1},8,["modelValue"])])}}});export{Ee as default};
