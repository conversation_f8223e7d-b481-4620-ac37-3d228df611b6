var Xr=Object.defineProperty;var Gt=e=>{throw TypeError(e)};var Yr=(e,u,t)=>u in e?Xr(e,u,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[u]=t;var W=(e,u,t)=>Yr(e,typeof u!="symbol"?u+"":u,t),nt=(e,u,t)=>u.has(e)||Gt("Cannot "+t);var d=(e,u,t)=>(nt(e,u,"read from private field"),t?t.call(e):u.get(e)),P=(e,u,t)=>u.has(e)?Gt("Cannot add the same private member more than once"):u instanceof WeakSet?u.add(e):u.set(e,t),T=(e,u,t,n)=>(nt(e,u,"write to private field"),n?n.call(e,t):u.set(e,t),t),w=(e,u,t)=>(nt(e,u,"access private method"),t);var wu=(e,u,t,n)=>({set _(r){T(e,u,r,t)},get _(){return d(e,u,n)}});import{g as Qr,s as zt,r as hu,aa as Pn,i as qe,a9 as Y,F as Jr,E as mt,w as ee,M as pe,af as Fe,e as Pe,ah as Bn,n as bu,ac as Nu}from"./index-D9f5ARRd.js";const Kr=e=>{const u=typeof e;return u!=="function"&&u!=="object"||e===null},ei=e=>{const u=e.flags===""?void 0:e.flags;return new RegExp(e.source,u)},fu=(e,u=new WeakMap)=>{if(e===null||Kr(e))return e;if(u.has(e))return u.get(e);if(e instanceof RegExp)return ei(e);if(e instanceof Date)return new Date(e.getTime());if(e instanceof Function)return e;if(e instanceof Map){const n=new Map;return u.set(e,n),e.forEach((r,i)=>{n.set(i,fu(r,u))}),n}if(e instanceof Set){const n=new Set;u.set(e,n);for(const r of e)n.add(fu(r,u));return n}if(Array.isArray(e)){const n=[];return u.set(e,n),e.forEach(r=>{n.push(fu(r,u))}),n}const t={};u.set(e,t);for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=fu(e[n],u));return t},Nn=(e,u=200)=>{let t=0;return(...n)=>new Promise(r=>{t&&(clearTimeout(t),r("cancel")),t=window.setTimeout(()=>{e.apply(void 0,n),t=0,r("done")},u)})},oc=(e,u={_blank:!0,nofollow:!0})=>{const t=document.createElement("a");t.href=e,u._blank&&(t.target="_blank"),u.nofollow&&(t.rel="noopener noreferrer"),t.click()},ac=()=>{let e=-1;return(u,t,n,r=100)=>{const i=()=>{n&&(typeof r=="number"?setTimeout(n,r):n())};e!==-1&&(cancelAnimationFrame(e),i());let o=u.scrollTop;const a=()=>{e=-1;const s=t-o;o=o+s/5,Math.abs(s)<1?(u.scrollTo(0,t),i()):(u.scrollTo(0,o),e=requestAnimationFrame(a))};e=requestAnimationFrame(a)}},sc=(e,u=200)=>{let t=0,n=null;const r=i=>{t===0&&(t=i),i-t>=u?(e.apply(void 0,n),n=null,t=0):window.requestAnimationFrame(r)};return(...i)=>{n===null&&window.requestAnimationFrame(r),n=i}},cc=e=>{const u=t=>{const{scrollHeight:n,scrollWidth:r,offsetHeight:i,offsetWidth:o,scrollLeft:a,scrollTop:s}=e,c=t.x,l=t.y,f=b=>{const h=s+l-b.y,m=a+c-b.x,x=n-i,g=r-o,_={};m>=0&&m<=g&&(_.left=m),h>=0&&h<=x&&(_.top=h),e.scroll(_)};document.addEventListener("mousemove",f);const p=()=>{document.removeEventListener("mousemove",f),document.removeEventListener("mouseup",p)};document.addEventListener("mouseup",p)};return e.addEventListener("mousedown",u),()=>{e.removeEventListener("mousedown",u)}},gt=()=>`${Date.now().toString(36)}${Math.random().toString(36).substring(2)}`,Vt=e=>e!==null&&typeof e=="object"&&!Array.isArray(e),qn=(e,u,t={})=>{const{excludeKeys:n}=t;for(const r in u)n&&n(r)?e[r]=u[r]:Vt(u[r])&&Vt(e[r])?e[r]=qn(e[r],u[r],t):e[r]=u[r];return e},F="md-editor",ui="https://at.alicdn.com/t/c/font_2605852_cmafimm6hot.js",ti="https://at.alicdn.com/t/c/font_2605852_cmafimm6hot.css",U="https://unpkg.com",ni=`${U}/@highlightjs/cdn-assets@11.10.0/highlight.min.js`,Zt={main:`${U}/prettier@3.3.3/standalone.js`,markdown:`${U}/prettier@3.3.3/plugins/markdown.js`},ri={css:`${U}/cropperjs@1.6.2/dist/cropper.min.css`,js:`${U}/cropperjs@1.6.2/dist/cropper.min.js`},ii=`${U}/screenfull@5.2.0/dist/screenfull.js`,oi=`${U}/mermaid@11.3.0/dist/mermaid.min.js`,ai={js:`${U}/katex@0.16.11/dist/katex.min.js`,css:`${U}/katex@0.16.11/dist/katex.min.css`},xt={a11y:{light:`${U}/@highlightjs/cdn-assets@11.10.0/styles/a11y-light.min.css`,dark:`${U}/@highlightjs/cdn-assets@11.10.0/styles/a11y-dark.min.css`},atom:{light:`${U}/@highlightjs/cdn-assets@11.10.0/styles/atom-one-light.min.css`,dark:`${U}/@highlightjs/cdn-assets@11.10.0/styles/atom-one-dark.min.css`},github:{light:`${U}/@highlightjs/cdn-assets@11.10.0/styles/github.min.css`,dark:`${U}/@highlightjs/cdn-assets@11.10.0/styles/github-dark.min.css`},gradient:{light:`${U}/@highlightjs/cdn-assets@11.10.0/styles/gradient-light.min.css`,dark:`${U}/@highlightjs/cdn-assets@11.10.0/styles/gradient-dark.min.css`},kimbie:{light:`${U}/@highlightjs/cdn-assets@11.10.0/styles/kimbie-light.min.css`,dark:`${U}/@highlightjs/cdn-assets@11.10.0/styles/kimbie-dark.min.css`},paraiso:{light:`${U}/@highlightjs/cdn-assets@11.10.0/styles/paraiso-light.min.css`,dark:`${U}/@highlightjs/cdn-assets@11.10.0/styles/paraiso-dark.min.css`},qtcreator:{light:`${U}/@highlightjs/cdn-assets@11.10.0/styles/qtcreator-light.min.css`,dark:`${U}/@highlightjs/cdn-assets@11.10.0/styles/qtcreator-dark.min.css`},stackoverflow:{light:`${U}/@highlightjs/cdn-assets@11.10.0/styles/stackoverflow-light.min.css`,dark:`${U}/@highlightjs/cdn-assets@11.10.0/styles/stackoverflow-dark.min.css`}},si=["bold","underline","italic","strikeThrough","-","title","sub","sup","quote","unorderedList","orderedList","task","-","codeRow","code","link","image","table","mermaid","katex","-","revoke","next","save","=","prettier","pageFullscreen","fullscreen","preview","previewOnly","htmlPreview","catalog","github"],ci=["markdownTotal","=","scrollSwitch"],Xt={"zh-CN":{toolbarTips:{bold:"加粗",underline:"下划线",italic:"斜体",strikeThrough:"删除线",title:"标题",sub:"下标",sup:"上标",quote:"引用",unorderedList:"无序列表",orderedList:"有序列表",task:"任务列表",codeRow:"行内代码",code:"块级代码",link:"链接",image:"图片",table:"表格",mermaid:"mermaid图",katex:"katex公式",revoke:"后退",next:"前进",save:"保存",prettier:"美化",pageFullscreen:"浏览器全屏",fullscreen:"屏幕全屏",preview:"预览",previewOnly:"仅预览",htmlPreview:"html代码预览",catalog:"目录",github:"源码地址"},titleItem:{h1:"一级标题",h2:"二级标题",h3:"三级标题",h4:"四级标题",h5:"五级标题",h6:"六级标题"},imgTitleItem:{link:"添加链接",upload:"上传图片",clip2upload:"裁剪上传"},linkModalTips:{linkTitle:"添加链接",imageTitle:"添加图片",descLabel:"链接描述：",descLabelPlaceHolder:"请输入描述...",urlLabel:"链接地址：",urlLabelPlaceHolder:"请输入链接...",buttonOK:"确定"},clipModalTips:{title:"裁剪图片上传",buttonUpload:"上传"},copyCode:{text:"复制代码",successTips:"已复制！",failTips:"复制失败！"},mermaid:{flow:"流程图",sequence:"时序图",gantt:"甘特图",class:"类图",state:"状态图",pie:"饼图",relationship:"关系图",journey:"旅程图"},katex:{inline:"行内公式",block:"块级公式"},footer:{markdownTotal:"字数",scrollAuto:"同步滚动"}},"en-US":{toolbarTips:{bold:"bold",underline:"underline",italic:"italic",strikeThrough:"strikeThrough",title:"title",sub:"subscript",sup:"superscript",quote:"quote",unorderedList:"unordered list",orderedList:"ordered list",task:"task list",codeRow:"inline code",code:"block-level code",link:"link",image:"image",table:"table",mermaid:"mermaid",katex:"formula",revoke:"revoke",next:"undo revoke",save:"save",prettier:"prettier",pageFullscreen:"fullscreen in page",fullscreen:"fullscreen",preview:"preview",previewOnly:"preview only",htmlPreview:"html preview",catalog:"catalog",github:"source code"},titleItem:{h1:"Lv1 Heading",h2:"Lv2 Heading",h3:"Lv3 Heading",h4:"Lv4 Heading",h5:"Lv5 Heading",h6:"Lv6 Heading"},imgTitleItem:{link:"Add Image Link",upload:"Upload Images",clip2upload:"Clip Upload"},linkModalTips:{linkTitle:"Add Link",imageTitle:"Add Image",descLabel:"Desc:",descLabelPlaceHolder:"Enter a description...",urlLabel:"Link:",urlLabelPlaceHolder:"Enter a link...",buttonOK:"OK"},clipModalTips:{title:"Crop Image",buttonUpload:"Upload"},copyCode:{text:"Copy",successTips:"Copied!",failTips:"Copy failed!"},mermaid:{flow:"flow",sequence:"sequence",gantt:"gantt",class:"class",state:"state",pie:"pie",relationship:"relationship",journey:"journey"},katex:{inline:"inline",block:"block"},footer:{markdownTotal:"Character Count",scrollAuto:"Scroll Auto"}}},ae={editorExtensions:{highlight:{js:ni,css:xt},prettier:{standaloneJs:Zt.main,parserMarkdownJs:Zt.markdown},cropper:{...ri},iconfont:ui,iconfontClass:ti,screenfull:{js:ii},mermaid:{js:oi},katex:{...ai}},editorExtensionsAttrs:{},editorConfig:{languageUserDefined:{},mermaidTemplate:{},renderDelay:500,zIndex:2e4},codeMirrorExtensions:(e,u)=>u,markdownItConfig:()=>{},markdownItPlugins:e=>e,iconfontType:"svg",mermaidConfig:e=>e,katexConfig:e=>e},lc=170,li=({instance:e,ctx:u,props:t={}},n="default")=>{const r=(e==null?void 0:e.$slots[n])||(u==null?void 0:u.slots[n]);return(r?r(e):"")||t[n]};var fi=Object.defineProperty,di=(e,u,t)=>u in e?fi(e,u,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[u]=t,hi=(e,u,t)=>di(e,u+"",t);class pi{constructor(){hi(this,"pools",{})}remove(u,t,n){const i=this.pools[u]&&this.pools[u][t];i&&(this.pools[u][t]=i.filter(o=>o!==n))}clear(u){this.pools[u]={}}on(u,t){return this.pools[u]||(this.pools[u]={}),this.pools[u][t.name]||(this.pools[u][t.name]=[]),this.pools[u][t.name].push(t.callback),this.pools[u][t.name].includes(t.callback)}emit(u,t,...n){this.pools[u]||(this.pools[u]={});const i=this.pools[u][t];i&&i.forEach(o=>{try{o(...n)}catch(a){console.error(`${t} monitor event exception！`,a)}})}}const O=new pi,Hn="onSave",jn="changeCatalogVisible",bi="changeFullscreen",Yt="pageFullscreenChanged",Qt="fullscreenChanged",Jt="previewChanged",Kt="previewOnlyChanged",en="htmlPreviewChanged",un="catalogVisibleChanged",Lu="buildFinished",mi="errorCatcher",_t="replace",gi="uploadImage",fc="openModals",dc="ctrlZ",hc="ctrlShiftZ",rt="catalogChanged",xi="pushCatalog",Lt="rerender",_i="eventListener",ki="taskStateChanged",pc=(e,u)=>{const t=n=>{const r=e.parentElement||document.body,i=r.offsetWidth,o=r.offsetHeight,{clientWidth:a,clientHeight:s}=document.documentElement,c=n.offsetX,l=n.offsetY,f=b=>{let h=b.x+document.body.scrollLeft-document.body.clientLeft-c,m=b.y+document.body.scrollTop-document.body.clientTop-l;h=h<1?1:h<a-i-1?h:a-i-1,m=m<1?1:m<s-o-1?m:s-o-1,u?u(h,m):(r.style.left=`${h}px`,r.style.top=`${m}px`)};document.addEventListener("mousemove",f);const p=()=>{document.removeEventListener("mousemove",f),document.removeEventListener("mouseup",p)};document.addEventListener("mouseup",p)};return e.addEventListener("mousedown",t),()=>{e.removeEventListener("mousedown",t)}},he=(e,u,t="")=>{var n;const r=document.getElementById(u.id);if(r)t!==""&&(Reflect.get(window,t)?(n=u.onload)==null||n.call(r,new Event("load")):u.onload&&r.addEventListener("load",u.onload));else{const i={...u};i.onload=null;const o=vi(e,i);u.onload&&o.addEventListener("load",u.onload),document.head.appendChild(o)}},yi=(e,u)=>{const t=document.getElementById(u.id);t==null||t.remove(),he(e,u)},vi=(e,u)=>{const t=document.createElement(e);return Object.keys(u).forEach(n=>{u[n]!==void 0&&(t[n]=u[n])}),t},tn=(()=>{const e=t=>{if(!t)return;const n=t.firstChild;let r=1,i=0,o=0,a=!1,s,c,l,f=1;const p=()=>{n.style.transform=`translate(${i}px, ${o}px) scale(${r})`};t.addEventListener("touchstart",b=>{b.touches.length===1?(a=!0,s=b.touches[0].clientX-i,c=b.touches[0].clientY-o):b.touches.length===2&&(l=Math.hypot(b.touches[0].clientX-b.touches[1].clientX,b.touches[0].clientY-b.touches[1].clientY),f=r)},{passive:!1}),t.addEventListener("touchmove",b=>{if(b.preventDefault(),a&&b.touches.length===1)i=b.touches[0].clientX-s,o=b.touches[0].clientY-c,p();else if(b.touches.length===2){const m=Math.hypot(b.touches[0].clientX-b.touches[1].clientX,b.touches[0].clientY-b.touches[1].clientY)/l,x=r;r=f*(1+(m-1));const g=(b.touches[0].clientX+b.touches[1].clientX)/2,_=(b.touches[0].clientY+b.touches[1].clientY)/2,y=n.getBoundingClientRect(),v=(g-y.left)/x,C=(_-y.top)/x;i-=v*(r-x),o-=C*(r-x),p()}},{passive:!1}),t.addEventListener("touchend",()=>{a=!1}),t.addEventListener("wheel",b=>{b.preventDefault();const h=.02,m=r;b.deltaY<0?r+=h:r=Math.max(.1,r-h);const x=n.getBoundingClientRect(),g=b.clientX-x.left,_=b.clientY-x.top;i-=g/m*(r-m),o-=_/m*(r-m),p()},{passive:!1}),t.addEventListener("mousedown",b=>{a=!0,s=b.clientX-i,c=b.clientY-o}),t.addEventListener("mousemove",b=>{a&&(i=b.clientX-s,o=b.clientY-c,p())}),t.addEventListener("mouseup",()=>{a=!1}),t.addEventListener("mouseleave",()=>{a=!1})};return t=>{t.forEach(n=>{e(n)})}})(),bc=(e,u="image.png")=>{const t=e.split(","),n=t[0].match(/:(.*?);/);if(n){const r=n[1],i=atob(t[1]);let o=i.length;const a=new Uint8Array(o);for(;o--;)a[o]=i.charCodeAt(o);return new File([a],u,{type:r})}return null},Ci=(e,u)=>{if(!e)return e;const t=u.split(`
`),n=['<span rn-wrapper aria-hidden="true">'];return t.forEach(()=>{n.push("<span></span>")}),n.push("</span>"),`<span class="${F}-code-block">${e}</span>${n.join("")}`},mc=(e,u)=>{if(!e||!u)return 0;const t=e==null?void 0:e.getBoundingClientRect();if(u===document.documentElement)return t.top-u.clientTop;const n=u==null?void 0:u.getBoundingClientRect();return t.top-n.top},Ei=(()=>{let e=0;return u=>u+ ++e})(),gc=(()=>{let e=0;return()=>++e})();/*! medium-zoom 1.1.0 | MIT License | https://github.com/francoischalifour/medium-zoom */var Ge=Object.assign||function(e){for(var u=1;u<arguments.length;u++){var t=arguments[u];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},Du=function(u){return u.tagName==="IMG"},Ai=function(u){return NodeList.prototype.isPrototypeOf(u)},Ru=function(u){return u&&u.nodeType===1},nn=function(u){var t=u.currentSrc||u.src;return t.substr(-4).toLowerCase()===".svg"},rn=function(u){try{return Array.isArray(u)?u.filter(Du):Ai(u)?[].slice.call(u).filter(Du):Ru(u)?[u].filter(Du):typeof u=="string"?[].slice.call(document.querySelectorAll(u)).filter(Du):[]}catch{throw new TypeError(`The provided selector is invalid.
Expects a CSS selector, a Node element, a NodeList or an array.
See: https://github.com/francoischalifour/medium-zoom`)}},wi=function(u){var t=document.createElement("div");return t.classList.add("medium-zoom-overlay"),t.style.background=u,t},Di=function(u){var t=u.getBoundingClientRect(),n=t.top,r=t.left,i=t.width,o=t.height,a=u.cloneNode(),s=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,c=window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0;return a.removeAttribute("id"),a.style.position="absolute",a.style.top=n+s+"px",a.style.left=r+c+"px",a.style.width=i+"px",a.style.height=o+"px",a.style.transform="",a},Je=function(u,t){var n=Ge({bubbles:!1,cancelable:!1,detail:void 0},t);if(typeof window.CustomEvent=="function")return new CustomEvent(u,n);var r=document.createEvent("CustomEvent");return r.initCustomEvent(u,n.bubbles,n.cancelable,n.detail),r},Fi=function e(u){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=window.Promise||function(S){function z(){}S(z,z)},r=function(S){var z=S.target;if(z===$){h();return}y.indexOf(z)!==-1&&m({target:z})},i=function(){if(!(C||!k.original)){var S=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;Math.abs(A-S)>D.scrollOffset&&setTimeout(h,150)}},o=function(S){var z=S.key||S.keyCode;(z==="Escape"||z==="Esc"||z===27)&&h()},a=function(){var S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},z=S;if(S.background&&($.style.background=S.background),S.container&&S.container instanceof Object&&(z.container=Ge({},D.container,S.container)),S.template){var M=Ru(S.template)?S.template:document.querySelector(S.template);z.template=M}return D=Ge({},D,z),y.forEach(function(N){N.dispatchEvent(Je("medium-zoom:update",{detail:{zoom:L}}))}),L},s=function(){var S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return e(Ge({},D,S))},c=function(){for(var S=arguments.length,z=Array(S),M=0;M<S;M++)z[M]=arguments[M];var N=z.reduce(function(R,te){return[].concat(R,rn(te))},[]);return N.filter(function(R){return y.indexOf(R)===-1}).forEach(function(R){y.push(R),R.classList.add("medium-zoom-image")}),v.forEach(function(R){var te=R.type,V=R.listener,re=R.options;N.forEach(function(xe){xe.addEventListener(te,V,re)})}),L},l=function(){for(var S=arguments.length,z=Array(S),M=0;M<S;M++)z[M]=arguments[M];k.zoomed&&h();var N=z.length>0?z.reduce(function(R,te){return[].concat(R,rn(te))},[]):y;return N.forEach(function(R){R.classList.remove("medium-zoom-image"),R.dispatchEvent(Je("medium-zoom:detach",{detail:{zoom:L}}))}),y=y.filter(function(R){return N.indexOf(R)===-1}),L},f=function(S,z){var M=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return y.forEach(function(N){N.addEventListener("medium-zoom:"+S,z,M)}),v.push({type:"medium-zoom:"+S,listener:z,options:M}),L},p=function(S,z){var M=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return y.forEach(function(N){N.removeEventListener("medium-zoom:"+S,z,M)}),v=v.filter(function(N){return!(N.type==="medium-zoom:"+S&&N.listener.toString()===z.toString())}),L},b=function(){var S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},z=S.target,M=function(){var R={width:document.documentElement.clientWidth,height:document.documentElement.clientHeight,left:0,top:0,right:0,bottom:0},te=void 0,V=void 0;if(D.container)if(D.container instanceof Object)R=Ge({},R,D.container),te=R.width-R.left-R.right-D.margin*2,V=R.height-R.top-R.bottom-D.margin*2;else{var re=Ru(D.container)?D.container:document.querySelector(D.container),xe=re.getBoundingClientRect(),Qe=xe.width,Ju=xe.height,Ku=xe.left,Nr=xe.top;R=Ge({},R,{width:Qe,height:Ju,left:Ku,top:Nr})}te=te||R.width-D.margin*2,V=V||R.height-D.margin*2;var cu=k.zoomedHd||k.original,qr=nn(cu)?te:cu.naturalWidth||te,Hr=nn(cu)?V:cu.naturalHeight||V,Au=cu.getBoundingClientRect(),jr=Au.top,Ur=Au.left,et=Au.width,ut=Au.height,Wr=Math.min(Math.max(et,qr),te)/et,Gr=Math.min(Math.max(ut,Hr),V)/ut,tt=Math.min(Wr,Gr),Vr=(-Ur+(te-et)/2+D.margin+R.left)/tt,Zr=(-jr+(V-ut)/2+D.margin+R.top)/tt,Wt="scale("+tt+") translate3d("+Vr+"px, "+Zr+"px, 0)";k.zoomed.style.transform=Wt,k.zoomedHd&&(k.zoomedHd.style.transform=Wt)};return new n(function(N){if(z&&y.indexOf(z)===-1){N(L);return}var R=function Qe(){C=!1,k.zoomed.removeEventListener("transitionend",Qe),k.original.dispatchEvent(Je("medium-zoom:opened",{detail:{zoom:L}})),N(L)};if(k.zoomed){N(L);return}if(z)k.original=z;else if(y.length>0){var te=y;k.original=te[0]}else{N(L);return}if(k.original.dispatchEvent(Je("medium-zoom:open",{detail:{zoom:L}})),A=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,C=!0,k.zoomed=Di(k.original),document.body.appendChild($),D.template){var V=Ru(D.template)?D.template:document.querySelector(D.template);k.template=document.createElement("div"),k.template.appendChild(V.content.cloneNode(!0)),document.body.appendChild(k.template)}if(k.original.parentElement&&k.original.parentElement.tagName==="PICTURE"&&k.original.currentSrc&&(k.zoomed.src=k.original.currentSrc),document.body.appendChild(k.zoomed),window.requestAnimationFrame(function(){document.body.classList.add("medium-zoom--opened")}),k.original.classList.add("medium-zoom-image--hidden"),k.zoomed.classList.add("medium-zoom-image--opened"),k.zoomed.addEventListener("click",h),k.zoomed.addEventListener("transitionend",R),k.original.getAttribute("data-zoom-src")){k.zoomedHd=k.zoomed.cloneNode(),k.zoomedHd.removeAttribute("srcset"),k.zoomedHd.removeAttribute("sizes"),k.zoomedHd.removeAttribute("loading"),k.zoomedHd.src=k.zoomed.getAttribute("data-zoom-src"),k.zoomedHd.onerror=function(){clearInterval(re),console.warn("Unable to reach the zoom image target "+k.zoomedHd.src),k.zoomedHd=null,M()};var re=setInterval(function(){k.zoomedHd.complete&&(clearInterval(re),k.zoomedHd.classList.add("medium-zoom-image--opened"),k.zoomedHd.addEventListener("click",h),document.body.appendChild(k.zoomedHd),M())},10)}else if(k.original.hasAttribute("srcset")){k.zoomedHd=k.zoomed.cloneNode(),k.zoomedHd.removeAttribute("sizes"),k.zoomedHd.removeAttribute("loading");var xe=k.zoomedHd.addEventListener("load",function(){k.zoomedHd.removeEventListener("load",xe),k.zoomedHd.classList.add("medium-zoom-image--opened"),k.zoomedHd.addEventListener("click",h),document.body.appendChild(k.zoomedHd),M()})}else M()})},h=function(){return new n(function(S){if(C||!k.original){S(L);return}var z=function M(){k.original.classList.remove("medium-zoom-image--hidden"),document.body.removeChild(k.zoomed),k.zoomedHd&&document.body.removeChild(k.zoomedHd),document.body.removeChild($),k.zoomed.classList.remove("medium-zoom-image--opened"),k.template&&document.body.removeChild(k.template),C=!1,k.zoomed.removeEventListener("transitionend",M),k.original.dispatchEvent(Je("medium-zoom:closed",{detail:{zoom:L}})),k.original=null,k.zoomed=null,k.zoomedHd=null,k.template=null,S(L)};C=!0,document.body.classList.remove("medium-zoom--opened"),k.zoomed.style.transform="",k.zoomedHd&&(k.zoomedHd.style.transform=""),k.template&&(k.template.style.transition="opacity 150ms",k.template.style.opacity=0),k.original.dispatchEvent(Je("medium-zoom:close",{detail:{zoom:L}})),k.zoomed.addEventListener("transitionend",z)})},m=function(){var S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},z=S.target;return k.original?h():b({target:z})},x=function(){return D},g=function(){return y},_=function(){return k.original},y=[],v=[],C=!1,A=0,D=t,k={original:null,zoomed:null,zoomedHd:null,template:null};Object.prototype.toString.call(u)==="[object Object]"?D=u:(u||typeof u=="string")&&c(u),D=Ge({margin:0,background:"#fff",scrollOffset:40,container:null,template:null},D);var $=wi(D.background);document.addEventListener("click",r),document.addEventListener("keyup",o),document.addEventListener("scroll",i),window.addEventListener("resize",h);var L={open:b,close:h,toggle:m,update:a,clone:s,attach:c,detach:l,on:f,off:p,getOptions:x,getImages:g,getZoomedImage:_};return L};function Si(e,u){u===void 0&&(u={});var t=u.insertAt;if(!(typeof document>"u")){var n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}}var Ti=".medium-zoom-overlay{position:fixed;top:0;right:0;bottom:0;left:0;opacity:0;transition:opacity .3s;will-change:opacity}.medium-zoom--opened .medium-zoom-overlay{cursor:pointer;cursor:zoom-out;opacity:1}.medium-zoom-image{cursor:pointer;cursor:zoom-in;transition:transform .3s cubic-bezier(.2,0,.2,1)!important}.medium-zoom-image--hidden{visibility:hidden}.medium-zoom-image--opened{position:relative;cursor:pointer;cursor:zoom-out;will-change:transform}";Si(Ti);var it,on;function Ii(){return on||(on=1,it=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var u=document.activeElement,t=[],n=0;n<e.rangeCount;n++)t.push(e.getRangeAt(n));switch(u.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":u.blur();break;default:u=null;break}return e.removeAllRanges(),function(){e.type==="Caret"&&e.removeAllRanges(),e.rangeCount||t.forEach(function(r){e.addRange(r)}),u&&u.focus()}}),it}var ot,an;function zi(){if(an)return ot;an=1;var e=Ii(),u={"text/plain":"Text","text/html":"Url",default:"Text"},t="Copy to clipboard: #{key}, Enter";function n(i){var o=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C";return i.replace(/#{\s*key\s*}/g,o)}function r(i,o){var a,s,c,l,f,p,b=!1;o||(o={}),a=o.debug||!1;try{c=e(),l=document.createRange(),f=document.getSelection(),p=document.createElement("span"),p.textContent=i,p.ariaHidden="true",p.style.all="unset",p.style.position="fixed",p.style.top=0,p.style.clip="rect(0, 0, 0, 0)",p.style.whiteSpace="pre",p.style.webkitUserSelect="text",p.style.MozUserSelect="text",p.style.msUserSelect="text",p.style.userSelect="text",p.addEventListener("copy",function(m){if(m.stopPropagation(),o.format)if(m.preventDefault(),typeof m.clipboardData>"u"){a&&console.warn("unable to use e.clipboardData"),a&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var x=u[o.format]||u.default;window.clipboardData.setData(x,i)}else m.clipboardData.clearData(),m.clipboardData.setData(o.format,i);o.onCopy&&(m.preventDefault(),o.onCopy(m.clipboardData))}),document.body.appendChild(p),l.selectNodeContents(p),f.addRange(l);var h=document.execCommand("copy");if(!h)throw new Error("copy command was unsuccessful");b=!0}catch(m){a&&console.error("unable to copy using execCommand: ",m),a&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(o.format||"text",i),o.onCopy&&o.onCopy(window.clipboardData),b=!0}catch(x){a&&console.error("unable to copy using clipboardData: ",x),a&&console.error("falling back to prompt"),s=n("message"in o?o.message:t),window.prompt(s,i)}}finally{f&&(typeof f.removeRange=="function"?f.removeRange(l):f.removeAllRanges()),p&&document.body.removeChild(p),c()}return b}return ot=r,ot}var Li=zi();const Ri=Qr(Li),sn={};function $i(e){let u=sn[e];if(u)return u;u=sn[e]=[];for(let t=0;t<128;t++){const n=String.fromCharCode(t);u.push(n)}for(let t=0;t<e.length;t++){const n=e.charCodeAt(t);u[n]="%"+("0"+n.toString(16).toUpperCase()).slice(-2)}return u}function ou(e,u){typeof u!="string"&&(u=ou.defaultChars);const t=$i(u);return e.replace(/(%[a-f0-9]{2})+/gi,function(n){let r="";for(let i=0,o=n.length;i<o;i+=3){const a=parseInt(n.slice(i+1,i+3),16);if(a<128){r+=t[a];continue}if((a&224)===192&&i+3<o){const s=parseInt(n.slice(i+4,i+6),16);if((s&192)===128){const c=a<<6&1984|s&63;c<128?r+="��":r+=String.fromCharCode(c),i+=3;continue}}if((a&240)===224&&i+6<o){const s=parseInt(n.slice(i+4,i+6),16),c=parseInt(n.slice(i+7,i+9),16);if((s&192)===128&&(c&192)===128){const l=a<<12&61440|s<<6&4032|c&63;l<2048||l>=55296&&l<=57343?r+="���":r+=String.fromCharCode(l),i+=6;continue}}if((a&248)===240&&i+9<o){const s=parseInt(n.slice(i+4,i+6),16),c=parseInt(n.slice(i+7,i+9),16),l=parseInt(n.slice(i+10,i+12),16);if((s&192)===128&&(c&192)===128&&(l&192)===128){let f=a<<18&1835008|s<<12&258048|c<<6&4032|l&63;f<65536||f>1114111?r+="����":(f-=65536,r+=String.fromCharCode(55296+(f>>10),56320+(f&1023))),i+=9;continue}}r+="�"}return r})}ou.defaultChars=";/?:@&=+$,#";ou.componentChars="";const cn={};function Oi(e){let u=cn[e];if(u)return u;u=cn[e]=[];for(let t=0;t<128;t++){const n=String.fromCharCode(t);/^[0-9a-z]$/i.test(n)?u.push(n):u.push("%"+("0"+t.toString(16).toUpperCase()).slice(-2))}for(let t=0;t<e.length;t++)u[e.charCodeAt(t)]=e[t];return u}function vu(e,u,t){typeof u!="string"&&(t=u,u=vu.defaultChars),typeof t>"u"&&(t=!0);const n=Oi(u);let r="";for(let i=0,o=e.length;i<o;i++){const a=e.charCodeAt(i);if(t&&a===37&&i+2<o&&/^[0-9a-f]{2}$/i.test(e.slice(i+1,i+3))){r+=e.slice(i,i+3),i+=2;continue}if(a<128){r+=n[a];continue}if(a>=55296&&a<=57343){if(a>=55296&&a<=56319&&i+1<o){const s=e.charCodeAt(i+1);if(s>=56320&&s<=57343){r+=encodeURIComponent(e[i]+e[i+1]),i++;continue}}r+="%EF%BF%BD";continue}r+=encodeURIComponent(e[i])}return r}vu.defaultChars=";/?:@&=+$,-_.!~*'()#";vu.componentChars="-_.!~*'()";function Rt(e){let u="";return u+=e.protocol||"",u+=e.slashes?"//":"",u+=e.auth?e.auth+"@":"",e.hostname&&e.hostname.indexOf(":")!==-1?u+="["+e.hostname+"]":u+=e.hostname||"",u+=e.port?":"+e.port:"",u+=e.pathname||"",u+=e.search||"",u+=e.hash||"",u}function qu(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}const Mi=/^([a-z0-9.+-]+:)/i,Pi=/:[0-9]*$/,Bi=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,Ni=["<",">",'"',"`"," ","\r",`
`,"	"],qi=["{","}","|","\\","^","`"].concat(Ni),Hi=["'"].concat(qi),ln=["%","/","?",";","#"].concat(Hi),fn=["/","?","#"],ji=255,dn=/^[+a-z0-9A-Z_-]{0,63}$/,Ui=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,hn={javascript:!0,"javascript:":!0},pn={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};function $t(e,u){if(e&&e instanceof qu)return e;const t=new qu;return t.parse(e,u),t}qu.prototype.parse=function(e,u){let t,n,r,i=e;if(i=i.trim(),!u&&e.split("#").length===1){const c=Bi.exec(i);if(c)return this.pathname=c[1],c[2]&&(this.search=c[2]),this}let o=Mi.exec(i);if(o&&(o=o[0],t=o.toLowerCase(),this.protocol=o,i=i.substr(o.length)),(u||o||i.match(/^\/\/[^@\/]+@[^@\/]+/))&&(r=i.substr(0,2)==="//",r&&!(o&&hn[o])&&(i=i.substr(2),this.slashes=!0)),!hn[o]&&(r||o&&!pn[o])){let c=-1;for(let h=0;h<fn.length;h++)n=i.indexOf(fn[h]),n!==-1&&(c===-1||n<c)&&(c=n);let l,f;c===-1?f=i.lastIndexOf("@"):f=i.lastIndexOf("@",c),f!==-1&&(l=i.slice(0,f),i=i.slice(f+1),this.auth=l),c=-1;for(let h=0;h<ln.length;h++)n=i.indexOf(ln[h]),n!==-1&&(c===-1||n<c)&&(c=n);c===-1&&(c=i.length),i[c-1]===":"&&c--;const p=i.slice(0,c);i=i.slice(c),this.parseHost(p),this.hostname=this.hostname||"";const b=this.hostname[0]==="["&&this.hostname[this.hostname.length-1]==="]";if(!b){const h=this.hostname.split(/\./);for(let m=0,x=h.length;m<x;m++){const g=h[m];if(g&&!g.match(dn)){let _="";for(let y=0,v=g.length;y<v;y++)g.charCodeAt(y)>127?_+="x":_+=g[y];if(!_.match(dn)){const y=h.slice(0,m),v=h.slice(m+1),C=g.match(Ui);C&&(y.push(C[1]),v.unshift(C[2])),v.length&&(i=v.join(".")+i),this.hostname=y.join(".");break}}}}this.hostname.length>ji&&(this.hostname=""),b&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}const a=i.indexOf("#");a!==-1&&(this.hash=i.substr(a),i=i.slice(0,a));const s=i.indexOf("?");return s!==-1&&(this.search=i.substr(s),i=i.slice(0,s)),i&&(this.pathname=i),pn[t]&&this.hostname&&!this.pathname&&(this.pathname=""),this};qu.prototype.parseHost=function(e){let u=Pi.exec(e);u&&(u=u[0],u!==":"&&(this.port=u.substr(1)),e=e.substr(0,e.length-u.length)),e&&(this.hostname=e)};const Wi=Object.freeze(Object.defineProperty({__proto__:null,decode:ou,encode:vu,format:Rt,parse:$t},Symbol.toStringTag,{value:"Module"})),Un=/[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,Wn=/[\0-\x1F\x7F-\x9F]/,Gi=/[\xAD\u0600-\u0605\u061C\u06DD\u070F\u0890\u0891\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD80D[\uDC30-\uDC3F]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/,Ot=/[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDEAD\uDF55-\uDF59\uDF86-\uDF89]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5A\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDEB9\uDF3C-\uDF3E]|\uD806[\uDC3B\uDD44-\uDD46\uDDE2\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2\uDF00-\uDF09]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8\uDF43-\uDF4F\uDFFF]|\uD809[\uDC70-\uDC74]|\uD80B[\uDFF1\uDFF2]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A\uDFE2]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/,Gn=/[\$\+<->\^`\|~\xA2-\xA6\xA8\xA9\xAC\xAE-\xB1\xB4\xB8\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0384\u0385\u03F6\u0482\u058D-\u058F\u0606-\u0608\u060B\u060E\u060F\u06DE\u06E9\u06FD\u06FE\u07F6\u07FE\u07FF\u0888\u09F2\u09F3\u09FA\u09FB\u0AF1\u0B70\u0BF3-\u0BFA\u0C7F\u0D4F\u0D79\u0E3F\u0F01-\u0F03\u0F13\u0F15-\u0F17\u0F1A-\u0F1F\u0F34\u0F36\u0F38\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE\u0FCF\u0FD5-\u0FD8\u109E\u109F\u1390-\u1399\u166D\u17DB\u1940\u19DE-\u19FF\u1B61-\u1B6A\u1B74-\u1B7C\u1FBD\u1FBF-\u1FC1\u1FCD-\u1FCF\u1FDD-\u1FDF\u1FED-\u1FEF\u1FFD\u1FFE\u2044\u2052\u207A-\u207C\u208A-\u208C\u20A0-\u20C0\u2100\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F\u218A\u218B\u2190-\u2307\u230C-\u2328\u232B-\u2426\u2440-\u244A\u249C-\u24E9\u2500-\u2767\u2794-\u27C4\u27C7-\u27E5\u27F0-\u2982\u2999-\u29D7\u29DC-\u29FB\u29FE-\u2B73\u2B76-\u2B95\u2B97-\u2BFF\u2CE5-\u2CEA\u2E50\u2E51\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFF\u3004\u3012\u3013\u3020\u3036\u3037\u303E\u303F\u309B\u309C\u3190\u3191\u3196-\u319F\u31C0-\u31E3\u31EF\u3200-\u321E\u322A-\u3247\u3250\u3260-\u327F\u328A-\u32B0\u32C0-\u33FF\u4DC0-\u4DFF\uA490-\uA4C6\uA700-\uA716\uA720\uA721\uA789\uA78A\uA828-\uA82B\uA836-\uA839\uAA77-\uAA79\uAB5B\uAB6A\uAB6B\uFB29\uFBB2-\uFBC2\uFD40-\uFD4F\uFDCF\uFDFC-\uFDFF\uFE62\uFE64-\uFE66\uFE69\uFF04\uFF0B\uFF1C-\uFF1E\uFF3E\uFF40\uFF5C\uFF5E\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFFC\uFFFD]|\uD800[\uDD37-\uDD3F\uDD79-\uDD89\uDD8C-\uDD8E\uDD90-\uDD9C\uDDA0\uDDD0-\uDDFC]|\uD802[\uDC77\uDC78\uDEC8]|\uD805\uDF3F|\uD807[\uDFD5-\uDFF1]|\uD81A[\uDF3C-\uDF3F\uDF45]|\uD82F\uDC9C|\uD833[\uDF50-\uDFC3]|\uD834[\uDC00-\uDCF5\uDD00-\uDD26\uDD29-\uDD64\uDD6A-\uDD6C\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDDEA\uDE00-\uDE41\uDE45\uDF00-\uDF56]|\uD835[\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85\uDE86]|\uD838[\uDD4F\uDEFF]|\uD83B[\uDCAC\uDCB0\uDD2E\uDEF0\uDEF1]|\uD83C[\uDC00-\uDC2B\uDC30-\uDC93\uDCA0-\uDCAE\uDCB1-\uDCBF\uDCC1-\uDCCF\uDCD1-\uDCF5\uDD0D-\uDDAD\uDDE6-\uDE02\uDE10-\uDE3B\uDE40-\uDE48\uDE50\uDE51\uDE60-\uDE65\uDF00-\uDFFF]|\uD83D[\uDC00-\uDED7\uDEDC-\uDEEC\uDEF0-\uDEFC\uDF00-\uDF76\uDF7B-\uDFD9\uDFE0-\uDFEB\uDFF0]|\uD83E[\uDC00-\uDC0B\uDC10-\uDC47\uDC50-\uDC59\uDC60-\uDC87\uDC90-\uDCAD\uDCB0\uDCB1\uDD00-\uDE53\uDE60-\uDE6D\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC5\uDECE-\uDEDB\uDEE0-\uDEE8\uDEF0-\uDEF8\uDF00-\uDF92\uDF94-\uDFCA]/,Vn=/[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/,Vi=Object.freeze(Object.defineProperty({__proto__:null,Any:Un,Cc:Wn,Cf:Gi,P:Ot,S:Gn,Z:Vn},Symbol.toStringTag,{value:"Module"})),Zi=new Uint16Array('ᵁ<Õıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms¦³¹ÈÏlig耻Æ䃆P耻&䀦cute耻Á䃁reve;䄂Āiyx}rc耻Â䃂;䐐r;쀀𝔄rave耻À䃀pha;䎑acr;䄀d;橓Āgp¡on;䄄f;쀀𝔸plyFunction;恡ing耻Å䃅Ācs¾Ãr;쀀𝒜ign;扔ilde耻Ã䃃ml耻Ä䃄ЀaceforsuåûþėĜĢħĪĀcrêòkslash;或Ŷöø;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀𝔅pf;쀀𝔹eve;䋘còēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻©䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻Ç䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷òſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀𝒞pĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀𝔇Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀𝔻ƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegraìȹoɴ͹\0\0ͻ»͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔eåˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀𝒟rok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻Ð䃐cute耻É䃉ƀaiyӒӗӜron;䄚rc耻Ê䃊;䐭ot;䄖r;쀀𝔈rave耻È䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀𝔼silon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻Ë䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀𝔉lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀𝔽All;戀riertrf;愱cò׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀𝔊;拙pf;쀀𝔾eater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀𝒢;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅòکrok;䄦mpńېۘownHumðįqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻Í䃍Āiyܓܘrc耻Î䃎;䐘ot;䄰r;愑rave耻Ì䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lieóϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀𝕀a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻Ï䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀𝔍pf;쀀𝕁ǣ߇\0ߌr;쀀𝒥rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀𝔎pf;쀀𝕂cr;쀀𝒦րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ightáΜs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀𝔏Ā;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊightáοightáϊf;쀀𝕃erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂòࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀𝔐nusPlus;戓pf;쀀𝕄cò੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘ë૙eryThiî૙tedĀGL૸ଆreaterGreateòٳessLesóੈLine;䀊r;쀀𝔑ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀𝒩ilde耻Ñ䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻Ó䃓Āiy෎ීrc耻Ô䃔;䐞blac;䅐r;쀀𝔒rave耻Ò䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀𝕆enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀𝒪ash耻Ø䃘iŬื฼de耻Õ䃕es;樷ml耻Ö䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀𝔓i;䎦;䎠usMinus;䂱Āipຢອncareplanåڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀𝒫;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀𝔔pf;愚cr;쀀𝒬؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻®䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r»ཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀𝔖ortȀDLRUᄪᄴᄾᅉownArrow»ОeftArrow»࢚ightArrow»࿝pArrow;憑gma;䎣allCircle;战pf;쀀𝕊ɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀𝒮ar;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Tháྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et»ሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻Þ䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀𝔗Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀𝕋ipleDot;惛Āctዖዛr;쀀𝒯rok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻Ú䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻Û䃛;䐣blac;䅰r;쀀𝔘rave耻Ù䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀𝕌ЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥ownáϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀𝒰ilde;䅨ml耻Ü䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀𝔙pf;쀀𝕍cr;쀀𝒱dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀𝔚pf;쀀𝕎cr;쀀𝒲Ȁfiosᓋᓐᓒᓘr;쀀𝔛;䎞pf;쀀𝕏cr;쀀𝒳ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻Ý䃝Āiyᔉᔍrc;䅶;䐫r;쀀𝔜pf;쀀𝕐cr;쀀𝒴ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidtè૙a;䎖r;愨pf;愤cr;쀀𝒵௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻á䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻â䃢te肻´̆;䐰lig耻æ䃦Ā;r²ᖺ;쀀𝔞rave耻à䃠ĀepᗊᗖĀfpᗏᗔsym;愵èᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e»ᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢»¹arr;捼Āgpᙣᙧon;䄅f;쀀𝕒΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒñᚃing耻å䃥ƀctyᚡᚦᚨr;쀀𝒶;䀪mpĀ;e዁ᚯñʈilde耻ã䃣ml耻ä䃤Āciᛂᛈoninôɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e»ᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰séᜌnoõēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀𝔟g΀costuvwឍឝឳេ៕៛៞ƀaiuបពរðݠrc;旯p»፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄eåᑄåᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀𝕓Ā;tᏋᡣom»Ꮜtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻¦䂦Ȁceioᥑᥖᥚᥠr;쀀𝒷mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t»᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁îړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻ç䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻¸ƭptyv;榲t脀¢;eᨭᨮ䂢räƲr;쀀𝔠ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark»ᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟»ཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it»᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;qÇÆɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁îᅠeĀmx᫱᫶ent»᫩eóɍǧ᫾\0ᬇĀ;dኻᬂot;橭nôɆƀfryᬐᬔᬗ;쀀𝕔oäɔ脀©;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀𝒸Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒreã᭳uã᭵ee;拎edge;拏en耻¤䂤earrowĀlrᯮ᯳eft»ᮀight»ᮽeäᯝĀciᰁᰇoninôǷnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍rò΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸òᄳhĀ;vᱚᱛ怐»ऊūᱡᱧarow;椏aã̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻°䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀𝔡arĀlrᲳᲵ»ࣜ»သʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀÷;o᳧ᳰntimes;拇nø᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀𝕕ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedgåúnƀadhᄮᵝᵧownarrowóᲃarpoonĀlrᵲᵶefôᲴighôᲶŢᵿᶅkaro÷གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀𝒹;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃ròЩaòྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴoôᲉĀcsḎḔute耻é䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻ê䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀𝔢ƀ;rsṐṑṗ檚ave耻è䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et»ẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀𝕖ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on»ớ;䏵ȀcsuvỪỳἋἣĀioữḱrc»Ḯɩỹ\0\0ỻíՈantĀglἂἆtr»ṝess»Ṻƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯oô͒ĀahὉὋ;䎷耻ð䃰Āmrὓὗl耻ë䃫o;悬ƀcipὡὤὧl;䀡sôծĀeoὬὴctatioîՙnentialåչৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotseñṄy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀𝔣lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀𝕗ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻½䂽;慓耻¼䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻¾䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀𝒻ࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lanô٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀𝔤Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox»ℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀𝕘Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎proø₞r;楸qĀlqؿ↖lesó₈ií٫Āen↣↭rtneqq;쀀≩︀Å↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽ròΠȀilmr⇐⇔⇗⇛rsðᒄf»․ilôکĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it»∊lip;怦con;抹r;쀀𝔥sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀𝕙bar;怕ƀclt≯≴≸r;쀀𝒽asè⇴rok;䄧Ābp⊂⊇ull;恃hen»ᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻í䃭ƀ;iyݱ⊰⊵rc耻î䃮;䐸Ācx⊼⊿y;䐵cl耻¡䂡ĀfrΟ⋉;쀀𝔦rave耻ì䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓inåގarôܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝doô⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙eróᕣã⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀𝕚a;䎹uest耻¿䂿Āci⎊⎏r;쀀𝒾nʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻ï䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀𝔧ath;䈷pf;쀀𝕛ǣ⏬\0⏱r;쀀𝒿rcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀𝔨reen;䄸cy;䑅cy;䑜pf;쀀𝕜cr;쀀𝓀஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼rò৆òΕail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴raîࡌbda;䎻gƀ;dlࢎⓁⓃ;榑åࢎ;檅uo耻«䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝ë≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼ìࢰâ┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□aé⓶arpoonĀdu▯▴own»њp»०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoonó྘quigarro÷⇰hreetimes;拋ƀ;qs▋ও◺lanôবʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋pproøⓆot;拖qĀgq♃♅ôউgtò⒌ôছiíলƀilr♕࣡♚sht;楼;쀀𝔩Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖rò◁orneòᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che»⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox»⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽rëࣁgƀlmr⛿✍✔eftĀar০✇ightá৲apsto;柼ightá৽parrowĀlr✥✩efô⓭ight;憬ƀafl✶✹✽r;榅;쀀𝕝us;樭imes;樴š❋❏st;戗áፎƀ;ef❗❘᠀旊nge»❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇ròࢨorneòᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀𝓁mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹reå◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀Å⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻¯䂯Āet⡗⡙;時Ā;e⡞⡟朠se»⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻owîҌefôएðᏑker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle»ᘦr;쀀𝔪o;愧ƀcdn⢯⢴⣉ro耻µ䂵Ȁ;acdᑤ⢽⣀⣄sôᚧir;櫰ot肻·Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛ò−ðઁĀdp⣩⣮els;抧f;쀀𝕞Āct⣸⣽r;쀀𝓂pos»ᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la»˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉roø඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻 ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸uiöୣĀei⩊⩎ar;椨í஘istĀ;s஠டr;쀀𝔫ȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lanô௢ií௪Ā;rஶ⪁»ஷƀAap⪊⪍⪑rò⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹rò⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro÷⫁ightarro÷⪐ƀ;qs఻⪺⫪lanôౕĀ;sౕ⫴»శiíౝĀ;rవ⫾iĀ;eచథiäඐĀpt⬌⬑f;쀀𝕟膀¬;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lleì୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳uåಥĀ;cಘ⭸Ā;eಒ⭽ñಘȀAait⮈⮋⮝⮧rò⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow»⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉uå൅;쀀𝓃ortɭ⬅\0\0⯖ará⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭å೸åഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗñസȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇìௗlde耻ñ䃱çృiangleĀlrⱒⱜeftĀ;eచⱚñదightĀ;eೋⱥñ೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻ó䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻ô䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀𝔬ͯ⵹\0\0⵼\0ⶂn;䋛ave耻ò䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨrò᪀Āir⶝ⶠr;榾oss;榻nå๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀𝕠ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨rò᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f»ⷿ耻ª䂪耻º䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧ò⸁ash耻ø䃸l;折iŬⸯ⸴de耻õ䃵esĀ;aǛ⸺s;樶ml耻ö䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀¶;l⹭⹮䂶leìЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀𝔭ƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕maô੶ne;明ƀ;tv⺿⻀⻈䏀chfork»´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎ö⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻±ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀𝕡nd耻£䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷uå໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾pproø⽃urlyeñ໙ñ໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨iíໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺ð⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴ï໻rel;抰Āci⿀⿅r;쀀𝓅;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀𝔮pf;쀀𝕢rime;恗cr;쀀𝓆ƀaeo⿸〉〓tĀei⿾々rnionóڰnt;樖stĀ;e【】䀿ñἙô༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがròႳòϝail;検aròᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕iãᅮmptyv;榳gȀ;del࿑らるろ;榒;榥å࿑uo耻»䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞ë≝ð✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶aló༞ƀabrョリヮrò៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗ì࿲âヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜnåႻarôྩt;断ƀilrㅩဣㅮsht;楽;쀀𝔯ĀaoㅷㆆrĀduㅽㅿ»ѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭaéトarpoonĀduㆻㆿowîㅾp»႒eftĀah㇊㇐rrowó࿪arpoonóՑightarrows;應quigarro÷ニhreetimes;拌g;䋚ingdotseñἲƀahm㈍㈐㈓rò࿪aòՑ;怏oustĀ;a㈞㈟掱che»㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾rëဃƀafl㉇㉊㉎r;榆;쀀𝕣us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒arò㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀𝓇Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠reåㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛quï➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡uåᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓iíሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒ë∨Ā;oਸ਼਴t耻§䂧i;䀻war;椩mĀin㍩ðnuóñt;朶rĀ;o㍶⁕쀀𝔰Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜iäᑤaraì⹯耻­䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲aròᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetmé㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀𝕤aĀdr㑍ЂesĀ;u㑔㑕晠it»㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍ñᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝ñᆮƀ;afᅻ㒦ְrť㒫ֱ»ᅼaròᅈȀcemt㒹㒾㓂㓅r;쀀𝓈tmîñiì㐕aræᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psiloîỠhé⺯s»⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦pproø㋺urlyeñᇾñᇳƀaes㖂㖈㌛pproø㌚qñ㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻¹䂹耻²䂲耻³䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨ë∮Ā;oਫ਩war;椪lig耻ß䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄rë๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀𝔱Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮pproø዁im»ኬsðኞĀas㚺㚮ð዁rn耻þ䃾Ǭ̟㛆⋧es膀×;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀á⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀𝕥rk;櫚á㍢rime;怴ƀaip㜏㜒㝤dåቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own»ᶻeftĀ;e⠀㜾ñम;扜ightĀ;e㊪㝋ñၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀𝓉;䑆cy;䑛rok;䅧Āio㞋㞎xô᝷headĀlr㞗㞠eftarro÷ࡏightarrow»ཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶ròϭar;楣Ācr㟜㟢ute耻ú䃺òᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻û䃻;䑃ƀabh㠃㠆㠋ròᎭlac;䅱aòᏃĀir㠓㠘sht;楾;쀀𝔲rave耻ù䃹š㠧㠱rĀlr㠬㠮»ॗ»ႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r»㡆op;挏ri;旸Āal㡖㡚cr;䅫肻¨͉Āgp㡢㡦on;䅳f;쀀𝕦̀adhlsuᅋ㡸㡽፲㢑㢠ownáᎳarpoonĀlr㢈㢌efô㠭ighô㠯iƀ;hl㢙㢚㢜䏅»ᏺon»㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r»㢽op;挎ng;䅯ri;旹cr;쀀𝓊ƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨»᠓Āam㣯㣲rò㢨l耻ü䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠ròϷarĀ;v㤦㤧櫨;櫩asèϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖appá␕othinçẖƀhir㓫⻈㥙opô⾵Ā;hᎷ㥢ïㆍĀiu㥩㥭gmá㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟etá㚜iangleĀlr㦪㦯eft»थight»ၑy;䐲ash»ံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨaòᑩr;쀀𝔳tré㦮suĀbp㧯㧱»ജ»൙pf;쀀𝕧roð໻tré㦴Ācu㨆㨋r;쀀𝓋Ābp㨐㨘nĀEe㦀㨖»㥾nĀEe㦒㨞»㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀𝔴pf;쀀𝕨Ā;eᑹ㩦atèᑹcr;쀀𝓌ૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tré៑r;쀀𝔵ĀAa㪔㪗ròσrò৶;䎾ĀAa㪡㪤ròθrò৫að✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀𝕩imåឲĀAa㫇㫊ròώròਁĀcq㫒ីr;쀀𝓍Āpt៖㫜ré។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻ý䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻¥䂥r;쀀𝔶cy;䑗pf;쀀𝕪cr;쀀𝓎Ācm㬦㬩y;䑎l耻ÿ䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡træᕟa;䎶r;쀀𝔷cy;䐶grarr;懝pf;쀀𝕫cr;쀀𝓏Ājn㮅㮇;怍j;怌'.split("").map(e=>e.charCodeAt(0))),Xi=new Uint16Array("Ȁaglq	\x1Bɭ\0\0p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map(e=>e.charCodeAt(0)));var at;const Yi=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),Qi=(at=String.fromCodePoint)!==null&&at!==void 0?at:function(e){let u="";return e>65535&&(e-=65536,u+=String.fromCharCode(e>>>10&1023|55296),e=56320|e&1023),u+=String.fromCharCode(e),u};function Ji(e){var u;return e>=55296&&e<=57343||e>1114111?65533:(u=Yi.get(e))!==null&&u!==void 0?u:e}var ue;(function(e){e[e.NUM=35]="NUM",e[e.SEMI=59]="SEMI",e[e.EQUALS=61]="EQUALS",e[e.ZERO=48]="ZERO",e[e.NINE=57]="NINE",e[e.LOWER_A=97]="LOWER_A",e[e.LOWER_F=102]="LOWER_F",e[e.LOWER_X=120]="LOWER_X",e[e.LOWER_Z=122]="LOWER_Z",e[e.UPPER_A=65]="UPPER_A",e[e.UPPER_F=70]="UPPER_F",e[e.UPPER_Z=90]="UPPER_Z"})(ue||(ue={}));const Ki=32;var Ue;(function(e){e[e.VALUE_LENGTH=49152]="VALUE_LENGTH",e[e.BRANCH_LENGTH=16256]="BRANCH_LENGTH",e[e.JUMP_TABLE=127]="JUMP_TABLE"})(Ue||(Ue={}));function kt(e){return e>=ue.ZERO&&e<=ue.NINE}function eo(e){return e>=ue.UPPER_A&&e<=ue.UPPER_F||e>=ue.LOWER_A&&e<=ue.LOWER_F}function uo(e){return e>=ue.UPPER_A&&e<=ue.UPPER_Z||e>=ue.LOWER_A&&e<=ue.LOWER_Z||kt(e)}function to(e){return e===ue.EQUALS||uo(e)}var Q;(function(e){e[e.EntityStart=0]="EntityStart",e[e.NumericStart=1]="NumericStart",e[e.NumericDecimal=2]="NumericDecimal",e[e.NumericHex=3]="NumericHex",e[e.NamedEntity=4]="NamedEntity"})(Q||(Q={}));var He;(function(e){e[e.Legacy=0]="Legacy",e[e.Strict=1]="Strict",e[e.Attribute=2]="Attribute"})(He||(He={}));class no{constructor(u,t,n){this.decodeTree=u,this.emitCodePoint=t,this.errors=n,this.state=Q.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=He.Strict}startEntity(u){this.decodeMode=u,this.state=Q.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(u,t){switch(this.state){case Q.EntityStart:return u.charCodeAt(t)===ue.NUM?(this.state=Q.NumericStart,this.consumed+=1,this.stateNumericStart(u,t+1)):(this.state=Q.NamedEntity,this.stateNamedEntity(u,t));case Q.NumericStart:return this.stateNumericStart(u,t);case Q.NumericDecimal:return this.stateNumericDecimal(u,t);case Q.NumericHex:return this.stateNumericHex(u,t);case Q.NamedEntity:return this.stateNamedEntity(u,t)}}stateNumericStart(u,t){return t>=u.length?-1:(u.charCodeAt(t)|Ki)===ue.LOWER_X?(this.state=Q.NumericHex,this.consumed+=1,this.stateNumericHex(u,t+1)):(this.state=Q.NumericDecimal,this.stateNumericDecimal(u,t))}addToNumericResult(u,t,n,r){if(t!==n){const i=n-t;this.result=this.result*Math.pow(r,i)+parseInt(u.substr(t,i),r),this.consumed+=i}}stateNumericHex(u,t){const n=t;for(;t<u.length;){const r=u.charCodeAt(t);if(kt(r)||eo(r))t+=1;else return this.addToNumericResult(u,n,t,16),this.emitNumericEntity(r,3)}return this.addToNumericResult(u,n,t,16),-1}stateNumericDecimal(u,t){const n=t;for(;t<u.length;){const r=u.charCodeAt(t);if(kt(r))t+=1;else return this.addToNumericResult(u,n,t,10),this.emitNumericEntity(r,2)}return this.addToNumericResult(u,n,t,10),-1}emitNumericEntity(u,t){var n;if(this.consumed<=t)return(n=this.errors)===null||n===void 0||n.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(u===ue.SEMI)this.consumed+=1;else if(this.decodeMode===He.Strict)return 0;return this.emitCodePoint(Ji(this.result),this.consumed),this.errors&&(u!==ue.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(u,t){const{decodeTree:n}=this;let r=n[this.treeIndex],i=(r&Ue.VALUE_LENGTH)>>14;for(;t<u.length;t++,this.excess++){const o=u.charCodeAt(t);if(this.treeIndex=ro(n,r,this.treeIndex+Math.max(1,i),o),this.treeIndex<0)return this.result===0||this.decodeMode===He.Attribute&&(i===0||to(o))?0:this.emitNotTerminatedNamedEntity();if(r=n[this.treeIndex],i=(r&Ue.VALUE_LENGTH)>>14,i!==0){if(o===ue.SEMI)return this.emitNamedEntityData(this.treeIndex,i,this.consumed+this.excess);this.decodeMode!==He.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1}emitNotTerminatedNamedEntity(){var u;const{result:t,decodeTree:n}=this,r=(n[t]&Ue.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,r,this.consumed),(u=this.errors)===null||u===void 0||u.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(u,t,n){const{decodeTree:r}=this;return this.emitCodePoint(t===1?r[u]&~Ue.VALUE_LENGTH:r[u+1],n),t===3&&this.emitCodePoint(r[u+2],n),n}end(){var u;switch(this.state){case Q.NamedEntity:return this.result!==0&&(this.decodeMode!==He.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case Q.NumericDecimal:return this.emitNumericEntity(0,2);case Q.NumericHex:return this.emitNumericEntity(0,3);case Q.NumericStart:return(u=this.errors)===null||u===void 0||u.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case Q.EntityStart:return 0}}}function Zn(e){let u="";const t=new no(e,n=>u+=Qi(n));return function(r,i){let o=0,a=0;for(;(a=r.indexOf("&",a))>=0;){u+=r.slice(o,a),t.startEntity(i);const c=t.write(r,a+1);if(c<0){o=a+t.end();break}o=a+c,a=c===0?o+1:o}const s=u+r.slice(o);return u="",s}}function ro(e,u,t,n){const r=(u&Ue.BRANCH_LENGTH)>>7,i=u&Ue.JUMP_TABLE;if(r===0)return i!==0&&n===i?t:-1;if(i){const s=n-i;return s<0||s>=r?-1:e[t+s]-1}let o=t,a=o+r-1;for(;o<=a;){const s=o+a>>>1,c=e[s];if(c<n)o=s+1;else if(c>n)a=s-1;else return e[s+r]}return-1}const io=Zn(Zi);Zn(Xi);function Xn(e,u=He.Legacy){return io(e,u)}function oo(e){return Object.prototype.toString.call(e)}function Mt(e){return oo(e)==="[object String]"}const ao=Object.prototype.hasOwnProperty;function so(e,u){return ao.call(e,u)}function Vu(e){return Array.prototype.slice.call(arguments,1).forEach(function(t){if(t){if(typeof t!="object")throw new TypeError(t+"must be object");Object.keys(t).forEach(function(n){e[n]=t[n]})}}),e}function Yn(e,u,t){return[].concat(e.slice(0,u),t,e.slice(u+1))}function Pt(e){return!(e>=55296&&e<=57343||e>=64976&&e<=65007||(e&65535)===65535||(e&65535)===65534||e>=0&&e<=8||e===11||e>=14&&e<=31||e>=127&&e<=159||e>1114111)}function Hu(e){if(e>65535){e-=65536;const u=55296+(e>>10),t=56320+(e&1023);return String.fromCharCode(u,t)}return String.fromCharCode(e)}const Qn=/\\([!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~])/g,co=/&([a-z#][a-z0-9]{1,31});/gi,lo=new RegExp(Qn.source+"|"+co.source,"gi"),fo=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))$/i;function ho(e,u){if(u.charCodeAt(0)===35&&fo.test(u)){const n=u[1].toLowerCase()==="x"?parseInt(u.slice(2),16):parseInt(u.slice(1),10);return Pt(n)?Hu(n):e}const t=Xn(e);return t!==e?t:e}function po(e){return e.indexOf("\\")<0?e:e.replace(Qn,"$1")}function au(e){return e.indexOf("\\")<0&&e.indexOf("&")<0?e:e.replace(lo,function(u,t,n){return t||ho(u,n)})}const bo=/[&<>"]/,mo=/[&<>"]/g,go={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function xo(e){return go[e]}function We(e){return bo.test(e)?e.replace(mo,xo):e}const _o=/[.?*+^$[\]\\(){}|-]/g;function ko(e){return e.replace(_o,"\\$&")}function q(e){switch(e){case 9:case 32:return!0}return!1}function mu(e){if(e>=8192&&e<=8202)return!0;switch(e){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1}function gu(e){return Ot.test(e)||Gn.test(e)}function xu(e){switch(e){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}}function Zu(e){return e=e.trim().replace(/\s+/g," "),"ẞ".toLowerCase()==="Ṿ"&&(e=e.replace(/ẞ/g,"ß")),e.toLowerCase().toUpperCase()}const yo={mdurl:Wi,ucmicro:Vi},vo=Object.freeze(Object.defineProperty({__proto__:null,arrayReplaceAt:Yn,assign:Vu,escapeHtml:We,escapeRE:ko,fromCodePoint:Hu,has:so,isMdAsciiPunct:xu,isPunctChar:gu,isSpace:q,isString:Mt,isValidEntityCode:Pt,isWhiteSpace:mu,lib:yo,normalizeReference:Zu,unescapeAll:au,unescapeMd:po},Symbol.toStringTag,{value:"Module"}));function Co(e,u,t){let n,r,i,o;const a=e.posMax,s=e.pos;for(e.pos=u+1,n=1;e.pos<a;){if(i=e.src.charCodeAt(e.pos),i===93&&(n--,n===0)){r=!0;break}if(o=e.pos,e.md.inline.skipToken(e),i===91){if(o===e.pos-1)n++;else if(t)return e.pos=s,-1}}let c=-1;return r&&(c=e.pos),e.pos=s,c}function Eo(e,u,t){let n,r=u;const i={ok:!1,pos:0,str:""};if(e.charCodeAt(r)===60){for(r++;r<t;){if(n=e.charCodeAt(r),n===10||n===60)return i;if(n===62)return i.pos=r+1,i.str=au(e.slice(u+1,r)),i.ok=!0,i;if(n===92&&r+1<t){r+=2;continue}r++}return i}let o=0;for(;r<t&&(n=e.charCodeAt(r),!(n===32||n<32||n===127));){if(n===92&&r+1<t){if(e.charCodeAt(r+1)===32)break;r+=2;continue}if(n===40&&(o++,o>32))return i;if(n===41){if(o===0)break;o--}r++}return u===r||o!==0||(i.str=au(e.slice(u,r)),i.pos=r,i.ok=!0),i}function Ao(e,u,t,n){let r,i=u;const o={ok:!1,can_continue:!1,pos:0,str:"",marker:0};if(n)o.str=n.str,o.marker=n.marker;else{if(i>=t)return o;let a=e.charCodeAt(i);if(a!==34&&a!==39&&a!==40)return o;u++,i++,a===40&&(a=41),o.marker=a}for(;i<t;){if(r=e.charCodeAt(i),r===o.marker)return o.pos=i+1,o.str+=au(e.slice(u,i)),o.ok=!0,o;if(r===40&&o.marker===41)return o;r===92&&i+1<t&&i++,i++}return o.can_continue=!0,o.str+=au(e.slice(u,i)),o}const wo=Object.freeze(Object.defineProperty({__proto__:null,parseLinkDestination:Eo,parseLinkLabel:Co,parseLinkTitle:Ao},Symbol.toStringTag,{value:"Module"})),Ie={};Ie.code_inline=function(e,u,t,n,r){const i=e[u];return"<code"+r.renderAttrs(i)+">"+We(i.content)+"</code>"};Ie.code_block=function(e,u,t,n,r){const i=e[u];return"<pre"+r.renderAttrs(i)+"><code>"+We(e[u].content)+`</code></pre>
`};Ie.fence=function(e,u,t,n,r){const i=e[u],o=i.info?au(i.info).trim():"";let a="",s="";if(o){const l=o.split(/(\s+)/g);a=l[0],s=l.slice(2).join("")}let c;if(t.highlight?c=t.highlight(i.content,a,s)||We(i.content):c=We(i.content),c.indexOf("<pre")===0)return c+`
`;if(o){const l=i.attrIndex("class"),f=i.attrs?i.attrs.slice():[];l<0?f.push(["class",t.langPrefix+a]):(f[l]=f[l].slice(),f[l][1]+=" "+t.langPrefix+a);const p={attrs:f};return`<pre><code${r.renderAttrs(p)}>${c}</code></pre>
`}return`<pre><code${r.renderAttrs(i)}>${c}</code></pre>
`};Ie.image=function(e,u,t,n,r){const i=e[u];return i.attrs[i.attrIndex("alt")][1]=r.renderInlineAsText(i.children,t,n),r.renderToken(e,u,t)};Ie.hardbreak=function(e,u,t){return t.xhtmlOut?`<br />
`:`<br>
`};Ie.softbreak=function(e,u,t){return t.breaks?t.xhtmlOut?`<br />
`:`<br>
`:`
`};Ie.text=function(e,u){return We(e[u].content)};Ie.html_block=function(e,u){return e[u].content};Ie.html_inline=function(e,u){return e[u].content};function su(){this.rules=Vu({},Ie)}su.prototype.renderAttrs=function(u){let t,n,r;if(!u.attrs)return"";for(r="",t=0,n=u.attrs.length;t<n;t++)r+=" "+We(u.attrs[t][0])+'="'+We(u.attrs[t][1])+'"';return r};su.prototype.renderToken=function(u,t,n){const r=u[t];let i="";if(r.hidden)return"";r.block&&r.nesting!==-1&&t&&u[t-1].hidden&&(i+=`
`),i+=(r.nesting===-1?"</":"<")+r.tag,i+=this.renderAttrs(r),r.nesting===0&&n.xhtmlOut&&(i+=" /");let o=!1;if(r.block&&(o=!0,r.nesting===1&&t+1<u.length)){const a=u[t+1];(a.type==="inline"||a.hidden||a.nesting===-1&&a.tag===r.tag)&&(o=!1)}return i+=o?`>
`:">",i};su.prototype.renderInline=function(e,u,t){let n="";const r=this.rules;for(let i=0,o=e.length;i<o;i++){const a=e[i].type;typeof r[a]<"u"?n+=r[a](e,i,u,t,this):n+=this.renderToken(e,i,u)}return n};su.prototype.renderInlineAsText=function(e,u,t){let n="";for(let r=0,i=e.length;r<i;r++)switch(e[r].type){case"text":n+=e[r].content;break;case"image":n+=this.renderInlineAsText(e[r].children,u,t);break;case"html_inline":case"html_block":n+=e[r].content;break;case"softbreak":case"hardbreak":n+=`
`;break}return n};su.prototype.render=function(e,u,t){let n="";const r=this.rules;for(let i=0,o=e.length;i<o;i++){const a=e[i].type;a==="inline"?n+=this.renderInline(e[i].children,u,t):typeof r[a]<"u"?n+=r[a](e,i,u,t,this):n+=this.renderToken(e,i,u,t)}return n};function se(){this.__rules__=[],this.__cache__=null}se.prototype.__find__=function(e){for(let u=0;u<this.__rules__.length;u++)if(this.__rules__[u].name===e)return u;return-1};se.prototype.__compile__=function(){const e=this,u=[""];e.__rules__.forEach(function(t){t.enabled&&t.alt.forEach(function(n){u.indexOf(n)<0&&u.push(n)})}),e.__cache__={},u.forEach(function(t){e.__cache__[t]=[],e.__rules__.forEach(function(n){n.enabled&&(t&&n.alt.indexOf(t)<0||e.__cache__[t].push(n.fn))})})};se.prototype.at=function(e,u,t){const n=this.__find__(e),r=t||{};if(n===-1)throw new Error("Parser rule not found: "+e);this.__rules__[n].fn=u,this.__rules__[n].alt=r.alt||[],this.__cache__=null};se.prototype.before=function(e,u,t,n){const r=this.__find__(e),i=n||{};if(r===-1)throw new Error("Parser rule not found: "+e);this.__rules__.splice(r,0,{name:u,enabled:!0,fn:t,alt:i.alt||[]}),this.__cache__=null};se.prototype.after=function(e,u,t,n){const r=this.__find__(e),i=n||{};if(r===-1)throw new Error("Parser rule not found: "+e);this.__rules__.splice(r+1,0,{name:u,enabled:!0,fn:t,alt:i.alt||[]}),this.__cache__=null};se.prototype.push=function(e,u,t){const n=t||{};this.__rules__.push({name:e,enabled:!0,fn:u,alt:n.alt||[]}),this.__cache__=null};se.prototype.enable=function(e,u){Array.isArray(e)||(e=[e]);const t=[];return e.forEach(function(n){const r=this.__find__(n);if(r<0){if(u)return;throw new Error("Rules manager: invalid rule name "+n)}this.__rules__[r].enabled=!0,t.push(n)},this),this.__cache__=null,t};se.prototype.enableOnly=function(e,u){Array.isArray(e)||(e=[e]),this.__rules__.forEach(function(t){t.enabled=!1}),this.enable(e,u)};se.prototype.disable=function(e,u){Array.isArray(e)||(e=[e]);const t=[];return e.forEach(function(n){const r=this.__find__(n);if(r<0){if(u)return;throw new Error("Rules manager: invalid rule name "+n)}this.__rules__[r].enabled=!1,t.push(n)},this),this.__cache__=null,t};se.prototype.getRules=function(e){return this.__cache__===null&&this.__compile__(),this.__cache__[e]||[]};function De(e,u,t){this.type=e,this.tag=u,this.attrs=null,this.map=null,this.nesting=t,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}De.prototype.attrIndex=function(u){if(!this.attrs)return-1;const t=this.attrs;for(let n=0,r=t.length;n<r;n++)if(t[n][0]===u)return n;return-1};De.prototype.attrPush=function(u){this.attrs?this.attrs.push(u):this.attrs=[u]};De.prototype.attrSet=function(u,t){const n=this.attrIndex(u),r=[u,t];n<0?this.attrPush(r):this.attrs[n]=r};De.prototype.attrGet=function(u){const t=this.attrIndex(u);let n=null;return t>=0&&(n=this.attrs[t][1]),n};De.prototype.attrJoin=function(u,t){const n=this.attrIndex(u);n<0?this.attrPush([u,t]):this.attrs[n][1]=this.attrs[n][1]+" "+t};function Jn(e,u,t){this.src=e,this.env=t,this.tokens=[],this.inlineMode=!1,this.md=u}Jn.prototype.Token=De;const Do=/\r\n?|\n/g,Fo=/\0/g;function So(e){let u;u=e.src.replace(Do,`
`),u=u.replace(Fo,"�"),e.src=u}function To(e){let u;e.inlineMode?(u=new e.Token("inline","",0),u.content=e.src,u.map=[0,1],u.children=[],e.tokens.push(u)):e.md.block.parse(e.src,e.md,e.env,e.tokens)}function Io(e){const u=e.tokens;for(let t=0,n=u.length;t<n;t++){const r=u[t];r.type==="inline"&&e.md.inline.parse(r.content,e.md,e.env,r.children)}}function zo(e){return/^<a[>\s]/i.test(e)}function Lo(e){return/^<\/a\s*>/i.test(e)}function Ro(e){const u=e.tokens;if(e.md.options.linkify)for(let t=0,n=u.length;t<n;t++){if(u[t].type!=="inline"||!e.md.linkify.pretest(u[t].content))continue;let r=u[t].children,i=0;for(let o=r.length-1;o>=0;o--){const a=r[o];if(a.type==="link_close"){for(o--;r[o].level!==a.level&&r[o].type!=="link_open";)o--;continue}if(a.type==="html_inline"&&(zo(a.content)&&i>0&&i--,Lo(a.content)&&i++),!(i>0)&&a.type==="text"&&e.md.linkify.test(a.content)){const s=a.content;let c=e.md.linkify.match(s);const l=[];let f=a.level,p=0;c.length>0&&c[0].index===0&&o>0&&r[o-1].type==="text_special"&&(c=c.slice(1));for(let b=0;b<c.length;b++){const h=c[b].url,m=e.md.normalizeLink(h);if(!e.md.validateLink(m))continue;let x=c[b].text;c[b].schema?c[b].schema==="mailto:"&&!/^mailto:/i.test(x)?x=e.md.normalizeLinkText("mailto:"+x).replace(/^mailto:/,""):x=e.md.normalizeLinkText(x):x=e.md.normalizeLinkText("http://"+x).replace(/^http:\/\//,"");const g=c[b].index;if(g>p){const C=new e.Token("text","",0);C.content=s.slice(p,g),C.level=f,l.push(C)}const _=new e.Token("link_open","a",1);_.attrs=[["href",m]],_.level=f++,_.markup="linkify",_.info="auto",l.push(_);const y=new e.Token("text","",0);y.content=x,y.level=f,l.push(y);const v=new e.Token("link_close","a",-1);v.level=--f,v.markup="linkify",v.info="auto",l.push(v),p=c[b].lastIndex}if(p<s.length){const b=new e.Token("text","",0);b.content=s.slice(p),b.level=f,l.push(b)}u[t].children=r=Yn(r,o,l)}}}}const Kn=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,$o=/\((c|tm|r)\)/i,Oo=/\((c|tm|r)\)/ig,Mo={c:"©",r:"®",tm:"™"};function Po(e,u){return Mo[u.toLowerCase()]}function Bo(e){let u=0;for(let t=e.length-1;t>=0;t--){const n=e[t];n.type==="text"&&!u&&(n.content=n.content.replace(Oo,Po)),n.type==="link_open"&&n.info==="auto"&&u--,n.type==="link_close"&&n.info==="auto"&&u++}}function No(e){let u=0;for(let t=e.length-1;t>=0;t--){const n=e[t];n.type==="text"&&!u&&Kn.test(n.content)&&(n.content=n.content.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/mg,"$1—").replace(/(^|\s)--(?=\s|$)/mg,"$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/mg,"$1–")),n.type==="link_open"&&n.info==="auto"&&u--,n.type==="link_close"&&n.info==="auto"&&u++}}function qo(e){let u;if(e.md.options.typographer)for(u=e.tokens.length-1;u>=0;u--)e.tokens[u].type==="inline"&&($o.test(e.tokens[u].content)&&Bo(e.tokens[u].children),Kn.test(e.tokens[u].content)&&No(e.tokens[u].children))}const Ho=/['"]/,bn=/['"]/g,mn="’";function Fu(e,u,t){return e.slice(0,u)+t+e.slice(u+1)}function jo(e,u){let t;const n=[];for(let r=0;r<e.length;r++){const i=e[r],o=e[r].level;for(t=n.length-1;t>=0&&!(n[t].level<=o);t--);if(n.length=t+1,i.type!=="text")continue;let a=i.content,s=0,c=a.length;e:for(;s<c;){bn.lastIndex=s;const l=bn.exec(a);if(!l)break;let f=!0,p=!0;s=l.index+1;const b=l[0]==="'";let h=32;if(l.index-1>=0)h=a.charCodeAt(l.index-1);else for(t=r-1;t>=0&&!(e[t].type==="softbreak"||e[t].type==="hardbreak");t--)if(e[t].content){h=e[t].content.charCodeAt(e[t].content.length-1);break}let m=32;if(s<c)m=a.charCodeAt(s);else for(t=r+1;t<e.length&&!(e[t].type==="softbreak"||e[t].type==="hardbreak");t++)if(e[t].content){m=e[t].content.charCodeAt(0);break}const x=xu(h)||gu(String.fromCharCode(h)),g=xu(m)||gu(String.fromCharCode(m)),_=mu(h),y=mu(m);if(y?f=!1:g&&(_||x||(f=!1)),_?p=!1:x&&(y||g||(p=!1)),m===34&&l[0]==='"'&&h>=48&&h<=57&&(p=f=!1),f&&p&&(f=x,p=g),!f&&!p){b&&(i.content=Fu(i.content,l.index,mn));continue}if(p)for(t=n.length-1;t>=0;t--){let v=n[t];if(n[t].level<o)break;if(v.single===b&&n[t].level===o){v=n[t];let C,A;b?(C=u.md.options.quotes[2],A=u.md.options.quotes[3]):(C=u.md.options.quotes[0],A=u.md.options.quotes[1]),i.content=Fu(i.content,l.index,A),e[v.token].content=Fu(e[v.token].content,v.pos,C),s+=A.length-1,v.token===r&&(s+=C.length-1),a=i.content,c=a.length,n.length=t;continue e}}f?n.push({token:r,pos:l.index,single:b,level:o}):p&&b&&(i.content=Fu(i.content,l.index,mn))}}}function Uo(e){if(e.md.options.typographer)for(let u=e.tokens.length-1;u>=0;u--)e.tokens[u].type!=="inline"||!Ho.test(e.tokens[u].content)||jo(e.tokens[u].children,e)}function Wo(e){let u,t;const n=e.tokens,r=n.length;for(let i=0;i<r;i++){if(n[i].type!=="inline")continue;const o=n[i].children,a=o.length;for(u=0;u<a;u++)o[u].type==="text_special"&&(o[u].type="text");for(u=t=0;u<a;u++)o[u].type==="text"&&u+1<a&&o[u+1].type==="text"?o[u+1].content=o[u].content+o[u+1].content:(u!==t&&(o[t]=o[u]),t++);u!==t&&(o.length=t)}}const st=[["normalize",So],["block",To],["inline",Io],["linkify",Ro],["replacements",qo],["smartquotes",Uo],["text_join",Wo]];function Bt(){this.ruler=new se;for(let e=0;e<st.length;e++)this.ruler.push(st[e][0],st[e][1])}Bt.prototype.process=function(e){const u=this.ruler.getRules("");for(let t=0,n=u.length;t<n;t++)u[t](e)};Bt.prototype.State=Jn;function ze(e,u,t,n){this.src=e,this.md=u,this.env=t,this.tokens=n,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0;const r=this.src;for(let i=0,o=0,a=0,s=0,c=r.length,l=!1;o<c;o++){const f=r.charCodeAt(o);if(!l)if(q(f)){a++,f===9?s+=4-s%4:s++;continue}else l=!0;(f===10||o===c-1)&&(f!==10&&o++,this.bMarks.push(i),this.eMarks.push(o),this.tShift.push(a),this.sCount.push(s),this.bsCount.push(0),l=!1,a=0,s=0,i=o+1)}this.bMarks.push(r.length),this.eMarks.push(r.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}ze.prototype.push=function(e,u,t){const n=new De(e,u,t);return n.block=!0,t<0&&this.level--,n.level=this.level,t>0&&this.level++,this.tokens.push(n),n};ze.prototype.isEmpty=function(u){return this.bMarks[u]+this.tShift[u]>=this.eMarks[u]};ze.prototype.skipEmptyLines=function(u){for(let t=this.lineMax;u<t&&!(this.bMarks[u]+this.tShift[u]<this.eMarks[u]);u++);return u};ze.prototype.skipSpaces=function(u){for(let t=this.src.length;u<t;u++){const n=this.src.charCodeAt(u);if(!q(n))break}return u};ze.prototype.skipSpacesBack=function(u,t){if(u<=t)return u;for(;u>t;)if(!q(this.src.charCodeAt(--u)))return u+1;return u};ze.prototype.skipChars=function(u,t){for(let n=this.src.length;u<n&&this.src.charCodeAt(u)===t;u++);return u};ze.prototype.skipCharsBack=function(u,t,n){if(u<=n)return u;for(;u>n;)if(t!==this.src.charCodeAt(--u))return u+1;return u};ze.prototype.getLines=function(u,t,n,r){if(u>=t)return"";const i=new Array(t-u);for(let o=0,a=u;a<t;a++,o++){let s=0;const c=this.bMarks[a];let l=c,f;for(a+1<t||r?f=this.eMarks[a]+1:f=this.eMarks[a];l<f&&s<n;){const p=this.src.charCodeAt(l);if(q(p))p===9?s+=4-(s+this.bsCount[a])%4:s++;else if(l-c<this.tShift[a])s++;else break;l++}s>n?i[o]=new Array(s-n+1).join(" ")+this.src.slice(l,f):i[o]=this.src.slice(l,f)}return i.join("")};ze.prototype.Token=De;const Go=65536;function ct(e,u){const t=e.bMarks[u]+e.tShift[u],n=e.eMarks[u];return e.src.slice(t,n)}function gn(e){const u=[],t=e.length;let n=0,r=e.charCodeAt(n),i=!1,o=0,a="";for(;n<t;)r===124&&(i?(a+=e.substring(o,n-1),o=n):(u.push(a+e.substring(o,n)),a="",o=n+1)),i=r===92,n++,r=e.charCodeAt(n);return u.push(a+e.substring(o)),u}function Vo(e,u,t,n){if(u+2>t)return!1;let r=u+1;if(e.sCount[r]<e.blkIndent||e.sCount[r]-e.blkIndent>=4)return!1;let i=e.bMarks[r]+e.tShift[r];if(i>=e.eMarks[r])return!1;const o=e.src.charCodeAt(i++);if(o!==124&&o!==45&&o!==58||i>=e.eMarks[r])return!1;const a=e.src.charCodeAt(i++);if(a!==124&&a!==45&&a!==58&&!q(a)||o===45&&q(a))return!1;for(;i<e.eMarks[r];){const v=e.src.charCodeAt(i);if(v!==124&&v!==45&&v!==58&&!q(v))return!1;i++}let s=ct(e,u+1),c=s.split("|");const l=[];for(let v=0;v<c.length;v++){const C=c[v].trim();if(!C){if(v===0||v===c.length-1)continue;return!1}if(!/^:?-+:?$/.test(C))return!1;C.charCodeAt(C.length-1)===58?l.push(C.charCodeAt(0)===58?"center":"right"):C.charCodeAt(0)===58?l.push("left"):l.push("")}if(s=ct(e,u).trim(),s.indexOf("|")===-1||e.sCount[u]-e.blkIndent>=4)return!1;c=gn(s),c.length&&c[0]===""&&c.shift(),c.length&&c[c.length-1]===""&&c.pop();const f=c.length;if(f===0||f!==l.length)return!1;if(n)return!0;const p=e.parentType;e.parentType="table";const b=e.md.block.ruler.getRules("blockquote"),h=e.push("table_open","table",1),m=[u,0];h.map=m;const x=e.push("thead_open","thead",1);x.map=[u,u+1];const g=e.push("tr_open","tr",1);g.map=[u,u+1];for(let v=0;v<c.length;v++){const C=e.push("th_open","th",1);l[v]&&(C.attrs=[["style","text-align:"+l[v]]]);const A=e.push("inline","",0);A.content=c[v].trim(),A.children=[],e.push("th_close","th",-1)}e.push("tr_close","tr",-1),e.push("thead_close","thead",-1);let _,y=0;for(r=u+2;r<t&&!(e.sCount[r]<e.blkIndent);r++){let v=!1;for(let A=0,D=b.length;A<D;A++)if(b[A](e,r,t,!0)){v=!0;break}if(v||(s=ct(e,r).trim(),!s)||e.sCount[r]-e.blkIndent>=4||(c=gn(s),c.length&&c[0]===""&&c.shift(),c.length&&c[c.length-1]===""&&c.pop(),y+=f-c.length,y>Go))break;if(r===u+2){const A=e.push("tbody_open","tbody",1);A.map=_=[u+2,0]}const C=e.push("tr_open","tr",1);C.map=[r,r+1];for(let A=0;A<f;A++){const D=e.push("td_open","td",1);l[A]&&(D.attrs=[["style","text-align:"+l[A]]]);const k=e.push("inline","",0);k.content=c[A]?c[A].trim():"",k.children=[],e.push("td_close","td",-1)}e.push("tr_close","tr",-1)}return _&&(e.push("tbody_close","tbody",-1),_[1]=r),e.push("table_close","table",-1),m[1]=r,e.parentType=p,e.line=r,!0}function Zo(e,u,t){if(e.sCount[u]-e.blkIndent<4)return!1;let n=u+1,r=n;for(;n<t;){if(e.isEmpty(n)){n++;continue}if(e.sCount[n]-e.blkIndent>=4){n++,r=n;continue}break}e.line=r;const i=e.push("code_block","code",0);return i.content=e.getLines(u,r,4+e.blkIndent,!1)+`
`,i.map=[u,e.line],!0}function Xo(e,u,t,n){let r=e.bMarks[u]+e.tShift[u],i=e.eMarks[u];if(e.sCount[u]-e.blkIndent>=4||r+3>i)return!1;const o=e.src.charCodeAt(r);if(o!==126&&o!==96)return!1;let a=r;r=e.skipChars(r,o);let s=r-a;if(s<3)return!1;const c=e.src.slice(a,r),l=e.src.slice(r,i);if(o===96&&l.indexOf(String.fromCharCode(o))>=0)return!1;if(n)return!0;let f=u,p=!1;for(;f++,!(f>=t||(r=a=e.bMarks[f]+e.tShift[f],i=e.eMarks[f],r<i&&e.sCount[f]<e.blkIndent));)if(e.src.charCodeAt(r)===o&&!(e.sCount[f]-e.blkIndent>=4)&&(r=e.skipChars(r,o),!(r-a<s)&&(r=e.skipSpaces(r),!(r<i)))){p=!0;break}s=e.sCount[u],e.line=f+(p?1:0);const b=e.push("fence","code",0);return b.info=l,b.content=e.getLines(u+1,f,s,!0),b.markup=c,b.map=[u,e.line],!0}function Yo(e,u,t,n){let r=e.bMarks[u]+e.tShift[u],i=e.eMarks[u];const o=e.lineMax;if(e.sCount[u]-e.blkIndent>=4||e.src.charCodeAt(r)!==62)return!1;if(n)return!0;const a=[],s=[],c=[],l=[],f=e.md.block.ruler.getRules("blockquote"),p=e.parentType;e.parentType="blockquote";let b=!1,h;for(h=u;h<t;h++){const y=e.sCount[h]<e.blkIndent;if(r=e.bMarks[h]+e.tShift[h],i=e.eMarks[h],r>=i)break;if(e.src.charCodeAt(r++)===62&&!y){let C=e.sCount[h]+1,A,D;e.src.charCodeAt(r)===32?(r++,C++,D=!1,A=!0):e.src.charCodeAt(r)===9?(A=!0,(e.bsCount[h]+C)%4===3?(r++,C++,D=!1):D=!0):A=!1;let k=C;for(a.push(e.bMarks[h]),e.bMarks[h]=r;r<i;){const $=e.src.charCodeAt(r);if(q($))$===9?k+=4-(k+e.bsCount[h]+(D?1:0))%4:k++;else break;r++}b=r>=i,s.push(e.bsCount[h]),e.bsCount[h]=e.sCount[h]+1+(A?1:0),c.push(e.sCount[h]),e.sCount[h]=k-C,l.push(e.tShift[h]),e.tShift[h]=r-e.bMarks[h];continue}if(b)break;let v=!1;for(let C=0,A=f.length;C<A;C++)if(f[C](e,h,t,!0)){v=!0;break}if(v){e.lineMax=h,e.blkIndent!==0&&(a.push(e.bMarks[h]),s.push(e.bsCount[h]),l.push(e.tShift[h]),c.push(e.sCount[h]),e.sCount[h]-=e.blkIndent);break}a.push(e.bMarks[h]),s.push(e.bsCount[h]),l.push(e.tShift[h]),c.push(e.sCount[h]),e.sCount[h]=-1}const m=e.blkIndent;e.blkIndent=0;const x=e.push("blockquote_open","blockquote",1);x.markup=">";const g=[u,0];x.map=g,e.md.block.tokenize(e,u,h);const _=e.push("blockquote_close","blockquote",-1);_.markup=">",e.lineMax=o,e.parentType=p,g[1]=e.line;for(let y=0;y<l.length;y++)e.bMarks[y+u]=a[y],e.tShift[y+u]=l[y],e.sCount[y+u]=c[y],e.bsCount[y+u]=s[y];return e.blkIndent=m,!0}function Qo(e,u,t,n){const r=e.eMarks[u];if(e.sCount[u]-e.blkIndent>=4)return!1;let i=e.bMarks[u]+e.tShift[u];const o=e.src.charCodeAt(i++);if(o!==42&&o!==45&&o!==95)return!1;let a=1;for(;i<r;){const c=e.src.charCodeAt(i++);if(c!==o&&!q(c))return!1;c===o&&a++}if(a<3)return!1;if(n)return!0;e.line=u+1;const s=e.push("hr","hr",0);return s.map=[u,e.line],s.markup=Array(a+1).join(String.fromCharCode(o)),!0}function xn(e,u){const t=e.eMarks[u];let n=e.bMarks[u]+e.tShift[u];const r=e.src.charCodeAt(n++);if(r!==42&&r!==45&&r!==43)return-1;if(n<t){const i=e.src.charCodeAt(n);if(!q(i))return-1}return n}function _n(e,u){const t=e.bMarks[u]+e.tShift[u],n=e.eMarks[u];let r=t;if(r+1>=n)return-1;let i=e.src.charCodeAt(r++);if(i<48||i>57)return-1;for(;;){if(r>=n)return-1;if(i=e.src.charCodeAt(r++),i>=48&&i<=57){if(r-t>=10)return-1;continue}if(i===41||i===46)break;return-1}return r<n&&(i=e.src.charCodeAt(r),!q(i))?-1:r}function Jo(e,u){const t=e.level+2;for(let n=u+2,r=e.tokens.length-2;n<r;n++)e.tokens[n].level===t&&e.tokens[n].type==="paragraph_open"&&(e.tokens[n+2].hidden=!0,e.tokens[n].hidden=!0,n+=2)}function Ko(e,u,t,n){let r,i,o,a,s=u,c=!0;if(e.sCount[s]-e.blkIndent>=4||e.listIndent>=0&&e.sCount[s]-e.listIndent>=4&&e.sCount[s]<e.blkIndent)return!1;let l=!1;n&&e.parentType==="paragraph"&&e.sCount[s]>=e.blkIndent&&(l=!0);let f,p,b;if((b=_n(e,s))>=0){if(f=!0,o=e.bMarks[s]+e.tShift[s],p=Number(e.src.slice(o,b-1)),l&&p!==1)return!1}else if((b=xn(e,s))>=0)f=!1;else return!1;if(l&&e.skipSpaces(b)>=e.eMarks[s])return!1;if(n)return!0;const h=e.src.charCodeAt(b-1),m=e.tokens.length;f?(a=e.push("ordered_list_open","ol",1),p!==1&&(a.attrs=[["start",p]])):a=e.push("bullet_list_open","ul",1);const x=[s,0];a.map=x,a.markup=String.fromCharCode(h);let g=!1;const _=e.md.block.ruler.getRules("list"),y=e.parentType;for(e.parentType="list";s<t;){i=b,r=e.eMarks[s];const v=e.sCount[s]+b-(e.bMarks[s]+e.tShift[s]);let C=v;for(;i<r;){const N=e.src.charCodeAt(i);if(N===9)C+=4-(C+e.bsCount[s])%4;else if(N===32)C++;else break;i++}const A=i;let D;A>=r?D=1:D=C-v,D>4&&(D=1);const k=v+D;a=e.push("list_item_open","li",1),a.markup=String.fromCharCode(h);const $=[s,0];a.map=$,f&&(a.info=e.src.slice(o,b-1));const L=e.tight,B=e.tShift[s],S=e.sCount[s],z=e.listIndent;if(e.listIndent=e.blkIndent,e.blkIndent=k,e.tight=!0,e.tShift[s]=A-e.bMarks[s],e.sCount[s]=C,A>=r&&e.isEmpty(s+1)?e.line=Math.min(e.line+2,t):e.md.block.tokenize(e,s,t,!0),(!e.tight||g)&&(c=!1),g=e.line-s>1&&e.isEmpty(e.line-1),e.blkIndent=e.listIndent,e.listIndent=z,e.tShift[s]=B,e.sCount[s]=S,e.tight=L,a=e.push("list_item_close","li",-1),a.markup=String.fromCharCode(h),s=e.line,$[1]=s,s>=t||e.sCount[s]<e.blkIndent||e.sCount[s]-e.blkIndent>=4)break;let M=!1;for(let N=0,R=_.length;N<R;N++)if(_[N](e,s,t,!0)){M=!0;break}if(M)break;if(f){if(b=_n(e,s),b<0)break;o=e.bMarks[s]+e.tShift[s]}else if(b=xn(e,s),b<0)break;if(h!==e.src.charCodeAt(b-1))break}return f?a=e.push("ordered_list_close","ol",-1):a=e.push("bullet_list_close","ul",-1),a.markup=String.fromCharCode(h),x[1]=s,e.line=s,e.parentType=y,c&&Jo(e,m),!0}function e0(e,u,t,n){let r=e.bMarks[u]+e.tShift[u],i=e.eMarks[u],o=u+1;if(e.sCount[u]-e.blkIndent>=4||e.src.charCodeAt(r)!==91)return!1;function a(_){const y=e.lineMax;if(_>=y||e.isEmpty(_))return null;let v=!1;if(e.sCount[_]-e.blkIndent>3&&(v=!0),e.sCount[_]<0&&(v=!0),!v){const D=e.md.block.ruler.getRules("reference"),k=e.parentType;e.parentType="reference";let $=!1;for(let L=0,B=D.length;L<B;L++)if(D[L](e,_,y,!0)){$=!0;break}if(e.parentType=k,$)return null}const C=e.bMarks[_]+e.tShift[_],A=e.eMarks[_];return e.src.slice(C,A+1)}let s=e.src.slice(r,i+1);i=s.length;let c=-1;for(r=1;r<i;r++){const _=s.charCodeAt(r);if(_===91)return!1;if(_===93){c=r;break}else if(_===10){const y=a(o);y!==null&&(s+=y,i=s.length,o++)}else if(_===92&&(r++,r<i&&s.charCodeAt(r)===10)){const y=a(o);y!==null&&(s+=y,i=s.length,o++)}}if(c<0||s.charCodeAt(c+1)!==58)return!1;for(r=c+2;r<i;r++){const _=s.charCodeAt(r);if(_===10){const y=a(o);y!==null&&(s+=y,i=s.length,o++)}else if(!q(_))break}const l=e.md.helpers.parseLinkDestination(s,r,i);if(!l.ok)return!1;const f=e.md.normalizeLink(l.str);if(!e.md.validateLink(f))return!1;r=l.pos;const p=r,b=o,h=r;for(;r<i;r++){const _=s.charCodeAt(r);if(_===10){const y=a(o);y!==null&&(s+=y,i=s.length,o++)}else if(!q(_))break}let m=e.md.helpers.parseLinkTitle(s,r,i);for(;m.can_continue;){const _=a(o);if(_===null)break;s+=_,r=i,i=s.length,o++,m=e.md.helpers.parseLinkTitle(s,r,i,m)}let x;for(r<i&&h!==r&&m.ok?(x=m.str,r=m.pos):(x="",r=p,o=b);r<i;){const _=s.charCodeAt(r);if(!q(_))break;r++}if(r<i&&s.charCodeAt(r)!==10&&x)for(x="",r=p,o=b;r<i;){const _=s.charCodeAt(r);if(!q(_))break;r++}if(r<i&&s.charCodeAt(r)!==10)return!1;const g=Zu(s.slice(1,c));return g?(n||(typeof e.env.references>"u"&&(e.env.references={}),typeof e.env.references[g]>"u"&&(e.env.references[g]={title:x,href:f}),e.line=o),!0):!1}const u0=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],t0="[a-zA-Z_:][a-zA-Z0-9:._-]*",n0="[^\"'=<>`\\x00-\\x20]+",r0="'[^']*'",i0='"[^"]*"',o0="(?:"+n0+"|"+r0+"|"+i0+")",a0="(?:\\s+"+t0+"(?:\\s*=\\s*"+o0+")?)",er="<[A-Za-z][A-Za-z0-9\\-]*"+a0+"*\\s*\\/?>",ur="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",s0="<!---?>|<!--(?:[^-]|-[^-]|--[^>])*-->",c0="<[?][\\s\\S]*?[?]>",l0="<![A-Za-z][^>]*>",f0="<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",d0=new RegExp("^(?:"+er+"|"+ur+"|"+s0+"|"+c0+"|"+l0+"|"+f0+")"),h0=new RegExp("^(?:"+er+"|"+ur+")"),Ke=[[/^<(script|pre|style|textarea)(?=(\s|>|$))/i,/<\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+u0.join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(h0.source+"\\s*$"),/^$/,!1]];function p0(e,u,t,n){let r=e.bMarks[u]+e.tShift[u],i=e.eMarks[u];if(e.sCount[u]-e.blkIndent>=4||!e.md.options.html||e.src.charCodeAt(r)!==60)return!1;let o=e.src.slice(r,i),a=0;for(;a<Ke.length&&!Ke[a][0].test(o);a++);if(a===Ke.length)return!1;if(n)return Ke[a][2];let s=u+1;if(!Ke[a][1].test(o)){for(;s<t&&!(e.sCount[s]<e.blkIndent);s++)if(r=e.bMarks[s]+e.tShift[s],i=e.eMarks[s],o=e.src.slice(r,i),Ke[a][1].test(o)){o.length!==0&&s++;break}}e.line=s;const c=e.push("html_block","",0);return c.map=[u,s],c.content=e.getLines(u,s,e.blkIndent,!0),!0}function b0(e,u,t,n){let r=e.bMarks[u]+e.tShift[u],i=e.eMarks[u];if(e.sCount[u]-e.blkIndent>=4)return!1;let o=e.src.charCodeAt(r);if(o!==35||r>=i)return!1;let a=1;for(o=e.src.charCodeAt(++r);o===35&&r<i&&a<=6;)a++,o=e.src.charCodeAt(++r);if(a>6||r<i&&!q(o))return!1;if(n)return!0;i=e.skipSpacesBack(i,r);const s=e.skipCharsBack(i,35,r);s>r&&q(e.src.charCodeAt(s-1))&&(i=s),e.line=u+1;const c=e.push("heading_open","h"+String(a),1);c.markup="########".slice(0,a),c.map=[u,e.line];const l=e.push("inline","",0);l.content=e.src.slice(r,i).trim(),l.map=[u,e.line],l.children=[];const f=e.push("heading_close","h"+String(a),-1);return f.markup="########".slice(0,a),!0}function m0(e,u,t){const n=e.md.block.ruler.getRules("paragraph");if(e.sCount[u]-e.blkIndent>=4)return!1;const r=e.parentType;e.parentType="paragraph";let i=0,o,a=u+1;for(;a<t&&!e.isEmpty(a);a++){if(e.sCount[a]-e.blkIndent>3)continue;if(e.sCount[a]>=e.blkIndent){let b=e.bMarks[a]+e.tShift[a];const h=e.eMarks[a];if(b<h&&(o=e.src.charCodeAt(b),(o===45||o===61)&&(b=e.skipChars(b,o),b=e.skipSpaces(b),b>=h))){i=o===61?1:2;break}}if(e.sCount[a]<0)continue;let p=!1;for(let b=0,h=n.length;b<h;b++)if(n[b](e,a,t,!0)){p=!0;break}if(p)break}if(!i)return!1;const s=e.getLines(u,a,e.blkIndent,!1).trim();e.line=a+1;const c=e.push("heading_open","h"+String(i),1);c.markup=String.fromCharCode(o),c.map=[u,e.line];const l=e.push("inline","",0);l.content=s,l.map=[u,e.line-1],l.children=[];const f=e.push("heading_close","h"+String(i),-1);return f.markup=String.fromCharCode(o),e.parentType=r,!0}function g0(e,u,t){const n=e.md.block.ruler.getRules("paragraph"),r=e.parentType;let i=u+1;for(e.parentType="paragraph";i<t&&!e.isEmpty(i);i++){if(e.sCount[i]-e.blkIndent>3||e.sCount[i]<0)continue;let c=!1;for(let l=0,f=n.length;l<f;l++)if(n[l](e,i,t,!0)){c=!0;break}if(c)break}const o=e.getLines(u,i,e.blkIndent,!1).trim();e.line=i;const a=e.push("paragraph_open","p",1);a.map=[u,e.line];const s=e.push("inline","",0);return s.content=o,s.map=[u,e.line],s.children=[],e.push("paragraph_close","p",-1),e.parentType=r,!0}const Su=[["table",Vo,["paragraph","reference"]],["code",Zo],["fence",Xo,["paragraph","reference","blockquote","list"]],["blockquote",Yo,["paragraph","reference","blockquote","list"]],["hr",Qo,["paragraph","reference","blockquote","list"]],["list",Ko,["paragraph","reference","blockquote"]],["reference",e0],["html_block",p0,["paragraph","reference","blockquote"]],["heading",b0,["paragraph","reference","blockquote"]],["lheading",m0],["paragraph",g0]];function Xu(){this.ruler=new se;for(let e=0;e<Su.length;e++)this.ruler.push(Su[e][0],Su[e][1],{alt:(Su[e][2]||[]).slice()})}Xu.prototype.tokenize=function(e,u,t){const n=this.ruler.getRules(""),r=n.length,i=e.md.options.maxNesting;let o=u,a=!1;for(;o<t&&(e.line=o=e.skipEmptyLines(o),!(o>=t||e.sCount[o]<e.blkIndent));){if(e.level>=i){e.line=t;break}const s=e.line;let c=!1;for(let l=0;l<r;l++)if(c=n[l](e,o,t,!1),c){if(s>=e.line)throw new Error("block rule didn't increment state.line");break}if(!c)throw new Error("none of the block rules matched");e.tight=!a,e.isEmpty(e.line-1)&&(a=!0),o=e.line,o<t&&e.isEmpty(o)&&(a=!0,o++,e.line=o)}};Xu.prototype.parse=function(e,u,t,n){if(!e)return;const r=new this.State(e,u,t,n);this.tokenize(r,r.line,r.lineMax)};Xu.prototype.State=ze;function Cu(e,u,t,n){this.src=e,this.env=t,this.md=u,this.tokens=n,this.tokens_meta=Array(n.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1,this.linkLevel=0}Cu.prototype.pushPending=function(){const e=new De("text","",0);return e.content=this.pending,e.level=this.pendingLevel,this.tokens.push(e),this.pending="",e};Cu.prototype.push=function(e,u,t){this.pending&&this.pushPending();const n=new De(e,u,t);let r=null;return t<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),n.level=this.level,t>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],r={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(n),this.tokens_meta.push(r),n};Cu.prototype.scanDelims=function(e,u){const t=this.posMax,n=this.src.charCodeAt(e),r=e>0?this.src.charCodeAt(e-1):32;let i=e;for(;i<t&&this.src.charCodeAt(i)===n;)i++;const o=i-e,a=i<t?this.src.charCodeAt(i):32,s=xu(r)||gu(String.fromCharCode(r)),c=xu(a)||gu(String.fromCharCode(a)),l=mu(r),f=mu(a),p=!f&&(!c||l||s),b=!l&&(!s||f||c);return{can_open:p&&(u||!b||s),can_close:b&&(u||!p||c),length:o}};Cu.prototype.Token=De;function x0(e){switch(e){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}function _0(e,u){let t=e.pos;for(;t<e.posMax&&!x0(e.src.charCodeAt(t));)t++;return t===e.pos?!1:(u||(e.pending+=e.src.slice(e.pos,t)),e.pos=t,!0)}const k0=/(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i;function y0(e,u){if(!e.md.options.linkify||e.linkLevel>0)return!1;const t=e.pos,n=e.posMax;if(t+3>n||e.src.charCodeAt(t)!==58||e.src.charCodeAt(t+1)!==47||e.src.charCodeAt(t+2)!==47)return!1;const r=e.pending.match(k0);if(!r)return!1;const i=r[1],o=e.md.linkify.matchAtStart(e.src.slice(t-i.length));if(!o)return!1;let a=o.url;if(a.length<=i.length)return!1;a=a.replace(/\*+$/,"");const s=e.md.normalizeLink(a);if(!e.md.validateLink(s))return!1;if(!u){e.pending=e.pending.slice(0,-i.length);const c=e.push("link_open","a",1);c.attrs=[["href",s]],c.markup="linkify",c.info="auto";const l=e.push("text","",0);l.content=e.md.normalizeLinkText(a);const f=e.push("link_close","a",-1);f.markup="linkify",f.info="auto"}return e.pos+=a.length-i.length,!0}function v0(e,u){let t=e.pos;if(e.src.charCodeAt(t)!==10)return!1;const n=e.pending.length-1,r=e.posMax;if(!u)if(n>=0&&e.pending.charCodeAt(n)===32)if(n>=1&&e.pending.charCodeAt(n-1)===32){let i=n-1;for(;i>=1&&e.pending.charCodeAt(i-1)===32;)i--;e.pending=e.pending.slice(0,i),e.push("hardbreak","br",0)}else e.pending=e.pending.slice(0,-1),e.push("softbreak","br",0);else e.push("softbreak","br",0);for(t++;t<r&&q(e.src.charCodeAt(t));)t++;return e.pos=t,!0}const Nt=[];for(let e=0;e<256;e++)Nt.push(0);"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach(function(e){Nt[e.charCodeAt(0)]=1});function C0(e,u){let t=e.pos;const n=e.posMax;if(e.src.charCodeAt(t)!==92||(t++,t>=n))return!1;let r=e.src.charCodeAt(t);if(r===10){for(u||e.push("hardbreak","br",0),t++;t<n&&(r=e.src.charCodeAt(t),!!q(r));)t++;return e.pos=t,!0}let i=e.src[t];if(r>=55296&&r<=56319&&t+1<n){const a=e.src.charCodeAt(t+1);a>=56320&&a<=57343&&(i+=e.src[t+1],t++)}const o="\\"+i;if(!u){const a=e.push("text_special","",0);r<256&&Nt[r]!==0?a.content=i:a.content=o,a.markup=o,a.info="escape"}return e.pos=t+1,!0}function E0(e,u){let t=e.pos;if(e.src.charCodeAt(t)!==96)return!1;const r=t;t++;const i=e.posMax;for(;t<i&&e.src.charCodeAt(t)===96;)t++;const o=e.src.slice(r,t),a=o.length;if(e.backticksScanned&&(e.backticks[a]||0)<=r)return u||(e.pending+=o),e.pos+=a,!0;let s=t,c;for(;(c=e.src.indexOf("`",s))!==-1;){for(s=c+1;s<i&&e.src.charCodeAt(s)===96;)s++;const l=s-c;if(l===a){if(!u){const f=e.push("code_inline","code",0);f.markup=o,f.content=e.src.slice(t,c).replace(/\n/g," ").replace(/^ (.+) $/,"$1")}return e.pos=s,!0}e.backticks[l]=c}return e.backticksScanned=!0,u||(e.pending+=o),e.pos+=a,!0}function A0(e,u){const t=e.pos,n=e.src.charCodeAt(t);if(u||n!==126)return!1;const r=e.scanDelims(e.pos,!0);let i=r.length;const o=String.fromCharCode(n);if(i<2)return!1;let a;i%2&&(a=e.push("text","",0),a.content=o,i--);for(let s=0;s<i;s+=2)a=e.push("text","",0),a.content=o+o,e.delimiters.push({marker:n,length:0,token:e.tokens.length-1,end:-1,open:r.can_open,close:r.can_close});return e.pos+=r.length,!0}function kn(e,u){let t;const n=[],r=u.length;for(let i=0;i<r;i++){const o=u[i];if(o.marker!==126||o.end===-1)continue;const a=u[o.end];t=e.tokens[o.token],t.type="s_open",t.tag="s",t.nesting=1,t.markup="~~",t.content="",t=e.tokens[a.token],t.type="s_close",t.tag="s",t.nesting=-1,t.markup="~~",t.content="",e.tokens[a.token-1].type==="text"&&e.tokens[a.token-1].content==="~"&&n.push(a.token-1)}for(;n.length;){const i=n.pop();let o=i+1;for(;o<e.tokens.length&&e.tokens[o].type==="s_close";)o++;o--,i!==o&&(t=e.tokens[o],e.tokens[o]=e.tokens[i],e.tokens[i]=t)}}function w0(e){const u=e.tokens_meta,t=e.tokens_meta.length;kn(e,e.delimiters);for(let n=0;n<t;n++)u[n]&&u[n].delimiters&&kn(e,u[n].delimiters)}const tr={tokenize:A0,postProcess:w0};function D0(e,u){const t=e.pos,n=e.src.charCodeAt(t);if(u||n!==95&&n!==42)return!1;const r=e.scanDelims(e.pos,n===42);for(let i=0;i<r.length;i++){const o=e.push("text","",0);o.content=String.fromCharCode(n),e.delimiters.push({marker:n,length:r.length,token:e.tokens.length-1,end:-1,open:r.can_open,close:r.can_close})}return e.pos+=r.length,!0}function yn(e,u){const t=u.length;for(let n=t-1;n>=0;n--){const r=u[n];if(r.marker!==95&&r.marker!==42||r.end===-1)continue;const i=u[r.end],o=n>0&&u[n-1].end===r.end+1&&u[n-1].marker===r.marker&&u[n-1].token===r.token-1&&u[r.end+1].token===i.token+1,a=String.fromCharCode(r.marker),s=e.tokens[r.token];s.type=o?"strong_open":"em_open",s.tag=o?"strong":"em",s.nesting=1,s.markup=o?a+a:a,s.content="";const c=e.tokens[i.token];c.type=o?"strong_close":"em_close",c.tag=o?"strong":"em",c.nesting=-1,c.markup=o?a+a:a,c.content="",o&&(e.tokens[u[n-1].token].content="",e.tokens[u[r.end+1].token].content="",n--)}}function F0(e){const u=e.tokens_meta,t=e.tokens_meta.length;yn(e,e.delimiters);for(let n=0;n<t;n++)u[n]&&u[n].delimiters&&yn(e,u[n].delimiters)}const nr={tokenize:D0,postProcess:F0};function S0(e,u){let t,n,r,i,o="",a="",s=e.pos,c=!0;if(e.src.charCodeAt(e.pos)!==91)return!1;const l=e.pos,f=e.posMax,p=e.pos+1,b=e.md.helpers.parseLinkLabel(e,e.pos,!0);if(b<0)return!1;let h=b+1;if(h<f&&e.src.charCodeAt(h)===40){for(c=!1,h++;h<f&&(t=e.src.charCodeAt(h),!(!q(t)&&t!==10));h++);if(h>=f)return!1;if(s=h,r=e.md.helpers.parseLinkDestination(e.src,h,e.posMax),r.ok){for(o=e.md.normalizeLink(r.str),e.md.validateLink(o)?h=r.pos:o="",s=h;h<f&&(t=e.src.charCodeAt(h),!(!q(t)&&t!==10));h++);if(r=e.md.helpers.parseLinkTitle(e.src,h,e.posMax),h<f&&s!==h&&r.ok)for(a=r.str,h=r.pos;h<f&&(t=e.src.charCodeAt(h),!(!q(t)&&t!==10));h++);}(h>=f||e.src.charCodeAt(h)!==41)&&(c=!0),h++}if(c){if(typeof e.env.references>"u")return!1;if(h<f&&e.src.charCodeAt(h)===91?(s=h+1,h=e.md.helpers.parseLinkLabel(e,h),h>=0?n=e.src.slice(s,h++):h=b+1):h=b+1,n||(n=e.src.slice(p,b)),i=e.env.references[Zu(n)],!i)return e.pos=l,!1;o=i.href,a=i.title}if(!u){e.pos=p,e.posMax=b;const m=e.push("link_open","a",1),x=[["href",o]];m.attrs=x,a&&x.push(["title",a]),e.linkLevel++,e.md.inline.tokenize(e),e.linkLevel--,e.push("link_close","a",-1)}return e.pos=h,e.posMax=f,!0}function T0(e,u){let t,n,r,i,o,a,s,c,l="";const f=e.pos,p=e.posMax;if(e.src.charCodeAt(e.pos)!==33||e.src.charCodeAt(e.pos+1)!==91)return!1;const b=e.pos+2,h=e.md.helpers.parseLinkLabel(e,e.pos+1,!1);if(h<0)return!1;if(i=h+1,i<p&&e.src.charCodeAt(i)===40){for(i++;i<p&&(t=e.src.charCodeAt(i),!(!q(t)&&t!==10));i++);if(i>=p)return!1;for(c=i,a=e.md.helpers.parseLinkDestination(e.src,i,e.posMax),a.ok&&(l=e.md.normalizeLink(a.str),e.md.validateLink(l)?i=a.pos:l=""),c=i;i<p&&(t=e.src.charCodeAt(i),!(!q(t)&&t!==10));i++);if(a=e.md.helpers.parseLinkTitle(e.src,i,e.posMax),i<p&&c!==i&&a.ok)for(s=a.str,i=a.pos;i<p&&(t=e.src.charCodeAt(i),!(!q(t)&&t!==10));i++);else s="";if(i>=p||e.src.charCodeAt(i)!==41)return e.pos=f,!1;i++}else{if(typeof e.env.references>"u")return!1;if(i<p&&e.src.charCodeAt(i)===91?(c=i+1,i=e.md.helpers.parseLinkLabel(e,i),i>=0?r=e.src.slice(c,i++):i=h+1):i=h+1,r||(r=e.src.slice(b,h)),o=e.env.references[Zu(r)],!o)return e.pos=f,!1;l=o.href,s=o.title}if(!u){n=e.src.slice(b,h);const m=[];e.md.inline.parse(n,e.md,e.env,m);const x=e.push("image","img",0),g=[["src",l],["alt",""]];x.attrs=g,x.children=m,x.content=n,s&&g.push(["title",s])}return e.pos=i,e.posMax=p,!0}const I0=/^([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,z0=/^([a-zA-Z][a-zA-Z0-9+.-]{1,31}):([^<>\x00-\x20]*)$/;function L0(e,u){let t=e.pos;if(e.src.charCodeAt(t)!==60)return!1;const n=e.pos,r=e.posMax;for(;;){if(++t>=r)return!1;const o=e.src.charCodeAt(t);if(o===60)return!1;if(o===62)break}const i=e.src.slice(n+1,t);if(z0.test(i)){const o=e.md.normalizeLink(i);if(!e.md.validateLink(o))return!1;if(!u){const a=e.push("link_open","a",1);a.attrs=[["href",o]],a.markup="autolink",a.info="auto";const s=e.push("text","",0);s.content=e.md.normalizeLinkText(i);const c=e.push("link_close","a",-1);c.markup="autolink",c.info="auto"}return e.pos+=i.length+2,!0}if(I0.test(i)){const o=e.md.normalizeLink("mailto:"+i);if(!e.md.validateLink(o))return!1;if(!u){const a=e.push("link_open","a",1);a.attrs=[["href",o]],a.markup="autolink",a.info="auto";const s=e.push("text","",0);s.content=e.md.normalizeLinkText(i);const c=e.push("link_close","a",-1);c.markup="autolink",c.info="auto"}return e.pos+=i.length+2,!0}return!1}function R0(e){return/^<a[>\s]/i.test(e)}function $0(e){return/^<\/a\s*>/i.test(e)}function O0(e){const u=e|32;return u>=97&&u<=122}function M0(e,u){if(!e.md.options.html)return!1;const t=e.posMax,n=e.pos;if(e.src.charCodeAt(n)!==60||n+2>=t)return!1;const r=e.src.charCodeAt(n+1);if(r!==33&&r!==63&&r!==47&&!O0(r))return!1;const i=e.src.slice(n).match(d0);if(!i)return!1;if(!u){const o=e.push("html_inline","",0);o.content=i[0],R0(o.content)&&e.linkLevel++,$0(o.content)&&e.linkLevel--}return e.pos+=i[0].length,!0}const P0=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,B0=/^&([a-z][a-z0-9]{1,31});/i;function N0(e,u){const t=e.pos,n=e.posMax;if(e.src.charCodeAt(t)!==38||t+1>=n)return!1;if(e.src.charCodeAt(t+1)===35){const i=e.src.slice(t).match(P0);if(i){if(!u){const o=i[1][0].toLowerCase()==="x"?parseInt(i[1].slice(1),16):parseInt(i[1],10),a=e.push("text_special","",0);a.content=Pt(o)?Hu(o):Hu(65533),a.markup=i[0],a.info="entity"}return e.pos+=i[0].length,!0}}else{const i=e.src.slice(t).match(B0);if(i){const o=Xn(i[0]);if(o!==i[0]){if(!u){const a=e.push("text_special","",0);a.content=o,a.markup=i[0],a.info="entity"}return e.pos+=i[0].length,!0}}}return!1}function vn(e){const u={},t=e.length;if(!t)return;let n=0,r=-2;const i=[];for(let o=0;o<t;o++){const a=e[o];if(i.push(0),(e[n].marker!==a.marker||r!==a.token-1)&&(n=o),r=a.token,a.length=a.length||0,!a.close)continue;u.hasOwnProperty(a.marker)||(u[a.marker]=[-1,-1,-1,-1,-1,-1]);const s=u[a.marker][(a.open?3:0)+a.length%3];let c=n-i[n]-1,l=c;for(;c>s;c-=i[c]+1){const f=e[c];if(f.marker===a.marker&&f.open&&f.end<0){let p=!1;if((f.close||a.open)&&(f.length+a.length)%3===0&&(f.length%3!==0||a.length%3!==0)&&(p=!0),!p){const b=c>0&&!e[c-1].open?i[c-1]+1:0;i[o]=o-c+b,i[c]=b,a.open=!1,f.end=o,f.close=!1,l=-1,r=-2;break}}}l!==-1&&(u[a.marker][(a.open?3:0)+(a.length||0)%3]=l)}}function q0(e){const u=e.tokens_meta,t=e.tokens_meta.length;vn(e.delimiters);for(let n=0;n<t;n++)u[n]&&u[n].delimiters&&vn(u[n].delimiters)}function H0(e){let u,t,n=0;const r=e.tokens,i=e.tokens.length;for(u=t=0;u<i;u++)r[u].nesting<0&&n--,r[u].level=n,r[u].nesting>0&&n++,r[u].type==="text"&&u+1<i&&r[u+1].type==="text"?r[u+1].content=r[u].content+r[u+1].content:(u!==t&&(r[t]=r[u]),t++);u!==t&&(r.length=t)}const lt=[["text",_0],["linkify",y0],["newline",v0],["escape",C0],["backticks",E0],["strikethrough",tr.tokenize],["emphasis",nr.tokenize],["link",S0],["image",T0],["autolink",L0],["html_inline",M0],["entity",N0]],ft=[["balance_pairs",q0],["strikethrough",tr.postProcess],["emphasis",nr.postProcess],["fragments_join",H0]];function Eu(){this.ruler=new se;for(let e=0;e<lt.length;e++)this.ruler.push(lt[e][0],lt[e][1]);this.ruler2=new se;for(let e=0;e<ft.length;e++)this.ruler2.push(ft[e][0],ft[e][1])}Eu.prototype.skipToken=function(e){const u=e.pos,t=this.ruler.getRules(""),n=t.length,r=e.md.options.maxNesting,i=e.cache;if(typeof i[u]<"u"){e.pos=i[u];return}let o=!1;if(e.level<r){for(let a=0;a<n;a++)if(e.level++,o=t[a](e,!0),e.level--,o){if(u>=e.pos)throw new Error("inline rule didn't increment state.pos");break}}else e.pos=e.posMax;o||e.pos++,i[u]=e.pos};Eu.prototype.tokenize=function(e){const u=this.ruler.getRules(""),t=u.length,n=e.posMax,r=e.md.options.maxNesting;for(;e.pos<n;){const i=e.pos;let o=!1;if(e.level<r){for(let a=0;a<t;a++)if(o=u[a](e,!1),o){if(i>=e.pos)throw new Error("inline rule didn't increment state.pos");break}}if(o){if(e.pos>=n)break;continue}e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()};Eu.prototype.parse=function(e,u,t,n){const r=new this.State(e,u,t,n);this.tokenize(r);const i=this.ruler2.getRules(""),o=i.length;for(let a=0;a<o;a++)i[a](r)};Eu.prototype.State=Cu;function j0(e){const u={};e=e||{},u.src_Any=Un.source,u.src_Cc=Wn.source,u.src_Z=Vn.source,u.src_P=Ot.source,u.src_ZPCc=[u.src_Z,u.src_P,u.src_Cc].join("|"),u.src_ZCc=[u.src_Z,u.src_Cc].join("|");const t="[><｜]";return u.src_pseudo_letter="(?:(?!"+t+"|"+u.src_ZPCc+")"+u.src_Any+")",u.src_ip4="(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)",u.src_auth="(?:(?:(?!"+u.src_ZCc+"|[@/\\[\\]()]).)+@)?",u.src_port="(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?",u.src_host_terminator="(?=$|"+t+"|"+u.src_ZPCc+")(?!"+(e["---"]?"-(?!--)|":"-|")+"_|:\\d|\\.-|\\.(?!$|"+u.src_ZPCc+"))",u.src_path="(?:[/?#](?:(?!"+u.src_ZCc+"|"+t+`|[()[\\]{}.,"'?!\\-;]).|\\[(?:(?!`+u.src_ZCc+"|\\]).)*\\]|\\((?:(?!"+u.src_ZCc+"|[)]).)*\\)|\\{(?:(?!"+u.src_ZCc+'|[}]).)*\\}|\\"(?:(?!'+u.src_ZCc+`|["]).)+\\"|\\'(?:(?!`+u.src_ZCc+"|[']).)+\\'|\\'(?="+u.src_pseudo_letter+"|[-])|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!"+u.src_ZCc+"|[.]|$)|"+(e["---"]?"\\-(?!--(?:[^-]|$))(?:-*)|":"\\-+|")+",(?!"+u.src_ZCc+"|$)|;(?!"+u.src_ZCc+"|$)|\\!+(?!"+u.src_ZCc+"|[!]|$)|\\?(?!"+u.src_ZCc+"|[?]|$))+|\\/)?",u.src_email_name='[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*',u.src_xn="xn--[a-z0-9\\-]{1,59}",u.src_domain_root="(?:"+u.src_xn+"|"+u.src_pseudo_letter+"{1,63})",u.src_domain="(?:"+u.src_xn+"|(?:"+u.src_pseudo_letter+")|(?:"+u.src_pseudo_letter+"(?:-|"+u.src_pseudo_letter+"){0,61}"+u.src_pseudo_letter+"))",u.src_host="(?:(?:(?:(?:"+u.src_domain+")\\.)*"+u.src_domain+"))",u.tpl_host_fuzzy="(?:"+u.src_ip4+"|(?:(?:(?:"+u.src_domain+")\\.)+(?:%TLDS%)))",u.tpl_host_no_ip_fuzzy="(?:(?:(?:"+u.src_domain+")\\.)+(?:%TLDS%))",u.src_host_strict=u.src_host+u.src_host_terminator,u.tpl_host_fuzzy_strict=u.tpl_host_fuzzy+u.src_host_terminator,u.src_host_port_strict=u.src_host+u.src_port+u.src_host_terminator,u.tpl_host_port_fuzzy_strict=u.tpl_host_fuzzy+u.src_port+u.src_host_terminator,u.tpl_host_port_no_ip_fuzzy_strict=u.tpl_host_no_ip_fuzzy+u.src_port+u.src_host_terminator,u.tpl_host_fuzzy_test="localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:"+u.src_ZPCc+"|>|$))",u.tpl_email_fuzzy="(^|"+t+'|"|\\(|'+u.src_ZCc+")("+u.src_email_name+"@"+u.tpl_host_fuzzy_strict+")",u.tpl_link_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+u.src_ZPCc+"))((?![$+<=>^`|｜])"+u.tpl_host_port_fuzzy_strict+u.src_path+")",u.tpl_link_no_ip_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+u.src_ZPCc+"))((?![$+<=>^`|｜])"+u.tpl_host_port_no_ip_fuzzy_strict+u.src_path+")",u}function yt(e){return Array.prototype.slice.call(arguments,1).forEach(function(t){t&&Object.keys(t).forEach(function(n){e[n]=t[n]})}),e}function Yu(e){return Object.prototype.toString.call(e)}function U0(e){return Yu(e)==="[object String]"}function W0(e){return Yu(e)==="[object Object]"}function G0(e){return Yu(e)==="[object RegExp]"}function Cn(e){return Yu(e)==="[object Function]"}function V0(e){return e.replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}const rr={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1};function Z0(e){return Object.keys(e||{}).reduce(function(u,t){return u||rr.hasOwnProperty(t)},!1)}const X0={"http:":{validate:function(e,u,t){const n=e.slice(u);return t.re.http||(t.re.http=new RegExp("^\\/\\/"+t.re.src_auth+t.re.src_host_port_strict+t.re.src_path,"i")),t.re.http.test(n)?n.match(t.re.http)[0].length:0}},"https:":"http:","ftp:":"http:","//":{validate:function(e,u,t){const n=e.slice(u);return t.re.no_http||(t.re.no_http=new RegExp("^"+t.re.src_auth+"(?:localhost|(?:(?:"+t.re.src_domain+")\\.)+"+t.re.src_domain_root+")"+t.re.src_port+t.re.src_host_terminator+t.re.src_path,"i")),t.re.no_http.test(n)?u>=3&&e[u-3]===":"||u>=3&&e[u-3]==="/"?0:n.match(t.re.no_http)[0].length:0}},"mailto:":{validate:function(e,u,t){const n=e.slice(u);return t.re.mailto||(t.re.mailto=new RegExp("^"+t.re.src_email_name+"@"+t.re.src_host_strict,"i")),t.re.mailto.test(n)?n.match(t.re.mailto)[0].length:0}}},Y0="a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]",Q0="biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");function J0(e){e.__index__=-1,e.__text_cache__=""}function K0(e){return function(u,t){const n=u.slice(t);return e.test(n)?n.match(e)[0].length:0}}function En(){return function(e,u){u.normalize(e)}}function ju(e){const u=e.re=j0(e.__opts__),t=e.__tlds__.slice();e.onCompile(),e.__tlds_replaced__||t.push(Y0),t.push(u.src_xn),u.src_tlds=t.join("|");function n(a){return a.replace("%TLDS%",u.src_tlds)}u.email_fuzzy=RegExp(n(u.tpl_email_fuzzy),"i"),u.link_fuzzy=RegExp(n(u.tpl_link_fuzzy),"i"),u.link_no_ip_fuzzy=RegExp(n(u.tpl_link_no_ip_fuzzy),"i"),u.host_fuzzy_test=RegExp(n(u.tpl_host_fuzzy_test),"i");const r=[];e.__compiled__={};function i(a,s){throw new Error('(LinkifyIt) Invalid schema "'+a+'": '+s)}Object.keys(e.__schemas__).forEach(function(a){const s=e.__schemas__[a];if(s===null)return;const c={validate:null,link:null};if(e.__compiled__[a]=c,W0(s)){G0(s.validate)?c.validate=K0(s.validate):Cn(s.validate)?c.validate=s.validate:i(a,s),Cn(s.normalize)?c.normalize=s.normalize:s.normalize?i(a,s):c.normalize=En();return}if(U0(s)){r.push(a);return}i(a,s)}),r.forEach(function(a){e.__compiled__[e.__schemas__[a]]&&(e.__compiled__[a].validate=e.__compiled__[e.__schemas__[a]].validate,e.__compiled__[a].normalize=e.__compiled__[e.__schemas__[a]].normalize)}),e.__compiled__[""]={validate:null,normalize:En()};const o=Object.keys(e.__compiled__).filter(function(a){return a.length>0&&e.__compiled__[a]}).map(V0).join("|");e.re.schema_test=RegExp("(^|(?!_)(?:[><｜]|"+u.src_ZPCc+"))("+o+")","i"),e.re.schema_search=RegExp("(^|(?!_)(?:[><｜]|"+u.src_ZPCc+"))("+o+")","ig"),e.re.schema_at_start=RegExp("^"+e.re.schema_search.source,"i"),e.re.pretest=RegExp("("+e.re.schema_test.source+")|("+e.re.host_fuzzy_test.source+")|@","i"),J0(e)}function ea(e,u){const t=e.__index__,n=e.__last_index__,r=e.__text_cache__.slice(t,n);this.schema=e.__schema__.toLowerCase(),this.index=t+u,this.lastIndex=n+u,this.raw=r,this.text=r,this.url=r}function vt(e,u){const t=new ea(e,u);return e.__compiled__[t.schema].normalize(t,e),t}function be(e,u){if(!(this instanceof be))return new be(e,u);u||Z0(e)&&(u=e,e={}),this.__opts__=yt({},rr,u),this.__index__=-1,this.__last_index__=-1,this.__schema__="",this.__text_cache__="",this.__schemas__=yt({},X0,e),this.__compiled__={},this.__tlds__=Q0,this.__tlds_replaced__=!1,this.re={},ju(this)}be.prototype.add=function(u,t){return this.__schemas__[u]=t,ju(this),this};be.prototype.set=function(u){return this.__opts__=yt(this.__opts__,u),this};be.prototype.test=function(u){if(this.__text_cache__=u,this.__index__=-1,!u.length)return!1;let t,n,r,i,o,a,s,c,l;if(this.re.schema_test.test(u)){for(s=this.re.schema_search,s.lastIndex=0;(t=s.exec(u))!==null;)if(i=this.testSchemaAt(u,t[2],s.lastIndex),i){this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+i;break}}return this.__opts__.fuzzyLink&&this.__compiled__["http:"]&&(c=u.search(this.re.host_fuzzy_test),c>=0&&(this.__index__<0||c<this.__index__)&&(n=u.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))!==null&&(o=n.index+n[1].length,(this.__index__<0||o<this.__index__)&&(this.__schema__="",this.__index__=o,this.__last_index__=n.index+n[0].length))),this.__opts__.fuzzyEmail&&this.__compiled__["mailto:"]&&(l=u.indexOf("@"),l>=0&&(r=u.match(this.re.email_fuzzy))!==null&&(o=r.index+r[1].length,a=r.index+r[0].length,(this.__index__<0||o<this.__index__||o===this.__index__&&a>this.__last_index__)&&(this.__schema__="mailto:",this.__index__=o,this.__last_index__=a))),this.__index__>=0};be.prototype.pretest=function(u){return this.re.pretest.test(u)};be.prototype.testSchemaAt=function(u,t,n){return this.__compiled__[t.toLowerCase()]?this.__compiled__[t.toLowerCase()].validate(u,n,this):0};be.prototype.match=function(u){const t=[];let n=0;this.__index__>=0&&this.__text_cache__===u&&(t.push(vt(this,n)),n=this.__last_index__);let r=n?u.slice(n):u;for(;this.test(r);)t.push(vt(this,n)),r=r.slice(this.__last_index__),n+=this.__last_index__;return t.length?t:null};be.prototype.matchAtStart=function(u){if(this.__text_cache__=u,this.__index__=-1,!u.length)return null;const t=this.re.schema_at_start.exec(u);if(!t)return null;const n=this.testSchemaAt(u,t[2],t[0].length);return n?(this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+n,vt(this,0)):null};be.prototype.tlds=function(u,t){return u=Array.isArray(u)?u:[u],t?(this.__tlds__=this.__tlds__.concat(u).sort().filter(function(n,r,i){return n!==i[r-1]}).reverse(),ju(this),this):(this.__tlds__=u.slice(),this.__tlds_replaced__=!0,ju(this),this)};be.prototype.normalize=function(u){u.schema||(u.url="http://"+u.url),u.schema==="mailto:"&&!/^mailto:/i.test(u.url)&&(u.url="mailto:"+u.url)};be.prototype.onCompile=function(){};const uu=2147483647,Se=36,qt=1,_u=26,ua=38,ta=700,ir=72,or=128,ar="-",na=/^xn--/,ra=/[^\0-\x7F]/,ia=/[\x2E\u3002\uFF0E\uFF61]/g,oa={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},dt=Se-qt,Te=Math.floor,ht=String.fromCharCode;function Be(e){throw new RangeError(oa[e])}function aa(e,u){const t=[];let n=e.length;for(;n--;)t[n]=u(e[n]);return t}function sr(e,u){const t=e.split("@");let n="";t.length>1&&(n=t[0]+"@",e=t[1]),e=e.replace(ia,".");const r=e.split("."),i=aa(r,u).join(".");return n+i}function cr(e){const u=[];let t=0;const n=e.length;for(;t<n;){const r=e.charCodeAt(t++);if(r>=55296&&r<=56319&&t<n){const i=e.charCodeAt(t++);(i&64512)==56320?u.push(((r&1023)<<10)+(i&1023)+65536):(u.push(r),t--)}else u.push(r)}return u}const sa=e=>String.fromCodePoint(...e),ca=function(e){return e>=48&&e<58?26+(e-48):e>=65&&e<91?e-65:e>=97&&e<123?e-97:Se},An=function(e,u){return e+22+75*(e<26)-((u!=0)<<5)},lr=function(e,u,t){let n=0;for(e=t?Te(e/ta):e>>1,e+=Te(e/u);e>dt*_u>>1;n+=Se)e=Te(e/dt);return Te(n+(dt+1)*e/(e+ua))},fr=function(e){const u=[],t=e.length;let n=0,r=or,i=ir,o=e.lastIndexOf(ar);o<0&&(o=0);for(let a=0;a<o;++a)e.charCodeAt(a)>=128&&Be("not-basic"),u.push(e.charCodeAt(a));for(let a=o>0?o+1:0;a<t;){const s=n;for(let l=1,f=Se;;f+=Se){a>=t&&Be("invalid-input");const p=ca(e.charCodeAt(a++));p>=Se&&Be("invalid-input"),p>Te((uu-n)/l)&&Be("overflow"),n+=p*l;const b=f<=i?qt:f>=i+_u?_u:f-i;if(p<b)break;const h=Se-b;l>Te(uu/h)&&Be("overflow"),l*=h}const c=u.length+1;i=lr(n-s,c,s==0),Te(n/c)>uu-r&&Be("overflow"),r+=Te(n/c),n%=c,u.splice(n++,0,r)}return String.fromCodePoint(...u)},dr=function(e){const u=[];e=cr(e);const t=e.length;let n=or,r=0,i=ir;for(const s of e)s<128&&u.push(ht(s));const o=u.length;let a=o;for(o&&u.push(ar);a<t;){let s=uu;for(const l of e)l>=n&&l<s&&(s=l);const c=a+1;s-n>Te((uu-r)/c)&&Be("overflow"),r+=(s-n)*c,n=s;for(const l of e)if(l<n&&++r>uu&&Be("overflow"),l===n){let f=r;for(let p=Se;;p+=Se){const b=p<=i?qt:p>=i+_u?_u:p-i;if(f<b)break;const h=f-b,m=Se-b;u.push(ht(An(b+h%m,0))),f=Te(h/m)}u.push(ht(An(f,0))),i=lr(r,c,a===o),r=0,++a}++r,++n}return u.join("")},la=function(e){return sr(e,function(u){return na.test(u)?fr(u.slice(4).toLowerCase()):u})},fa=function(e){return sr(e,function(u){return ra.test(u)?"xn--"+dr(u):u})},hr={version:"2.3.1",ucs2:{decode:cr,encode:sa},decode:fr,encode:dr,toASCII:fa,toUnicode:la},da={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}},ha={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["paragraph"]},inline:{rules:["text"],rules2:["balance_pairs","fragments_join"]}}},pa={options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["blockquote","code","fence","heading","hr","html_block","lheading","list","reference","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","html_inline","image","link","newline","text"],rules2:["balance_pairs","emphasis","fragments_join"]}}},ba={default:da,zero:ha,commonmark:pa},ma=/^(vbscript|javascript|file|data):/,ga=/^data:image\/(gif|png|jpeg|webp);/;function xa(e){const u=e.trim().toLowerCase();return ma.test(u)?ga.test(u):!0}const pr=["http:","https:","mailto:"];function _a(e){const u=$t(e,!0);if(u.hostname&&(!u.protocol||pr.indexOf(u.protocol)>=0))try{u.hostname=hr.toASCII(u.hostname)}catch{}return vu(Rt(u))}function ka(e){const u=$t(e,!0);if(u.hostname&&(!u.protocol||pr.indexOf(u.protocol)>=0))try{u.hostname=hr.toUnicode(u.hostname)}catch{}return ou(Rt(u),ou.defaultChars+"%")}function ge(e,u){if(!(this instanceof ge))return new ge(e,u);u||Mt(e)||(u=e||{},e="default"),this.inline=new Eu,this.block=new Xu,this.core=new Bt,this.renderer=new su,this.linkify=new be,this.validateLink=xa,this.normalizeLink=_a,this.normalizeLinkText=ka,this.utils=vo,this.helpers=Vu({},wo),this.options={},this.configure(e),u&&this.set(u)}ge.prototype.set=function(e){return Vu(this.options,e),this};ge.prototype.configure=function(e){const u=this;if(Mt(e)){const t=e;if(e=ba[t],!e)throw new Error('Wrong `markdown-it` preset "'+t+'", check name')}if(!e)throw new Error("Wrong `markdown-it` preset, can't be empty");return e.options&&u.set(e.options),e.components&&Object.keys(e.components).forEach(function(t){e.components[t].rules&&u[t].ruler.enableOnly(e.components[t].rules),e.components[t].rules2&&u[t].ruler2.enableOnly(e.components[t].rules2)}),this};ge.prototype.enable=function(e,u){let t=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach(function(r){t=t.concat(this[r].ruler.enable(e,!0))},this),t=t.concat(this.inline.ruler2.enable(e,!0));const n=e.filter(function(r){return t.indexOf(r)<0});if(n.length&&!u)throw new Error("MarkdownIt. Failed to enable unknown rule(s): "+n);return this};ge.prototype.disable=function(e,u){let t=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach(function(r){t=t.concat(this[r].ruler.disable(e,!0))},this),t=t.concat(this.inline.ruler2.disable(e,!0));const n=e.filter(function(r){return t.indexOf(r)<0});if(n.length&&!u)throw new Error("MarkdownIt. Failed to disable unknown rule(s): "+n);return this};ge.prototype.use=function(e){const u=[this].concat(Array.prototype.slice.call(arguments,1));return e.apply(e,u),this};ge.prototype.parse=function(e,u){if(typeof e!="string")throw new Error("Input data should be a String");const t=new this.core.State(e,this,u);return this.core.process(t),t.tokens};ge.prototype.render=function(e,u){return u=u||{},this.renderer.render(this.parse(e,u),this.options,u)};ge.prototype.parseInline=function(e,u){const t=new this.core.State(e,this,u);return t.inlineMode=!0,this.core.process(t),t.tokens};ge.prototype.renderInline=function(e,u){return u=u||{},this.renderer.render(this.parseInline(e,u),this.options,u)};const wn=new Set([!0,!1,"alt","title"]);function br(e,u){return(Array.isArray(e)?e:[]).filter(([t])=>t!==u)}function mr(e,u){e&&e.attrs&&(e.attrs=br(e.attrs,u))}function ya(e,u){if(!wn.has(e))throw new TypeError(`figcaption must be one of: ${[...wn]}.`);if(e==="alt")return u.content;const t=u.attrs.find(([n])=>n==="title");return Array.isArray(t)&&t[1]?(mr(u,"title"),t[1]):void 0}function va(e,u){u=u||{},e.core.ruler.before("linkify","image_figures",function(t){let n=1;for(let r=1,i=t.tokens.length;r<i-1;++r){const o=t.tokens[r];if(o.type!=="inline"||!o.children||o.children.length!==1&&o.children.length!==3||o.children.length===1&&o.children[0].type!=="image")continue;if(o.children.length===3){const[c,l,f]=o.children;if(c.type!=="link_open"||l.type!=="image"||f.type!=="link_close")continue}if(r!==0&&t.tokens[r-1].type!=="paragraph_open"||r!==i-1&&t.tokens[r+1].type!=="paragraph_close")continue;const a=t.tokens[r-1];let s;if(a.type="figure_open",a.tag="figure",t.tokens[r+1].type="figure_close",t.tokens[r+1].tag="figure",u.dataType&&t.tokens[r-1].attrPush(["data-type","image"]),u.link&&o.children.length===1){[s]=o.children;const c=new t.Token("link_open","a",1);c.attrPush(["href",s.attrGet("src")]),o.children.unshift(c),o.children.push(new t.Token("link_close","a",-1))}if(s=o.children.length===1?o.children[0]:o.children[1],u.figcaption){const c=ya(u.figcaption,s);if(c){const[l]=e.parseInline(c,t.env);o.children.push(new t.Token("figcaption_open","figcaption",1)),o.children.push(...l.children),o.children.push(new t.Token("figcaption_close","figcaption",-1)),s.attrs&&(s.attrs=br(s.attrs,"title"))}}if(u.copyAttrs&&s.attrs){const c=u.copyAttrs===!0?"":u.copyAttrs;a.attrs=s.attrs.filter(([l])=>l.match(c)).map(l=>Array.from(l))}if(u.tabindex&&(t.tokens[r-1].attrPush(["tabindex",n]),n++),u.lazy&&(s.attrs.some(([c])=>c==="loading")||s.attrs.push(["loading","lazy"])),u.async&&(s.attrs.some(([c])=>c==="decoding")||s.attrs.push(["decoding","async"])),u.classes&&typeof u.classes=="string"){let c=!1;for(let l=0,f=s.attrs.length;l<f&&!c;l++){const p=s.attrs[l];p[0]==="class"&&(p[1]=`${p[1]} ${u.classes}`,c=!0)}c||s.attrs.push(["class",u.classes])}if(u.removeSrc){const c=s.attrs.find(([l])=>l==="src");s.attrs.push(["data-src",c[1]]),mr(s,"src")}}})}const Ca=/\\([ \\!"#$%&'()*+,./:;<=>?@[\]^_`{|}~-])/g;function Ea(e,u){const t=e.posMax,n=e.pos;if(e.src.charCodeAt(n)!==126||u||n+2>=t)return!1;e.pos=n+1;let r=!1;for(;e.pos<t;){if(e.src.charCodeAt(e.pos)===126){r=!0;break}e.md.inline.skipToken(e)}if(!r||n+1===e.pos)return e.pos=n,!1;const i=e.src.slice(n+1,e.pos);if(i.match(/(^|[^\\])(\\\\)*\s/))return e.pos=n,!1;e.posMax=e.pos,e.pos=n+1;const o=e.push("sub_open","sub",1);o.markup="~";const a=e.push("text","",0);a.content=i.replace(Ca,"$1");const s=e.push("sub_close","sub",-1);return s.markup="~",e.pos=e.posMax+1,e.posMax=t,!0}function Aa(e){e.inline.ruler.after("emphasis","sub",Ea)}const wa=/\\([ \\!"#$%&'()*+,./:;<=>?@[\]^_`{|}~-])/g;function Da(e,u){const t=e.posMax,n=e.pos;if(e.src.charCodeAt(n)!==94||u||n+2>=t)return!1;e.pos=n+1;let r=!1;for(;e.pos<t;){if(e.src.charCodeAt(e.pos)===94){r=!0;break}e.md.inline.skipToken(e)}if(!r||n+1===e.pos)return e.pos=n,!1;const i=e.src.slice(n+1,e.pos);if(i.match(/(^|[^\\])(\\\\)*\s/))return e.pos=n,!1;e.posMax=e.pos,e.pos=n+1;const o=e.push("sup_open","sup",1);o.markup="^";const a=e.push("text","",0);a.content=i.replace(wa,"$1");const s=e.push("sup_close","sup",-1);return s.markup="^",e.pos=e.posMax+1,e.posMax=t,!0}function Fa(e){e.inline.ruler.after("emphasis","sup",Da)}const eu=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,gr=new Set,Ct=typeof process=="object"&&process?process:{},xr=(e,u,t,n)=>{typeof Ct.emitWarning=="function"?Ct.emitWarning(e,u,t,n):console.error(`[${t}] ${u}: ${e}`)};let Uu=globalThis.AbortController,Dn=globalThis.AbortSignal;var $n;if(typeof Uu>"u"){Dn=class{constructor(){W(this,"onabort");W(this,"_onabort",[]);W(this,"reason");W(this,"aborted",!1)}addEventListener(n,r){this._onabort.push(r)}},Uu=class{constructor(){W(this,"signal",new Dn);u()}abort(n){var r,i;if(!this.signal.aborted){this.signal.reason=n,this.signal.aborted=!0;for(const o of this.signal._onabort)o(n);(i=(r=this.signal).onabort)==null||i.call(r,n)}}};let e=(($n=Ct.env)==null?void 0:$n.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const u=()=>{e&&(e=!1,xr("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",u))}}const Sa=e=>!gr.has(e),Re=e=>e&&e===Math.floor(e)&&e>0&&isFinite(e),_r=e=>Re(e)?e<=Math.pow(2,8)?Uint8Array:e<=Math.pow(2,16)?Uint16Array:e<=Math.pow(2,32)?Uint32Array:e<=Number.MAX_SAFE_INTEGER?$u:null:null;class $u extends Array{constructor(u){super(u),this.fill(0)}}var tu;const Ve=class Ve{constructor(u,t){W(this,"heap");W(this,"length");if(!d(Ve,tu))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new t(u),this.length=0}static create(u){const t=_r(u);if(!t)return[];T(Ve,tu,!0);const n=new Ve(u,t);return T(Ve,tu,!1),n}push(u){this.heap[this.length++]=u}pop(){return this.heap[--this.length]}};tu=new WeakMap,P(Ve,tu,!1);let Et=Ve;var On,Mn,_e,ce,ke,ye,nu,ru,X,ve,Z,j,I,ie,le,ne,J,Ce,K,Ee,Ae,fe,we,je,oe,E,wt,Ze,Le,ku,de,kr,Xe,iu,yu,$e,Oe,Dt,Ou,Mu,H,Ft,du,Me,St;const Ut=class Ut{constructor(u){P(this,E);P(this,_e);P(this,ce);P(this,ke);P(this,ye);P(this,nu);P(this,ru);W(this,"ttl");W(this,"ttlResolution");W(this,"ttlAutopurge");W(this,"updateAgeOnGet");W(this,"updateAgeOnHas");W(this,"allowStale");W(this,"noDisposeOnSet");W(this,"noUpdateTTL");W(this,"maxEntrySize");W(this,"sizeCalculation");W(this,"noDeleteOnFetchRejection");W(this,"noDeleteOnStaleGet");W(this,"allowStaleOnFetchAbort");W(this,"allowStaleOnFetchRejection");W(this,"ignoreFetchAbort");P(this,X);P(this,ve);P(this,Z);P(this,j);P(this,I);P(this,ie);P(this,le);P(this,ne);P(this,J);P(this,Ce);P(this,K);P(this,Ee);P(this,Ae);P(this,fe);P(this,we);P(this,je);P(this,oe);P(this,Ze,()=>{});P(this,Le,()=>{});P(this,ku,()=>{});P(this,de,()=>!1);P(this,Xe,u=>{});P(this,iu,(u,t,n)=>{});P(this,yu,(u,t,n,r)=>{if(n||r)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});W(this,On,"LRUCache");const{max:t=0,ttl:n,ttlResolution:r=1,ttlAutopurge:i,updateAgeOnGet:o,updateAgeOnHas:a,allowStale:s,dispose:c,disposeAfter:l,noDisposeOnSet:f,noUpdateTTL:p,maxSize:b=0,maxEntrySize:h=0,sizeCalculation:m,fetchMethod:x,memoMethod:g,noDeleteOnFetchRejection:_,noDeleteOnStaleGet:y,allowStaleOnFetchRejection:v,allowStaleOnFetchAbort:C,ignoreFetchAbort:A}=u;if(t!==0&&!Re(t))throw new TypeError("max option must be a nonnegative integer");const D=t?_r(t):Array;if(!D)throw new Error("invalid max value: "+t);if(T(this,_e,t),T(this,ce,b),this.maxEntrySize=h||d(this,ce),this.sizeCalculation=m,this.sizeCalculation){if(!d(this,ce)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(g!==void 0&&typeof g!="function")throw new TypeError("memoMethod must be a function if defined");if(T(this,ru,g),x!==void 0&&typeof x!="function")throw new TypeError("fetchMethod must be a function if specified");if(T(this,nu,x),T(this,je,!!x),T(this,Z,new Map),T(this,j,new Array(t).fill(void 0)),T(this,I,new Array(t).fill(void 0)),T(this,ie,new D(t)),T(this,le,new D(t)),T(this,ne,0),T(this,J,0),T(this,Ce,Et.create(t)),T(this,X,0),T(this,ve,0),typeof c=="function"&&T(this,ke,c),typeof l=="function"?(T(this,ye,l),T(this,K,[])):(T(this,ye,void 0),T(this,K,void 0)),T(this,we,!!d(this,ke)),T(this,oe,!!d(this,ye)),this.noDisposeOnSet=!!f,this.noUpdateTTL=!!p,this.noDeleteOnFetchRejection=!!_,this.allowStaleOnFetchRejection=!!v,this.allowStaleOnFetchAbort=!!C,this.ignoreFetchAbort=!!A,this.maxEntrySize!==0){if(d(this,ce)!==0&&!Re(d(this,ce)))throw new TypeError("maxSize must be a positive integer if specified");if(!Re(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");w(this,E,kr).call(this)}if(this.allowStale=!!s,this.noDeleteOnStaleGet=!!y,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!a,this.ttlResolution=Re(r)||r===0?r:1,this.ttlAutopurge=!!i,this.ttl=n||0,this.ttl){if(!Re(this.ttl))throw new TypeError("ttl must be a positive integer if specified");w(this,E,wt).call(this)}if(d(this,_e)===0&&this.ttl===0&&d(this,ce)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!d(this,_e)&&!d(this,ce)){const k="LRU_CACHE_UNBOUNDED";Sa(k)&&(gr.add(k),xr("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",k,Ut))}}static unsafeExposeInternals(u){return{starts:d(u,Ae),ttls:d(u,fe),sizes:d(u,Ee),keyMap:d(u,Z),keyList:d(u,j),valList:d(u,I),next:d(u,ie),prev:d(u,le),get head(){return d(u,ne)},get tail(){return d(u,J)},free:d(u,Ce),isBackgroundFetch:t=>{var n;return w(n=u,E,H).call(n,t)},backgroundFetch:(t,n,r,i)=>{var o;return w(o=u,E,Mu).call(o,t,n,r,i)},moveToTail:t=>{var n;return w(n=u,E,du).call(n,t)},indexes:t=>{var n;return w(n=u,E,$e).call(n,t)},rindexes:t=>{var n;return w(n=u,E,Oe).call(n,t)},isStale:t=>{var n;return d(n=u,de).call(n,t)}}}get max(){return d(this,_e)}get maxSize(){return d(this,ce)}get calculatedSize(){return d(this,ve)}get size(){return d(this,X)}get fetchMethod(){return d(this,nu)}get memoMethod(){return d(this,ru)}get dispose(){return d(this,ke)}get disposeAfter(){return d(this,ye)}getRemainingTTL(u){return d(this,Z).has(u)?1/0:0}*entries(){for(const u of w(this,E,$e).call(this))d(this,I)[u]!==void 0&&d(this,j)[u]!==void 0&&!w(this,E,H).call(this,d(this,I)[u])&&(yield[d(this,j)[u],d(this,I)[u]])}*rentries(){for(const u of w(this,E,Oe).call(this))d(this,I)[u]!==void 0&&d(this,j)[u]!==void 0&&!w(this,E,H).call(this,d(this,I)[u])&&(yield[d(this,j)[u],d(this,I)[u]])}*keys(){for(const u of w(this,E,$e).call(this)){const t=d(this,j)[u];t!==void 0&&!w(this,E,H).call(this,d(this,I)[u])&&(yield t)}}*rkeys(){for(const u of w(this,E,Oe).call(this)){const t=d(this,j)[u];t!==void 0&&!w(this,E,H).call(this,d(this,I)[u])&&(yield t)}}*values(){for(const u of w(this,E,$e).call(this))d(this,I)[u]!==void 0&&!w(this,E,H).call(this,d(this,I)[u])&&(yield d(this,I)[u])}*rvalues(){for(const u of w(this,E,Oe).call(this))d(this,I)[u]!==void 0&&!w(this,E,H).call(this,d(this,I)[u])&&(yield d(this,I)[u])}[(Mn=Symbol.iterator,On=Symbol.toStringTag,Mn)](){return this.entries()}find(u,t={}){for(const n of w(this,E,$e).call(this)){const r=d(this,I)[n],i=w(this,E,H).call(this,r)?r.__staleWhileFetching:r;if(i!==void 0&&u(i,d(this,j)[n],this))return this.get(d(this,j)[n],t)}}forEach(u,t=this){for(const n of w(this,E,$e).call(this)){const r=d(this,I)[n],i=w(this,E,H).call(this,r)?r.__staleWhileFetching:r;i!==void 0&&u.call(t,i,d(this,j)[n],this)}}rforEach(u,t=this){for(const n of w(this,E,Oe).call(this)){const r=d(this,I)[n],i=w(this,E,H).call(this,r)?r.__staleWhileFetching:r;i!==void 0&&u.call(t,i,d(this,j)[n],this)}}purgeStale(){let u=!1;for(const t of w(this,E,Oe).call(this,{allowStale:!0}))d(this,de).call(this,t)&&(w(this,E,Me).call(this,d(this,j)[t],"expire"),u=!0);return u}info(u){const t=d(this,Z).get(u);if(t===void 0)return;const n=d(this,I)[t],r=w(this,E,H).call(this,n)?n.__staleWhileFetching:n;if(r===void 0)return;const i={value:r};if(d(this,fe)&&d(this,Ae)){const o=d(this,fe)[t],a=d(this,Ae)[t];if(o&&a){const s=o-(eu.now()-a);i.ttl=s,i.start=Date.now()}}return d(this,Ee)&&(i.size=d(this,Ee)[t]),i}dump(){const u=[];for(const t of w(this,E,$e).call(this,{allowStale:!0})){const n=d(this,j)[t],r=d(this,I)[t],i=w(this,E,H).call(this,r)?r.__staleWhileFetching:r;if(i===void 0||n===void 0)continue;const o={value:i};if(d(this,fe)&&d(this,Ae)){o.ttl=d(this,fe)[t];const a=eu.now()-d(this,Ae)[t];o.start=Math.floor(Date.now()-a)}d(this,Ee)&&(o.size=d(this,Ee)[t]),u.unshift([n,o])}return u}load(u){this.clear();for(const[t,n]of u){if(n.start){const r=Date.now()-n.start;n.start=eu.now()-r}this.set(t,n.value,n)}}set(u,t,n={}){var p,b,h,m,x;if(t===void 0)return this.delete(u),this;const{ttl:r=this.ttl,start:i,noDisposeOnSet:o=this.noDisposeOnSet,sizeCalculation:a=this.sizeCalculation,status:s}=n;let{noUpdateTTL:c=this.noUpdateTTL}=n;const l=d(this,yu).call(this,u,t,n.size||0,a);if(this.maxEntrySize&&l>this.maxEntrySize)return s&&(s.set="miss",s.maxEntrySizeExceeded=!0),w(this,E,Me).call(this,u,"set"),this;let f=d(this,X)===0?void 0:d(this,Z).get(u);if(f===void 0)f=d(this,X)===0?d(this,J):d(this,Ce).length!==0?d(this,Ce).pop():d(this,X)===d(this,_e)?w(this,E,Ou).call(this,!1):d(this,X),d(this,j)[f]=u,d(this,I)[f]=t,d(this,Z).set(u,f),d(this,ie)[d(this,J)]=f,d(this,le)[f]=d(this,J),T(this,J,f),wu(this,X)._++,d(this,iu).call(this,f,l,s),s&&(s.set="add"),c=!1;else{w(this,E,du).call(this,f);const g=d(this,I)[f];if(t!==g){if(d(this,je)&&w(this,E,H).call(this,g)){g.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:_}=g;_!==void 0&&!o&&(d(this,we)&&((p=d(this,ke))==null||p.call(this,_,u,"set")),d(this,oe)&&((b=d(this,K))==null||b.push([_,u,"set"])))}else o||(d(this,we)&&((h=d(this,ke))==null||h.call(this,g,u,"set")),d(this,oe)&&((m=d(this,K))==null||m.push([g,u,"set"])));if(d(this,Xe).call(this,f),d(this,iu).call(this,f,l,s),d(this,I)[f]=t,s){s.set="replace";const _=g&&w(this,E,H).call(this,g)?g.__staleWhileFetching:g;_!==void 0&&(s.oldValue=_)}}else s&&(s.set="update")}if(r!==0&&!d(this,fe)&&w(this,E,wt).call(this),d(this,fe)&&(c||d(this,ku).call(this,f,r,i),s&&d(this,Le).call(this,s,f)),!o&&d(this,oe)&&d(this,K)){const g=d(this,K);let _;for(;_=g==null?void 0:g.shift();)(x=d(this,ye))==null||x.call(this,..._)}return this}pop(){var u;try{for(;d(this,X);){const t=d(this,I)[d(this,ne)];if(w(this,E,Ou).call(this,!0),w(this,E,H).call(this,t)){if(t.__staleWhileFetching)return t.__staleWhileFetching}else if(t!==void 0)return t}}finally{if(d(this,oe)&&d(this,K)){const t=d(this,K);let n;for(;n=t==null?void 0:t.shift();)(u=d(this,ye))==null||u.call(this,...n)}}}has(u,t={}){const{updateAgeOnHas:n=this.updateAgeOnHas,status:r}=t,i=d(this,Z).get(u);if(i!==void 0){const o=d(this,I)[i];if(w(this,E,H).call(this,o)&&o.__staleWhileFetching===void 0)return!1;if(d(this,de).call(this,i))r&&(r.has="stale",d(this,Le).call(this,r,i));else return n&&d(this,Ze).call(this,i),r&&(r.has="hit",d(this,Le).call(this,r,i)),!0}else r&&(r.has="miss");return!1}peek(u,t={}){const{allowStale:n=this.allowStale}=t,r=d(this,Z).get(u);if(r===void 0||!n&&d(this,de).call(this,r))return;const i=d(this,I)[r];return w(this,E,H).call(this,i)?i.__staleWhileFetching:i}async fetch(u,t={}){const{allowStale:n=this.allowStale,updateAgeOnGet:r=this.updateAgeOnGet,noDeleteOnStaleGet:i=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:a=this.noDisposeOnSet,size:s=0,sizeCalculation:c=this.sizeCalculation,noUpdateTTL:l=this.noUpdateTTL,noDeleteOnFetchRejection:f=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:p=this.allowStaleOnFetchRejection,ignoreFetchAbort:b=this.ignoreFetchAbort,allowStaleOnFetchAbort:h=this.allowStaleOnFetchAbort,context:m,forceRefresh:x=!1,status:g,signal:_}=t;if(!d(this,je))return g&&(g.fetch="get"),this.get(u,{allowStale:n,updateAgeOnGet:r,noDeleteOnStaleGet:i,status:g});const y={allowStale:n,updateAgeOnGet:r,noDeleteOnStaleGet:i,ttl:o,noDisposeOnSet:a,size:s,sizeCalculation:c,noUpdateTTL:l,noDeleteOnFetchRejection:f,allowStaleOnFetchRejection:p,allowStaleOnFetchAbort:h,ignoreFetchAbort:b,status:g,signal:_};let v=d(this,Z).get(u);if(v===void 0){g&&(g.fetch="miss");const C=w(this,E,Mu).call(this,u,v,y,m);return C.__returned=C}else{const C=d(this,I)[v];if(w(this,E,H).call(this,C)){const L=n&&C.__staleWhileFetching!==void 0;return g&&(g.fetch="inflight",L&&(g.returnedStale=!0)),L?C.__staleWhileFetching:C.__returned=C}const A=d(this,de).call(this,v);if(!x&&!A)return g&&(g.fetch="hit"),w(this,E,du).call(this,v),r&&d(this,Ze).call(this,v),g&&d(this,Le).call(this,g,v),C;const D=w(this,E,Mu).call(this,u,v,y,m),$=D.__staleWhileFetching!==void 0&&n;return g&&(g.fetch=A?"stale":"refresh",$&&A&&(g.returnedStale=!0)),$?D.__staleWhileFetching:D.__returned=D}}async forceFetch(u,t={}){const n=await this.fetch(u,t);if(n===void 0)throw new Error("fetch() returned undefined");return n}memo(u,t={}){const n=d(this,ru);if(!n)throw new Error("no memoMethod provided to constructor");const{context:r,forceRefresh:i,...o}=t,a=this.get(u,o);if(!i&&a!==void 0)return a;const s=n(u,a,{options:o,context:r});return this.set(u,s,o),s}get(u,t={}){const{allowStale:n=this.allowStale,updateAgeOnGet:r=this.updateAgeOnGet,noDeleteOnStaleGet:i=this.noDeleteOnStaleGet,status:o}=t,a=d(this,Z).get(u);if(a!==void 0){const s=d(this,I)[a],c=w(this,E,H).call(this,s);return o&&d(this,Le).call(this,o,a),d(this,de).call(this,a)?(o&&(o.get="stale"),c?(o&&n&&s.__staleWhileFetching!==void 0&&(o.returnedStale=!0),n?s.__staleWhileFetching:void 0):(i||w(this,E,Me).call(this,u,"expire"),o&&n&&(o.returnedStale=!0),n?s:void 0)):(o&&(o.get="hit"),c?s.__staleWhileFetching:(w(this,E,du).call(this,a),r&&d(this,Ze).call(this,a),s))}else o&&(o.get="miss")}delete(u){return w(this,E,Me).call(this,u,"delete")}clear(){return w(this,E,St).call(this,"delete")}};_e=new WeakMap,ce=new WeakMap,ke=new WeakMap,ye=new WeakMap,nu=new WeakMap,ru=new WeakMap,X=new WeakMap,ve=new WeakMap,Z=new WeakMap,j=new WeakMap,I=new WeakMap,ie=new WeakMap,le=new WeakMap,ne=new WeakMap,J=new WeakMap,Ce=new WeakMap,K=new WeakMap,Ee=new WeakMap,Ae=new WeakMap,fe=new WeakMap,we=new WeakMap,je=new WeakMap,oe=new WeakMap,E=new WeakSet,wt=function(){const u=new $u(d(this,_e)),t=new $u(d(this,_e));T(this,fe,u),T(this,Ae,t),T(this,ku,(i,o,a=eu.now())=>{if(t[i]=o!==0?a:0,u[i]=o,o!==0&&this.ttlAutopurge){const s=setTimeout(()=>{d(this,de).call(this,i)&&w(this,E,Me).call(this,d(this,j)[i],"expire")},o+1);s.unref&&s.unref()}}),T(this,Ze,i=>{t[i]=u[i]!==0?eu.now():0}),T(this,Le,(i,o)=>{if(u[o]){const a=u[o],s=t[o];if(!a||!s)return;i.ttl=a,i.start=s,i.now=n||r();const c=i.now-s;i.remainingTTL=a-c}});let n=0;const r=()=>{const i=eu.now();if(this.ttlResolution>0){n=i;const o=setTimeout(()=>n=0,this.ttlResolution);o.unref&&o.unref()}return i};this.getRemainingTTL=i=>{const o=d(this,Z).get(i);if(o===void 0)return 0;const a=u[o],s=t[o];if(!a||!s)return 1/0;const c=(n||r())-s;return a-c},T(this,de,i=>{const o=t[i],a=u[i];return!!a&&!!o&&(n||r())-o>a})},Ze=new WeakMap,Le=new WeakMap,ku=new WeakMap,de=new WeakMap,kr=function(){const u=new $u(d(this,_e));T(this,ve,0),T(this,Ee,u),T(this,Xe,t=>{T(this,ve,d(this,ve)-u[t]),u[t]=0}),T(this,yu,(t,n,r,i)=>{if(w(this,E,H).call(this,n))return 0;if(!Re(r))if(i){if(typeof i!="function")throw new TypeError("sizeCalculation must be a function");if(r=i(n,t),!Re(r))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}else throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");return r}),T(this,iu,(t,n,r)=>{if(u[t]=n,d(this,ce)){const i=d(this,ce)-u[t];for(;d(this,ve)>i;)w(this,E,Ou).call(this,!0)}T(this,ve,d(this,ve)+u[t]),r&&(r.entrySize=n,r.totalCalculatedSize=d(this,ve))})},Xe=new WeakMap,iu=new WeakMap,yu=new WeakMap,$e=function*({allowStale:u=this.allowStale}={}){if(d(this,X))for(let t=d(this,J);!(!w(this,E,Dt).call(this,t)||((u||!d(this,de).call(this,t))&&(yield t),t===d(this,ne)));)t=d(this,le)[t]},Oe=function*({allowStale:u=this.allowStale}={}){if(d(this,X))for(let t=d(this,ne);!(!w(this,E,Dt).call(this,t)||((u||!d(this,de).call(this,t))&&(yield t),t===d(this,J)));)t=d(this,ie)[t]},Dt=function(u){return u!==void 0&&d(this,Z).get(d(this,j)[u])===u},Ou=function(u){var i,o;const t=d(this,ne),n=d(this,j)[t],r=d(this,I)[t];return d(this,je)&&w(this,E,H).call(this,r)?r.__abortController.abort(new Error("evicted")):(d(this,we)||d(this,oe))&&(d(this,we)&&((i=d(this,ke))==null||i.call(this,r,n,"evict")),d(this,oe)&&((o=d(this,K))==null||o.push([r,n,"evict"]))),d(this,Xe).call(this,t),u&&(d(this,j)[t]=void 0,d(this,I)[t]=void 0,d(this,Ce).push(t)),d(this,X)===1?(T(this,ne,T(this,J,0)),d(this,Ce).length=0):T(this,ne,d(this,ie)[t]),d(this,Z).delete(n),wu(this,X)._--,t},Mu=function(u,t,n,r){const i=t===void 0?void 0:d(this,I)[t];if(w(this,E,H).call(this,i))return i;const o=new Uu,{signal:a}=n;a==null||a.addEventListener("abort",()=>o.abort(a.reason),{signal:o.signal});const s={signal:o.signal,options:n,context:r},c=(m,x=!1)=>{const{aborted:g}=o.signal,_=n.ignoreFetchAbort&&m!==void 0;if(n.status&&(g&&!x?(n.status.fetchAborted=!0,n.status.fetchError=o.signal.reason,_&&(n.status.fetchAbortIgnored=!0)):n.status.fetchResolved=!0),g&&!_&&!x)return f(o.signal.reason);const y=b;return d(this,I)[t]===b&&(m===void 0?y.__staleWhileFetching?d(this,I)[t]=y.__staleWhileFetching:w(this,E,Me).call(this,u,"fetch"):(n.status&&(n.status.fetchUpdated=!0),this.set(u,m,s.options))),m},l=m=>(n.status&&(n.status.fetchRejected=!0,n.status.fetchError=m),f(m)),f=m=>{const{aborted:x}=o.signal,g=x&&n.allowStaleOnFetchAbort,_=g||n.allowStaleOnFetchRejection,y=_||n.noDeleteOnFetchRejection,v=b;if(d(this,I)[t]===b&&(!y||v.__staleWhileFetching===void 0?w(this,E,Me).call(this,u,"fetch"):g||(d(this,I)[t]=v.__staleWhileFetching)),_)return n.status&&v.__staleWhileFetching!==void 0&&(n.status.returnedStale=!0),v.__staleWhileFetching;if(v.__returned===v)throw m},p=(m,x)=>{var _;const g=(_=d(this,nu))==null?void 0:_.call(this,u,i,s);g&&g instanceof Promise&&g.then(y=>m(y===void 0?void 0:y),x),o.signal.addEventListener("abort",()=>{(!n.ignoreFetchAbort||n.allowStaleOnFetchAbort)&&(m(void 0),n.allowStaleOnFetchAbort&&(m=y=>c(y,!0)))})};n.status&&(n.status.fetchDispatched=!0);const b=new Promise(p).then(c,l),h=Object.assign(b,{__abortController:o,__staleWhileFetching:i,__returned:void 0});return t===void 0?(this.set(u,h,{...s.options,status:void 0}),t=d(this,Z).get(u)):d(this,I)[t]=h,h},H=function(u){if(!d(this,je))return!1;const t=u;return!!t&&t instanceof Promise&&t.hasOwnProperty("__staleWhileFetching")&&t.__abortController instanceof Uu},Ft=function(u,t){d(this,le)[t]=u,d(this,ie)[u]=t},du=function(u){u!==d(this,J)&&(u===d(this,ne)?T(this,ne,d(this,ie)[u]):w(this,E,Ft).call(this,d(this,le)[u],d(this,ie)[u]),w(this,E,Ft).call(this,d(this,J),u),T(this,J,u))},Me=function(u,t){var r,i,o,a;let n=!1;if(d(this,X)!==0){const s=d(this,Z).get(u);if(s!==void 0)if(n=!0,d(this,X)===1)w(this,E,St).call(this,t);else{d(this,Xe).call(this,s);const c=d(this,I)[s];if(w(this,E,H).call(this,c)?c.__abortController.abort(new Error("deleted")):(d(this,we)||d(this,oe))&&(d(this,we)&&((r=d(this,ke))==null||r.call(this,c,u,t)),d(this,oe)&&((i=d(this,K))==null||i.push([c,u,t]))),d(this,Z).delete(u),d(this,j)[s]=void 0,d(this,I)[s]=void 0,s===d(this,J))T(this,J,d(this,le)[s]);else if(s===d(this,ne))T(this,ne,d(this,ie)[s]);else{const l=d(this,le)[s];d(this,ie)[l]=d(this,ie)[s];const f=d(this,ie)[s];d(this,le)[f]=d(this,le)[s]}wu(this,X)._--,d(this,Ce).push(s)}}if(d(this,oe)&&((o=d(this,K))!=null&&o.length)){const s=d(this,K);let c;for(;c=s==null?void 0:s.shift();)(a=d(this,ye))==null||a.call(this,...c)}return n},St=function(u){var t,n,r;for(const i of w(this,E,Oe).call(this,{allowStale:!0})){const o=d(this,I)[i];if(w(this,E,H).call(this,o))o.__abortController.abort(new Error("deleted"));else{const a=d(this,j)[i];d(this,we)&&((t=d(this,ke))==null||t.call(this,o,a,u)),d(this,oe)&&((n=d(this,K))==null||n.push([o,a,u]))}}if(d(this,Z).clear(),d(this,I).fill(void 0),d(this,j).fill(void 0),d(this,fe)&&d(this,Ae)&&(d(this,fe).fill(0),d(this,Ae).fill(0)),d(this,Ee)&&d(this,Ee).fill(0),T(this,ne,0),T(this,J,0),d(this,Ce).length=0,T(this,ve,0),T(this,X,0),d(this,oe)&&d(this,K)){const i=d(this,K);let o;for(;o=i==null?void 0:i.shift();)(r=d(this,ye))==null||r.call(this,...o)}};let At=Ut;function Ta(e,u){for(var t=0;t<u.length;t++){const n=u[t];if(typeof n!="string"&&!Array.isArray(n)){for(const r in n)if(r!=="default"&&!(r in e)){const i=Object.getOwnPropertyDescriptor(n,r);i&&Object.defineProperty(e,r,i.get?i:{enumerable:!0,get:()=>n[r]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}const Ia=(e,u)=>{const t=Y("editorId"),{noImgZoomIn:n}=e,r=Nn(()=>{const i=document.querySelectorAll(`#${t}-preview img:not(.not-zoom):not(.medium-zoom-image)`);i.length!==0&&Fi(i,{background:"#00000073"})});pe(()=>{!n&&e.setting.preview&&r()}),ee([u,Bn(e.setting,"preview")],()=>{!n&&e.setting.preview&&r()})},za=(e,u,t)=>{const n=Y("editorId"),r=Y("rootRef"),i=Y("usedLanguageText"),o=()=>{r.value.querySelectorAll(`#${n} .${F}-preview .${F}-code`).forEach(c=>{let l=-1;const f=c.querySelector(`.${F}-copy-button`);f&&(f.onclick=p=>{p.preventDefault(),clearTimeout(l);const h=(c.querySelector("input:checked + pre code")||c.querySelector("pre code")).textContent,m=Ri(e.formatCopiedText(h)),{text:x,successTips:g,failTips:_}=i.value.copyCode,y=m?g:_;f.dataset.isIcon?f.dataset.tips=y:f.innerHTML=y,l=window.setTimeout(()=>{f.dataset.isIcon?f.dataset.tips=x:f.innerHTML=x},1500)})})},a=()=>{bu(o)},s=c=>{c&&bu(o)};ee([u,t],a),ee(()=>e.setting.preview,s),ee(()=>e.setting.htmlPreview,s),ee(()=>i.value,o),pe(o)},La=e=>{const u=Y("highlight"),t=Nu(ae.editorExtensions.highlight.instance);return pe(()=>{e.noHighlight||t.value||(he("link",{...u.value.css,rel:"stylesheet",id:`${F}-hlCss`}),he("script",{...u.value.js,id:`${F}-hljs`,onload(){t.value=window.hljs}},"hljs"))}),ee(()=>u.value.css,()=>{e.noHighlight||ae.editorExtensions.highlight.instance||yi("link",{...u.value.css,rel:"stylesheet",id:`${F}-hlCss`})}),t},Pu=new At({max:1e3,ttl:6e5}),Ra=e=>{const u=Y("theme"),t=Y("rootRef"),{editorExtensions:n,editorExtensionsAttrs:r,mermaidConfig:i}=ae,o=Nu(n.mermaid.instance),a=Nu(-1),s=()=>{const l=o.value;!e.noMermaid&&l&&(l.initialize(i({startOnLoad:!1,theme:u.value==="dark"?"dark":"default"})),a.value=a.value+1)};return ee(()=>u.value,()=>{Pu.clear(),s()}),pe(()=>{var l,f;if(e.noMermaid||o.value)return;const p=n.mermaid.js;/\.mjs/.test(p)?(he("link",{...(l=r.mermaid)==null?void 0:l.js,rel:"modulepreload",href:p,id:`${F}-mermaid-m`}),import(p).then(b=>{o.value=b.default,s()})):he("script",{...(f=r.mermaid)==null?void 0:f.js,src:p,id:`${F}-mermaid`,onload(){o.value=window.mermaid,s()}},"mermaid")}),{mermaidRef:o,reRenderRef:a,replaceMermaid:async()=>{if(!e.noMermaid&&o.value){const l=t.value.querySelectorAll(`div.${F}-mermaid`),f=document.createElement("div"),p=document.body.offsetWidth>1366?document.body.offsetWidth:1366,b=document.body.offsetHeight>768?document.body.offsetHeight:768;f.style.width=p+"px",f.style.height=b+"px",f.style.position="fixed",f.style.zIndex="-10000",f.style.top="-10000";let h=l.length;h>0&&document.body.appendChild(f),await Promise.allSettled(Array.from(l).map(m=>(async g=>{var _;let y=Pu.get(g.innerText);if(!y){const v=gt();let C={svg:""};try{C=await o.value.render(v,g.innerText,f),y=await e.sanitizeMermaid(C.svg);const A=document.createElement("p");A.className=`${F}-mermaid`,A.setAttribute("data-processed",""),A.innerHTML=y,(_=A.children[0])==null||_.removeAttribute("height"),Pu.set(g.innerText,A.innerHTML),g.dataset.line!==void 0&&(A.dataset.line=g.dataset.line),g.replaceWith(A)}catch{}--h===0&&f.remove()}})(m)))}}}},$a=e=>{const u=Nu(ae.editorExtensions.katex.instance);return pe(()=>{if(e.noKatex||u.value)return;const{editorExtensions:t}=ae;he("script",{src:t.katex.js,id:`${F}-katex`,onload(){u.value=window.katex}},"katex"),he("link",{rel:"stylesheet",href:t.katex.css,id:`${F}-katexCss`})}),u},Oa=(e,u)=>{const t=e.renderer.rules.fence.bind(e.renderer.rules);e.renderer.rules.fence=(n,r,i,o,a)=>{const s=n[r],c=s.content.trim();if(s.info==="mermaid"){let l;n[r].map&&n[r].level===0&&(l=n[r].map[0],n[r].attrSet("data-line",String(l)));const f=Pu.get(c);return f?`<p class="${F}-mermaid" ${l!==void 0?"data-line="+l:""} data-processed>${f}</p>`:`<div class="${F}-mermaid" ${l!==void 0?"data-line="+l:""} data-mermaid-theme=${u.themeRef.value}>${c}</div>`}return t(n,r,i,o,a)}},Wu=(e,u)=>{const t=e.attrs?e.attrs.slice():[];return u.forEach(n=>{const r=e.attrIndex(n[0]);r<0?t.push(n):(t[r]=t[r].slice(),t[r][1]+=` ${n[1]}`)}),t},Ma=(e,u)=>{const t=[{open:"$",close:"$"},{open:"\\(",close:"\\)"}];let n,r,i;for(const o of t)if(e.src.startsWith(o.open,e.pos)){const a=e.pos+o.open.length;for(n=a;(n=e.src.indexOf(o.close,n))!==-1;){for(i=n-1;e.src[i]==="\\";)i-=1;if((n-i)%2==1)break;n+=o.close.length}return n===-1?(u||(e.pending+=o.open),e.pos=a,!0):n-a===0?(u||(e.pending+=o.open+o.close),e.pos=a+o.close.length,!0):(u||(r=e.push("math_inline","math",0),r.markup=o.open,r.content=e.src.slice(a,n)),e.pos=n+o.close.length,!0)}return!1},Pa=(e,u,t,n)=>{const r=[{open:"$$",close:"$$"},{open:"\\[",close:"\\]"}];let i,o,a,s,c=!1,l=e.bMarks[u]+e.tShift[u],f=e.eMarks[u];for(const p of r){if(l+p.open.length>f||e.src.slice(l,l+p.open.length)!==p.open)continue;if(l+=p.open.length,i=e.src.slice(l,f),n)return!0;for(i.trim().slice(-p.close.length)===p.close&&(i=i.trim().slice(0,-p.close.length),c=!0),a=u;!c&&(a++,!(a>=t||(l=e.bMarks[a]+e.tShift[a],f=e.eMarks[a],l<f&&e.tShift[a]<e.blkIndent)));)e.src.slice(l,f).trim().slice(-p.close.length)===p.close&&(s=e.src.slice(0,f).lastIndexOf(p.close),o=e.src.slice(l,s),c=!0);e.line=a+1;const b=e.push("math_block","math",0);return b.block=!0,b.content=(i&&i.trim()?i+`
`:"")+e.getLines(u+1,a,e.tShift[u],!0)+(o&&o.trim()?o:""),b.map=[u,e.line],b.markup=p.open,!0}return!1},Ba=(e,{katexRef:u})=>{const t=(r,i,o,a,s)=>{const c=r[i],l={attrs:Wu(c,[["class",`${F}-katex-inline`]])};if(u.value){const f=u.value.renderToString(c.content,ae.katexConfig({throwOnError:!1}));return`<span ${s.renderAttrs(l)} data-processed>${f}</span>`}else return`<span ${s.renderAttrs(l)}>${c.content}</span>`},n=(r,i,o,a,s)=>{const c=r[i],l={attrs:Wu(c,[["class",`${F}-katex-block`]])};if(u.value){const f=u.value.renderToString(c.content,ae.katexConfig({throwOnError:!1,displayMode:!0}));return`<p ${s.renderAttrs(l)} data-processed>${f}</p>`}else return`<p ${s.renderAttrs(l)}>${c.content}</p>`};e.inline.ruler.before("escape","math_inline",Ma),e.block.ruler.after("blockquote","math_block",Pa,{alt:["paragraph","reference","blockquote","list"]}),e.renderer.rules.math_inline=t,e.renderer.rules.math_block=n},Na=(e,u)=>{u=u||{};const t=3,n=u.marker||"!",r=n.charCodeAt(0),i=n.length;let o="",a="";const s=(l,f,p,b,h)=>{const m=l[f];return m.type==="admonition_open"?l[f].attrPush(["class",`${F}-admonition ${F}-admonition-${m.info}`]):m.type==="admonition_title_open"&&l[f].attrPush(["class",`${F}-admonition-title`]),h.renderToken(l,f,p)},c=l=>{const f=l.trim().split(" ",2);a="",o=f[0],f.length>1&&(a=l.substring(o.length+2))};e.block.ruler.before("code","admonition",(l,f,p,b)=>{let h,m,x,g=!1,_=l.bMarks[f]+l.tShift[f],y=l.eMarks[f];if(r!==l.src.charCodeAt(_))return!1;for(h=_+1;h<=y&&n[(h-_)%i]===l.src[h];h++);const v=Math.floor((h-_)/i);if(v!==t)return!1;h-=(h-_)%i;const C=l.src.slice(_,h),A=l.src.slice(h,y);if(c(A),b)return!0;for(m=f;m++,!(m>=p||(_=l.bMarks[m]+l.tShift[m],y=l.eMarks[m],_<y&&l.sCount[m]<l.blkIndent));)if(r===l.src.charCodeAt(_)&&!(l.sCount[m]-l.blkIndent>=4)){for(h=_+1;h<=y&&n[(h-_)%i]===l.src[h];h++);if(!(Math.floor((h-_)/i)<v)&&(h-=(h-_)%i,h=l.skipSpaces(h),!(h<y))){g=!0;break}}const D=l.parentType,k=l.lineMax;return l.parentType="root",l.lineMax=m,x=l.push("admonition_open","div",1),x.markup=C,x.block=!0,x.info=o,x.map=[f,m],a&&(x=l.push("admonition_title_open","p",1),x.markup=C+" "+o,x.map=[f,m],x=l.push("inline","",0),x.content=a,x.map=[f,l.line-1],x.children=[],x=l.push("admonition_title_close","p",-1),x.markup=C+" "+o),l.md.block.tokenize(l,f+1,m),x=l.push("admonition_close","div",-1),x.markup=l.src.slice(_,h),x.block=!0,l.parentType=D,l.lineMax=k,l.line=m+(g?1:0),!0},{alt:["paragraph","reference","blockquote","list"]}),e.renderer.rules.admonition_open=s,e.renderer.rules.admonition_title_open=s,e.renderer.rules.admonition_title_close=s,e.renderer.rules.admonition_close=s},qa=(e,u)=>{e.renderer.rules.heading_open=(t,n)=>{var r;const i=t[n],o=((r=t[n+1].children)==null?void 0:r.reduce((s,c)=>s+(["text","code_inline","math_inline"].includes(c.type)&&c.content||""),""))||"",a=i.markup.length;return u.headsRef.value.push({text:o,level:a}),i.map&&i.level===0&&i.attrSet("id",u.mdHeadingId(o,a,u.headsRef.value.length)),e.renderer.renderToken(t,n,u)},e.renderer.rules.heading_close=(t,n,r,i,o)=>o.renderToken(t,n,r)},Ha=(e,u)=>{if(typeof u[e]=="string")return u[e];const t=`<i class="${F}-iconfont ${F}-icon-${e}"></i>`;switch(ae.iconfontType){case"svg":return`<svg class="${F}-icon" aria-hidden="true"><use xlink:href="#${F}-icon-${e}"></use></svg>`;default:return t}},ja=Ha,Ua=(e,u)=>{const t=e.renderer.rules.fence,n=e.utils.unescapeAll,r=/\[(\w*)(?::([\w ]*))?\]/,i=/::(open|close)/,o=f=>f.info?n(f.info).trim():"",a=f=>{const p=o(f),[b=null,h=""]=(r.exec(p)||[]).slice(1);return[b,h]},s=f=>{const p=o(f);return p?p.split(/(\s+)/g)[0]:""},c=f=>{const p=f.info.match(i)||[],b=p[1]==="open"||p[1]!=="close"&&u.codeFoldable&&f.content.trim().split(`
`).length<u.autoFoldThreshold,h=p[1]||u.codeFoldable?"details":"div",m=p[1]||u.codeFoldable?"summary":"div";return{open:b,tagContainer:h,tagHeader:m}},l=(f,p,b,h,m)=>{var x;if(f[p].hidden)return"";const g=(x=u.usedLanguageTextRef.value)==null?void 0:x.copyCode.text,_=u.customIconRef.value.copy||g,y=!!u.customIconRef.value.copy,v=`<span class="${F}-collapse-tips">${ja("collapse-tips",u.customIconRef.value)}</span>`,[C]=a(f[p]);if(C===null){const{open:V,tagContainer:re,tagHeader:xe}=c(f[p]),Qe=[["class",`${F}-code`]];V&&Qe.push(["open",""]);const Ju={attrs:Wu(f[p],Qe)};f[p].info=f[p].info.replace(i,"");const Ku=t(f,p,b,h,m);return`
        <${re} ${m.renderAttrs(Ju)}>
          <${xe} class="${F}-code-head">
            <div class="${F}-code-flag"><span></span><span></span><span></span></div>
            <div class="${F}-code-action">
              <span class="${F}-code-lang">${f[p].info.trim()}</span>
              <span class="${F}-copy-button" data-tips="${g}"${y?" data-is-icon=true":""}>${_}</span>
              ${re==="details"?v:""}
            </div>
          </${xe}>
          ${Ku}
        </${re}>
      `}let A,D,k,$,L="",B="",S="";const{open:z,tagContainer:M,tagHeader:N}=c(f[p]),R=[["class",`${F}-code`]];z&&R.push(["open",""]);const te={attrs:Wu(f[p],R)};for(let V=p;V<f.length&&(A=f[V],[D,k]=a(A),D===C);V++){A.info=A.info.replace(r,"").replace(i,""),A.hidden=!0;const re=`${F}-codetab-${u.editorId}-${p}-${V-p}`;$=V-p>0?"":"checked",L+=`
        <li>
          <input
            type="radio"
            id="label-${F}-codetab-label-1-${u.editorId}-${p}-${V-p}"
            name="${F}-codetab-label-${u.editorId}-${p}"
            class="${re}"
            ${$}
          >
          <label
            for="label-${F}-codetab-label-1-${u.editorId}-${p}-${V-p}"
            onclick="this.getRootNode().querySelectorAll('.${re}').forEach(e => e.click())"
          >
            ${k||s(A)}
          </label>
        </li>`,B+=`
        <div role="tabpanel">
          <input
            type="radio"
            name="${F}-codetab-pre-${u.editorId}-${p}"
            class="${re}"
            ${$}
            role="presentation">
          ${t(f,V,b,h,m)}
        </div>`,S+=`
        <input
          type="radio"
          name="${F}-codetab-lang-${u.editorId}-${p}"
          class="${re}"
          ${$}
          role="presentation">
        <span class=${F}-code-lang role="note">${s(A)}</span>`}return`
      <${M} ${m.renderAttrs(te)}>
        <${N} class="${F}-code-head">
          <div class="${F}-code-flag">
            <ul class="${F}-codetab-label" role="tablist">${L}</ul>
          </div>
          <div class="${F}-code-action">
            <span class="${F}-codetab-lang">${S}</span>
            <span class="${F}-copy-button" data-tips="${g}"${y?" data-is-icon=true":""}>${_}</span>
            ${M==="details"?v:""}
          </div>
        </${N}>
        ${B}
      </${M}>
    `};e.renderer.rules.fence=l,e.renderer.rules.code_block=l};function Wa(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Tt={exports:{}},G={},It={exports:{}},Ye={};function yr(){var e={};return e["align-content"]=!1,e["align-items"]=!1,e["align-self"]=!1,e["alignment-adjust"]=!1,e["alignment-baseline"]=!1,e.all=!1,e["anchor-point"]=!1,e.animation=!1,e["animation-delay"]=!1,e["animation-direction"]=!1,e["animation-duration"]=!1,e["animation-fill-mode"]=!1,e["animation-iteration-count"]=!1,e["animation-name"]=!1,e["animation-play-state"]=!1,e["animation-timing-function"]=!1,e.azimuth=!1,e["backface-visibility"]=!1,e.background=!0,e["background-attachment"]=!0,e["background-clip"]=!0,e["background-color"]=!0,e["background-image"]=!0,e["background-origin"]=!0,e["background-position"]=!0,e["background-repeat"]=!0,e["background-size"]=!0,e["baseline-shift"]=!1,e.binding=!1,e.bleed=!1,e["bookmark-label"]=!1,e["bookmark-level"]=!1,e["bookmark-state"]=!1,e.border=!0,e["border-bottom"]=!0,e["border-bottom-color"]=!0,e["border-bottom-left-radius"]=!0,e["border-bottom-right-radius"]=!0,e["border-bottom-style"]=!0,e["border-bottom-width"]=!0,e["border-collapse"]=!0,e["border-color"]=!0,e["border-image"]=!0,e["border-image-outset"]=!0,e["border-image-repeat"]=!0,e["border-image-slice"]=!0,e["border-image-source"]=!0,e["border-image-width"]=!0,e["border-left"]=!0,e["border-left-color"]=!0,e["border-left-style"]=!0,e["border-left-width"]=!0,e["border-radius"]=!0,e["border-right"]=!0,e["border-right-color"]=!0,e["border-right-style"]=!0,e["border-right-width"]=!0,e["border-spacing"]=!0,e["border-style"]=!0,e["border-top"]=!0,e["border-top-color"]=!0,e["border-top-left-radius"]=!0,e["border-top-right-radius"]=!0,e["border-top-style"]=!0,e["border-top-width"]=!0,e["border-width"]=!0,e.bottom=!1,e["box-decoration-break"]=!0,e["box-shadow"]=!0,e["box-sizing"]=!0,e["box-snap"]=!0,e["box-suppress"]=!0,e["break-after"]=!0,e["break-before"]=!0,e["break-inside"]=!0,e["caption-side"]=!1,e.chains=!1,e.clear=!0,e.clip=!1,e["clip-path"]=!1,e["clip-rule"]=!1,e.color=!0,e["color-interpolation-filters"]=!0,e["column-count"]=!1,e["column-fill"]=!1,e["column-gap"]=!1,e["column-rule"]=!1,e["column-rule-color"]=!1,e["column-rule-style"]=!1,e["column-rule-width"]=!1,e["column-span"]=!1,e["column-width"]=!1,e.columns=!1,e.contain=!1,e.content=!1,e["counter-increment"]=!1,e["counter-reset"]=!1,e["counter-set"]=!1,e.crop=!1,e.cue=!1,e["cue-after"]=!1,e["cue-before"]=!1,e.cursor=!1,e.direction=!1,e.display=!0,e["display-inside"]=!0,e["display-list"]=!0,e["display-outside"]=!0,e["dominant-baseline"]=!1,e.elevation=!1,e["empty-cells"]=!1,e.filter=!1,e.flex=!1,e["flex-basis"]=!1,e["flex-direction"]=!1,e["flex-flow"]=!1,e["flex-grow"]=!1,e["flex-shrink"]=!1,e["flex-wrap"]=!1,e.float=!1,e["float-offset"]=!1,e["flood-color"]=!1,e["flood-opacity"]=!1,e["flow-from"]=!1,e["flow-into"]=!1,e.font=!0,e["font-family"]=!0,e["font-feature-settings"]=!0,e["font-kerning"]=!0,e["font-language-override"]=!0,e["font-size"]=!0,e["font-size-adjust"]=!0,e["font-stretch"]=!0,e["font-style"]=!0,e["font-synthesis"]=!0,e["font-variant"]=!0,e["font-variant-alternates"]=!0,e["font-variant-caps"]=!0,e["font-variant-east-asian"]=!0,e["font-variant-ligatures"]=!0,e["font-variant-numeric"]=!0,e["font-variant-position"]=!0,e["font-weight"]=!0,e.grid=!1,e["grid-area"]=!1,e["grid-auto-columns"]=!1,e["grid-auto-flow"]=!1,e["grid-auto-rows"]=!1,e["grid-column"]=!1,e["grid-column-end"]=!1,e["grid-column-start"]=!1,e["grid-row"]=!1,e["grid-row-end"]=!1,e["grid-row-start"]=!1,e["grid-template"]=!1,e["grid-template-areas"]=!1,e["grid-template-columns"]=!1,e["grid-template-rows"]=!1,e["hanging-punctuation"]=!1,e.height=!0,e.hyphens=!1,e.icon=!1,e["image-orientation"]=!1,e["image-resolution"]=!1,e["ime-mode"]=!1,e["initial-letters"]=!1,e["inline-box-align"]=!1,e["justify-content"]=!1,e["justify-items"]=!1,e["justify-self"]=!1,e.left=!1,e["letter-spacing"]=!0,e["lighting-color"]=!0,e["line-box-contain"]=!1,e["line-break"]=!1,e["line-grid"]=!1,e["line-height"]=!1,e["line-snap"]=!1,e["line-stacking"]=!1,e["line-stacking-ruby"]=!1,e["line-stacking-shift"]=!1,e["line-stacking-strategy"]=!1,e["list-style"]=!0,e["list-style-image"]=!0,e["list-style-position"]=!0,e["list-style-type"]=!0,e.margin=!0,e["margin-bottom"]=!0,e["margin-left"]=!0,e["margin-right"]=!0,e["margin-top"]=!0,e["marker-offset"]=!1,e["marker-side"]=!1,e.marks=!1,e.mask=!1,e["mask-box"]=!1,e["mask-box-outset"]=!1,e["mask-box-repeat"]=!1,e["mask-box-slice"]=!1,e["mask-box-source"]=!1,e["mask-box-width"]=!1,e["mask-clip"]=!1,e["mask-image"]=!1,e["mask-origin"]=!1,e["mask-position"]=!1,e["mask-repeat"]=!1,e["mask-size"]=!1,e["mask-source-type"]=!1,e["mask-type"]=!1,e["max-height"]=!0,e["max-lines"]=!1,e["max-width"]=!0,e["min-height"]=!0,e["min-width"]=!0,e["move-to"]=!1,e["nav-down"]=!1,e["nav-index"]=!1,e["nav-left"]=!1,e["nav-right"]=!1,e["nav-up"]=!1,e["object-fit"]=!1,e["object-position"]=!1,e.opacity=!1,e.order=!1,e.orphans=!1,e.outline=!1,e["outline-color"]=!1,e["outline-offset"]=!1,e["outline-style"]=!1,e["outline-width"]=!1,e.overflow=!1,e["overflow-wrap"]=!1,e["overflow-x"]=!1,e["overflow-y"]=!1,e.padding=!0,e["padding-bottom"]=!0,e["padding-left"]=!0,e["padding-right"]=!0,e["padding-top"]=!0,e.page=!1,e["page-break-after"]=!1,e["page-break-before"]=!1,e["page-break-inside"]=!1,e["page-policy"]=!1,e.pause=!1,e["pause-after"]=!1,e["pause-before"]=!1,e.perspective=!1,e["perspective-origin"]=!1,e.pitch=!1,e["pitch-range"]=!1,e["play-during"]=!1,e.position=!1,e["presentation-level"]=!1,e.quotes=!1,e["region-fragment"]=!1,e.resize=!1,e.rest=!1,e["rest-after"]=!1,e["rest-before"]=!1,e.richness=!1,e.right=!1,e.rotation=!1,e["rotation-point"]=!1,e["ruby-align"]=!1,e["ruby-merge"]=!1,e["ruby-position"]=!1,e["shape-image-threshold"]=!1,e["shape-outside"]=!1,e["shape-margin"]=!1,e.size=!1,e.speak=!1,e["speak-as"]=!1,e["speak-header"]=!1,e["speak-numeral"]=!1,e["speak-punctuation"]=!1,e["speech-rate"]=!1,e.stress=!1,e["string-set"]=!1,e["tab-size"]=!1,e["table-layout"]=!1,e["text-align"]=!0,e["text-align-last"]=!0,e["text-combine-upright"]=!0,e["text-decoration"]=!0,e["text-decoration-color"]=!0,e["text-decoration-line"]=!0,e["text-decoration-skip"]=!0,e["text-decoration-style"]=!0,e["text-emphasis"]=!0,e["text-emphasis-color"]=!0,e["text-emphasis-position"]=!0,e["text-emphasis-style"]=!0,e["text-height"]=!0,e["text-indent"]=!0,e["text-justify"]=!0,e["text-orientation"]=!0,e["text-overflow"]=!0,e["text-shadow"]=!0,e["text-space-collapse"]=!0,e["text-transform"]=!0,e["text-underline-position"]=!0,e["text-wrap"]=!0,e.top=!1,e.transform=!1,e["transform-origin"]=!1,e["transform-style"]=!1,e.transition=!1,e["transition-delay"]=!1,e["transition-duration"]=!1,e["transition-property"]=!1,e["transition-timing-function"]=!1,e["unicode-bidi"]=!1,e["vertical-align"]=!1,e.visibility=!1,e["voice-balance"]=!1,e["voice-duration"]=!1,e["voice-family"]=!1,e["voice-pitch"]=!1,e["voice-range"]=!1,e["voice-rate"]=!1,e["voice-stress"]=!1,e["voice-volume"]=!1,e.volume=!1,e["white-space"]=!1,e.widows=!1,e.width=!0,e["will-change"]=!1,e["word-break"]=!0,e["word-spacing"]=!0,e["word-wrap"]=!0,e["wrap-flow"]=!1,e["wrap-through"]=!1,e["writing-mode"]=!1,e["z-index"]=!1,e}function Ga(e,u,t){}function Va(e,u,t){}var Za=/javascript\s*\:/img;function Xa(e,u){return Za.test(u)?"":u}Ye.whiteList=yr();Ye.getDefaultWhiteList=yr;Ye.onAttr=Ga;Ye.onIgnoreAttr=Va;Ye.safeAttrValue=Xa;var Ya={indexOf:function(e,u){var t,n;if(Array.prototype.indexOf)return e.indexOf(u);for(t=0,n=e.length;t<n;t++)if(e[t]===u)return t;return-1},forEach:function(e,u,t){var n,r;if(Array.prototype.forEach)return e.forEach(u,t);for(n=0,r=e.length;n<r;n++)u.call(t,e[n],n,e)},trim:function(e){return String.prototype.trim?e.trim():e.replace(/(^\s*)|(\s*$)/g,"")},trimRight:function(e){return String.prototype.trimRight?e.trimRight():e.replace(/(\s*$)/g,"")}},lu=Ya;function Qa(e,u){e=lu.trimRight(e),e[e.length-1]!==";"&&(e+=";");var t=e.length,n=!1,r=0,i=0,o="";function a(){if(!n){var l=lu.trim(e.slice(r,i)),f=l.indexOf(":");if(f!==-1){var p=lu.trim(l.slice(0,f)),b=lu.trim(l.slice(f+1));if(p){var h=u(r,o.length,p,b,l);h&&(o+=h+"; ")}}}r=i+1}for(;i<t;i++){var s=e[i];if(s==="/"&&e[i+1]==="*"){var c=e.indexOf("*/",i+2);if(c===-1)break;i=c+1,r=i+1,n=!1}else s==="("?n=!0:s===")"?n=!1:s===";"?n||a():s===`
`&&a()}return lu.trim(o)}var Ja=Qa,Tu=Ye,Ka=Ja;function Fn(e){return e==null}function es(e){var u={};for(var t in e)u[t]=e[t];return u}function vr(e){e=es(e||{}),e.whiteList=e.whiteList||Tu.whiteList,e.onAttr=e.onAttr||Tu.onAttr,e.onIgnoreAttr=e.onIgnoreAttr||Tu.onIgnoreAttr,e.safeAttrValue=e.safeAttrValue||Tu.safeAttrValue,this.options=e}vr.prototype.process=function(e){if(e=e||"",e=e.toString(),!e)return"";var u=this,t=u.options,n=t.whiteList,r=t.onAttr,i=t.onIgnoreAttr,o=t.safeAttrValue,a=Ka(e,function(s,c,l,f,p){var b=n[l],h=!1;if(b===!0?h=b:typeof b=="function"?h=b(f):b instanceof RegExp&&(h=b.test(f)),h!==!0&&(h=!1),f=o(l,f),!!f){var m={position:c,sourcePosition:s,source:p,isWhite:h};if(h){var x=r(l,f,m);return Fn(x)?l+":"+f:x}else{var x=i(l,f,m);if(!Fn(x))return x}}});return a};var us=vr;(function(e,u){var t=Ye,n=us;function r(o,a){var s=new n(a);return s.process(o)}u=e.exports=r,u.FilterCSS=n;for(var i in t)u[i]=t[i];typeof window<"u"&&(window.filterCSS=e.exports)})(It,It.exports);var Ht=It.exports,jt={indexOf:function(e,u){var t,n;if(Array.prototype.indexOf)return e.indexOf(u);for(t=0,n=e.length;t<n;t++)if(e[t]===u)return t;return-1},forEach:function(e,u,t){var n,r;if(Array.prototype.forEach)return e.forEach(u,t);for(n=0,r=e.length;n<r;n++)u.call(t,e[n],n,e)},trim:function(e){return String.prototype.trim?e.trim():e.replace(/(^\s*)|(\s*$)/g,"")},spaceIndex:function(e){var u=/\s|\n|\t/,t=u.exec(e);return t?t.index:-1}},ts=Ht.FilterCSS,ns=Ht.getDefaultWhiteList,Gu=jt;function Cr(){return{a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height","loading"],ins:["datetime"],kbd:[],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]}}var Er=new ts;function rs(e,u,t){}function is(e,u,t){}function os(e,u,t){}function as(e,u,t){}function Ar(e){return e.replace(cs,"&lt;").replace(ls,"&gt;")}function ss(e,u,t,n){if(t=Ir(t),u==="href"||u==="src"){if(t=Gu.trim(t),t==="#")return"#";if(!(t.substr(0,7)==="http://"||t.substr(0,8)==="https://"||t.substr(0,7)==="mailto:"||t.substr(0,4)==="tel:"||t.substr(0,11)==="data:image/"||t.substr(0,6)==="ftp://"||t.substr(0,2)==="./"||t.substr(0,3)==="../"||t[0]==="#"||t[0]==="/"))return""}else if(u==="background"){if(Iu.lastIndex=0,Iu.test(t))return""}else if(u==="style"){if(Sn.lastIndex=0,Sn.test(t)||(Tn.lastIndex=0,Tn.test(t)&&(Iu.lastIndex=0,Iu.test(t))))return"";n!==!1&&(n=n||Er,t=n.process(t))}return t=zr(t),t}var cs=/</g,ls=/>/g,fs=/"/g,ds=/&quot;/g,hs=/&#([a-zA-Z0-9]*);?/gim,ps=/&colon;?/gim,bs=/&newline;?/gim,Iu=/((j\s*a\s*v\s*a|v\s*b|l\s*i\s*v\s*e)\s*s\s*c\s*r\s*i\s*p\s*t\s*|m\s*o\s*c\s*h\s*a):/gi,Sn=/e\s*x\s*p\s*r\s*e\s*s\s*s\s*i\s*o\s*n\s*\(.*/gi,Tn=/u\s*r\s*l\s*\(.*/gi;function wr(e){return e.replace(fs,"&quot;")}function Dr(e){return e.replace(ds,'"')}function Fr(e){return e.replace(hs,function(t,n){return n[0]==="x"||n[0]==="X"?String.fromCharCode(parseInt(n.substr(1),16)):String.fromCharCode(parseInt(n,10))})}function Sr(e){return e.replace(ps,":").replace(bs," ")}function Tr(e){for(var u="",t=0,n=e.length;t<n;t++)u+=e.charCodeAt(t)<32?" ":e.charAt(t);return Gu.trim(u)}function Ir(e){return e=Dr(e),e=Fr(e),e=Sr(e),e=Tr(e),e}function zr(e){return e=wr(e),e=Ar(e),e}function ms(){return""}function gs(e,u){typeof u!="function"&&(u=function(){});var t=!Array.isArray(e);function n(o){return t?!0:Gu.indexOf(e,o)!==-1}var r=[],i=!1;return{onIgnoreTag:function(o,a,s){if(n(o))if(s.isClosing){var c="[/removed]",l=s.position+c.length;return r.push([i!==!1?i:s.position,l]),i=!1,c}else return i||(i=s.position),"[removed]";else return u(o,a,s)},remove:function(o){var a="",s=0;return Gu.forEach(r,function(c){a+=o.slice(s,c[0]),s=c[1]}),a+=o.slice(s),a}}}function xs(e){for(var u="",t=0;t<e.length;){var n=e.indexOf("<!--",t);if(n===-1){u+=e.slice(t);break}u+=e.slice(t,n);var r=e.indexOf("-->",n);if(r===-1)break;t=r+3}return u}function _s(e){var u=e.split("");return u=u.filter(function(t){var n=t.charCodeAt(0);return n===127?!1:n<=31?n===10||n===13:!0}),u.join("")}G.whiteList=Cr();G.getDefaultWhiteList=Cr;G.onTag=rs;G.onIgnoreTag=is;G.onTagAttr=os;G.onIgnoreTagAttr=as;G.safeAttrValue=ss;G.escapeHtml=Ar;G.escapeQuote=wr;G.unescapeQuote=Dr;G.escapeHtmlEntities=Fr;G.escapeDangerHtml5Entities=Sr;G.clearNonPrintableCharacter=Tr;G.friendlyAttrValue=Ir;G.escapeAttrValue=zr;G.onIgnoreTagStripAll=ms;G.StripTagBody=gs;G.stripCommentTag=xs;G.stripBlankChar=_s;G.attributeWrapSign='"';G.cssFilter=Er;G.getDefaultCSSWhiteList=ns;var Qu={},Ne=jt;function ks(e){var u=Ne.spaceIndex(e),t;return u===-1?t=e.slice(1,-1):t=e.slice(1,u+1),t=Ne.trim(t).toLowerCase(),t.slice(0,1)==="/"&&(t=t.slice(1)),t.slice(-1)==="/"&&(t=t.slice(0,-1)),t}function ys(e){return e.slice(0,2)==="</"}function vs(e,u,t){var n="",r=0,i=!1,o=!1,a=0,s=e.length,c="",l="";e:for(a=0;a<s;a++){var f=e.charAt(a);if(i===!1){if(f==="<"){i=a;continue}}else if(o===!1){if(f==="<"){n+=t(e.slice(r,a)),i=a,r=a;continue}if(f===">"||a===s-1){n+=t(e.slice(r,i)),l=e.slice(i,a+1),c=ks(l),n+=u(i,n.length,c,l,ys(l)),r=a+1,i=!1;continue}if(f==='"'||f==="'")for(var p=1,b=e.charAt(a-p);b.trim()===""||b==="=";){if(b==="="){o=f;continue e}b=e.charAt(a-++p)}}else if(f===o){o=!1;continue}}return r<s&&(n+=t(e.substr(r))),n}var Cs=/[^a-zA-Z0-9\\_:.-]/gim;function Es(e,u){var t=0,n=0,r=[],i=!1,o=e.length;function a(p,b){if(p=Ne.trim(p),p=p.replace(Cs,"").toLowerCase(),!(p.length<1)){var h=u(p,b||"");h&&r.push(h)}}for(var s=0;s<o;s++){var c=e.charAt(s),l,f;if(i===!1&&c==="="){i=e.slice(t,s),t=s+1,n=e.charAt(t)==='"'||e.charAt(t)==="'"?t:ws(e,s+1);continue}if(i!==!1&&s===n){if(f=e.indexOf(c,s+1),f===-1)break;l=Ne.trim(e.slice(n+1,f)),a(i,l),i=!1,s=f,t=s+1;continue}if(/\s|\n|\t/.test(c))if(e=e.replace(/\s|\n|\t/g," "),i===!1)if(f=As(e,s),f===-1){l=Ne.trim(e.slice(t,s)),a(l),i=!1,t=s+1;continue}else{s=f-1;continue}else if(f=Ds(e,s-1),f===-1){l=Ne.trim(e.slice(t,s)),l=In(l),a(i,l),i=!1,t=s+1;continue}else continue}return t<e.length&&(i===!1?a(e.slice(t)):a(i,In(Ne.trim(e.slice(t))))),Ne.trim(r.join(" "))}function As(e,u){for(;u<e.length;u++){var t=e[u];if(t!==" ")return t==="="?u:-1}}function ws(e,u){for(;u<e.length;u++){var t=e[u];if(t!==" ")return t==="'"||t==='"'?u:-1}}function Ds(e,u){for(;u>0;u--){var t=e[u];if(t!==" ")return t==="="?u:-1}}function Fs(e){return e[0]==='"'&&e[e.length-1]==='"'||e[0]==="'"&&e[e.length-1]==="'"}function In(e){return Fs(e)?e.substr(1,e.length-2):e}Qu.parseTag=vs;Qu.parseAttr=Es;var Ss=Ht.FilterCSS,me=G,Lr=Qu,Ts=Lr.parseTag,Is=Lr.parseAttr,Bu=jt;function zu(e){return e==null}function zs(e){var u=Bu.spaceIndex(e);if(u===-1)return{html:"",closing:e[e.length-2]==="/"};e=Bu.trim(e.slice(u+1,-1));var t=e[e.length-1]==="/";return t&&(e=Bu.trim(e.slice(0,-1))),{html:e,closing:t}}function Ls(e){var u={};for(var t in e)u[t]=e[t];return u}function Rs(e){var u={};for(var t in e)Array.isArray(e[t])?u[t.toLowerCase()]=e[t].map(function(n){return n.toLowerCase()}):u[t.toLowerCase()]=e[t];return u}function Rr(e){e=Ls(e||{}),e.stripIgnoreTag&&(e.onIgnoreTag&&console.error('Notes: cannot use these two options "stripIgnoreTag" and "onIgnoreTag" at the same time'),e.onIgnoreTag=me.onIgnoreTagStripAll),e.whiteList||e.allowList?e.whiteList=Rs(e.whiteList||e.allowList):e.whiteList=me.whiteList,this.attributeWrapSign=e.singleQuotedAttributeValue===!0?"'":me.attributeWrapSign,e.onTag=e.onTag||me.onTag,e.onTagAttr=e.onTagAttr||me.onTagAttr,e.onIgnoreTag=e.onIgnoreTag||me.onIgnoreTag,e.onIgnoreTagAttr=e.onIgnoreTagAttr||me.onIgnoreTagAttr,e.safeAttrValue=e.safeAttrValue||me.safeAttrValue,e.escapeHtml=e.escapeHtml||me.escapeHtml,this.options=e,e.css===!1?this.cssFilter=!1:(e.css=e.css||{},this.cssFilter=new Ss(e.css))}Rr.prototype.process=function(e){if(e=e||"",e=e.toString(),!e)return"";var u=this,t=u.options,n=t.whiteList,r=t.onTag,i=t.onIgnoreTag,o=t.onTagAttr,a=t.onIgnoreTagAttr,s=t.safeAttrValue,c=t.escapeHtml,l=u.attributeWrapSign,f=u.cssFilter;t.stripBlankChar&&(e=me.stripBlankChar(e)),t.allowCommentTag||(e=me.stripCommentTag(e));var p=!1;t.stripIgnoreTagBody&&(p=me.StripTagBody(t.stripIgnoreTagBody,i),i=p.onIgnoreTag);var b=Ts(e,function(h,m,x,g,_){var y={sourcePosition:h,position:m,isClosing:_,isWhite:Object.prototype.hasOwnProperty.call(n,x)},v=r(x,g,y);if(!zu(v))return v;if(y.isWhite){if(y.isClosing)return"</"+x+">";var C=zs(g),A=n[x],D=Is(C.html,function(k,$){var L=Bu.indexOf(A,k)!==-1,B=o(x,k,$,L);return zu(B)?L?($=s(x,k,$,f),$?k+"="+l+$+l:k):(B=a(x,k,$,L),zu(B)?void 0:B):B});return g="<"+x,D&&(g+=" "+D),C.closing&&(g+=" /"),g+=">",g}else return v=i(x,g,y),zu(v)?c(g):v},c);return p&&(b=p.remove(b)),b};var $s=Rr;(function(e,u){var t=G,n=Qu,r=$s;function i(a,s){var c=new r(s);return c.process(a)}u=e.exports=i,u.filterXSS=i,u.FilterXSS=r,function(){for(var a in t)u[a]=t[a];for(var s in n)u[s]=n[s]}(),typeof window<"u"&&(window.filterXSS=e.exports);function o(){return typeof self<"u"&&typeof DedicatedWorkerGlobalScope<"u"&&self instanceof DedicatedWorkerGlobalScope}o()&&(self.filterXSS=e.exports)})(Tt,Tt.exports);var pu=Tt.exports;const Os=Wa(pu),Ms=Ta({__proto__:null,default:Os},[pu]),zn={img:["class"],input:["class","disabled","type","checked"],iframe:["class","width","height","src","title","border","frameborder","framespacing","allow","allowfullscreen"]},Ps=(e,u)=>{const{extendedWhiteList:t={},xss:n={}}=u;let r;if(typeof n=="function")r=new pu.FilterXSS(n(Ms));else{const i=pu.getDefaultWhiteList();[...Object.keys(t),...Object.keys(zn)].forEach(a=>{const s=i[a]||[],c=zn[a]||[],l=t[a]||[];i[a]=[...new Set([...s,...c,...l])]}),r=new pu.FilterXSS({whiteList:i,...n})}e.core.ruler.after("linkify","xss",i=>{for(let o=0;o<i.tokens.length;o++){const a=i.tokens[o];switch(a.type){case"html_block":{a.content=r.process(a.content);break}case"inline":{(a.children||[]).forEach(c=>{c.type==="html_inline"&&(c.content=r.process(c.content))});break}}}})},Ln=(e,u,t)=>{const n=e.attrIndex(u),r=[u,t];n<0?e.attrPush(r):(e.attrs=e.attrs||[],e.attrs[n]=r)},Bs=e=>e.type==="inline",Ns=e=>e.type==="paragraph_open",qs=e=>e.type==="list_item_open",Hs=e=>e.content.indexOf("[ ] ")===0||e.content.indexOf("[x] ")===0||e.content.indexOf("[X] ")===0,js=(e,u)=>Bs(e[u])&&Ns(e[u-1])&&qs(e[u-2])&&Hs(e[u]),Us=(e,u)=>{const t=e[u].level-1;for(let n=u-1;n>=0;n--)if(e[n].level===t)return n;return-1},Ws=e=>{const u=new e("html_inline","",0);return u.content="<label>",u},Gs=e=>{const u=new e("html_inline","",0);return u.content="</label>",u},Vs=(e,u,t)=>{const n=new t("html_inline","",0);return n.content='<label class="task-list-item-label" for="'+u+'">'+e+"</label>",n.attrs=[{for:u}],n},Zs=(e,u,t)=>{const n=new u("html_inline","",0),r=t.enabled?" ":' disabled="" ';return e.content.indexOf("[ ] ")===0?n.content='<input class="task-list-item-checkbox"'+r+'type="checkbox">':(e.content.indexOf("[x] ")===0||e.content.indexOf("[X] ")===0)&&(n.content='<input class="task-list-item-checkbox" checked=""'+r+'type="checkbox">'),n},Xs=(e,u,t)=>{if(e.children=e.children||[],e.children.unshift(Zs(e,u,t)),e.children[1].content=e.children[1].content.slice(3),e.content=e.content.slice(3),t.label)if(t.labelAfter){e.children.pop();const n="task-item-"+Math.ceil(Math.random()*(1e4*1e3)-1e3);e.children[0].content=e.children[0].content.slice(0,-1)+' id="'+n+'">',e.children.push(Vs(e.content,n,u))}else e.children.unshift(Ws(u)),e.children.push(Gs(u))},Ys=(e,u={})=>{e.core.ruler.after("inline","github-task-lists",t=>{const n=t.tokens;for(let r=2;r<n.length;r++)js(n,r)&&(Xs(n[r],t.Token,u),Ln(n[r-2],"class","task-list-item"+(u.enabled?" enabled":" ")),Ln(n[Us(n,r-2)],"class","contains-task-list"))})},Qs=e=>{e.core.ruler.push("init-line-number",u=>(u.tokens.forEach(t=>{t.map&&(t.attrs||(t.attrs=[]),t.attrs.push(["data-line",t.map[0].toString()]))}),!0))},Js=(e,u)=>{const{editorConfig:t,markdownItConfig:n,markdownItPlugins:r}=ae,i=Y("editorId"),o=Y("language"),a=Y("usedLanguageText"),s=Y("showCodeRowNumber"),c=Y("theme"),l=Y("customIcon"),f=Y("rootRef"),p=hu([]),b=La(e),h=$a(e),{reRenderRef:m,replaceMermaid:x}=Ra(e),g=ge({html:!0,breaks:!0,linkify:!0});n(g,{editorId:i});const _=[{type:"image",plugin:va,options:{figcaption:!0,classes:"md-zoom"}},{type:"admonition",plugin:Na,options:{}},{type:"taskList",plugin:Ys,options:{}},{type:"heading",plugin:qa,options:{mdHeadingId:e.mdHeadingId,headsRef:p}},{type:"code",plugin:Ua,options:{editorId:i,usedLanguageTextRef:a,codeFoldable:e.codeFoldable,autoFoldThreshold:e.autoFoldThreshold,customIconRef:l}},{type:"xss",plugin:Ps,options:{}},{type:"sub",plugin:Aa,options:{}},{type:"sup",plugin:Fa,options:{}}];e.noKatex||_.push({type:"katex",plugin:Ba,options:{katexRef:h}}),e.noMermaid||_.push({type:"mermaid",plugin:Oa,options:{themeRef:c}}),r(_,{editorId:i}).forEach($=>{g.use($.plugin,$.options)});const y=g.options.highlight;g.set({highlight:($,L,B)=>{if(y){const M=y($,L,B);if(M)return M}let S;!e.noHighlight&&b.value?b.value.getLanguage(L)?S=b.value.highlight($,{language:L,ignoreIllegals:!0}).value:S=b.value.highlightAuto($).value:S=g.utils.escapeHtml($);const z=s?Ci(S.replace(/^\n+|\n+$/g,""),$.replace(/^\n+|\n+$/g,"")):`<span class="${F}-code-block">${S.replace(/^\n+|\n+$/g,"")}</span>`;return`<pre><code class="language-${L}" language=${L}>${z}</code></pre>`}}),Qs(g);const v=hu(`_article-key_${gt()}`),C=hu(e.sanitize(g.render(e.modelValue))),A=()=>{O.emit(i,Lu,C.value),e.onHtmlChanged(C.value),e.onGetCatalog(p.value),O.emit(i,rt,p.value),bu(()=>{x().then(()=>{tn(f.value.querySelectorAll(`#${i} .${F}-mermaid`))})})};pe(A);const D=()=>{p.value=[],C.value=e.sanitize(g.render(e.modelValue)),A()},k=Pe(()=>(e.noKatex||h.value)&&(e.noHighlight||b.value));return ee([Bn(e,"modelValue"),k,m,o],Nn(D,u?0:t.renderDelay)),ee(()=>e.setting.preview,()=>{e.setting.preview&&bu(()=>{x().then(()=>{tn(f.value.querySelectorAll(`#${i} .${F}-mermaid`))}),O.emit(i,rt,p.value)})}),pe(()=>{O.on(i,{name:xi,callback(){O.emit(i,rt,p.value)}}),O.on(i,{name:Lt,callback:()=>{v.value=`_article-key_${gt()}`,D()}})}),{html:C,key:v}},Rn={checked:{regexp:/- \[x\]/,value:"- [ ]"},unChecked:{regexp:/- \[\s\]/,value:"- [x]"}},Ks=(e,u)=>{const t=Y("editorId"),n=Y("rootRef");let r=()=>{};const i=()=>{if(!n.value)return!1;const o=n.value.querySelectorAll(".task-list-item.enabled"),a=s=>{var c;s.preventDefault();const l=s.target.checked?"unChecked":"checked",f=(c=s.target.parentElement)==null?void 0:c.dataset.line;if(!f)return;const p=Number(f),b=e.modelValue.split(`
`),h=b[Number(p)].replace(Rn[l].regexp,Rn[l].value);e.previewOnly?(b[Number(p)]=h,e.onChange(b.join(`
`))):O.emit(t,ki,p+1,h)};o.forEach(s=>{s.addEventListener("click",a)}),r=()=>{o.forEach(s=>{s.removeEventListener("click",a)})}};Pn(()=>{r()}),ee([u],()=>{r(),bu(i)},{immediate:!0})},$r={modelValue:{type:String,default:""},onChange:{type:Function,default:()=>{}},setting:{type:Object,default:()=>({preview:!0})},onHtmlChanged:{type:Function,default:()=>{}},onGetCatalog:{type:Function,default:()=>{}},mdHeadingId:{type:Function,default:()=>""},noMermaid:{type:Boolean,default:!1},sanitize:{type:Function,default:e=>e},noKatex:{type:Boolean,default:!1},formatCopiedText:{type:Function,default:e=>e},noHighlight:{type:Boolean,default:!1},previewOnly:{type:Boolean,default:!1},noImgZoomIn:{type:Boolean},sanitizeMermaid:{type:Function},codeFoldable:{type:Boolean},autoFoldThreshold:{type:Number}},kc={...$r,updateModelValue:{type:Function,default:()=>{}},placeholder:{type:String,default:""},scrollAuto:{type:Boolean},autofocus:{type:Boolean},disabled:{type:Boolean},readonly:{type:Boolean},maxlength:{type:Number},autoDetectCode:{type:Boolean},onBlur:{type:Function,default:()=>{}},onFocus:{type:Function,default:()=>{}},noPrettier:{type:Boolean},completions:{type:Array},catalogVisible:{type:Boolean},theme:{type:String,default:"light"},onInput:{type:Function},onDrop:{type:Function,default:()=>{}},inputBoxWitdh:{type:String},onInputBoxWitdhChange:{type:Function},transformImgUrl:{type:Function,default:e=>e}},ec=zt({name:"ContentPreview",props:$r,setup(e){const u=Y("editorId"),t=Y("previewTheme"),n=Y("showCodeRowNumber"),{html:r,key:i}=Js(e,e.previewOnly);return za(e,r,i),Ia(e,r),Ks(e,r),()=>qe(Jr,null,[e.setting.preview&&qe("div",{id:`${u}-preview-wrapper`,class:`${F}-preview-wrapper`,key:"content-preview-wrapper"},[qe("div",{key:i.value,id:`${u}-preview`,class:[`${F}-preview`,`${t==null?void 0:t.value}-theme`,n&&`${F}-scrn`],innerHTML:r.value},null)]),!e.previewOnly&&e.setting.htmlPreview&&qe("div",{id:`${u}-html-wrapper`,class:`${F}-preview-wrapper`,key:"html-preview-wrapper"},[qe("div",{class:`${F}-html`},[r.value])])])}}),yc=(e,u)=>{const{editorId:t}=e,n=mt({buildFinished:!1,html:""});ee(()=>e.modelValue,()=>{n.buildFinished=!1}),pe(()=>{O.on(t,{name:Lu,callback(r){n.buildFinished=!0,n.html=r}}),O.on(t,{name:Hn,callback(){const r=new Promise(i=>{if(n.buildFinished)i(n.html);else{const o=a=>{i(a),O.remove(t,Lu,o)};O.on(t,{name:Lu,callback:o})}});e.onSave?e.onSave(e.modelValue,r):u.emit("onSave",e.modelValue,r)}})})},Or=(e,u)=>{const{editorId:t}=e,n=ae.editorExtensions.highlight,r=ae.editorExtensionsAttrs.highlight;Fe("editorId",t),Fe("rootRef",u),Fe("theme",Pe(()=>e.theme)),Fe("language",Pe(()=>e.language)),Fe("highlight",Pe(()=>{const{js:o}=n,a={...xt,...n.css},{js:s,css:c={}}=r||{},l=e.codeStyleReverse&&e.codeStyleReverseList.includes(e.previewTheme)?"dark":e.theme,f=a[e.codeTheme]?a[e.codeTheme][l]:xt.atom[l],p=a[e.codeTheme]&&c[e.codeTheme]?c[e.codeTheme][l]:c.atom?c.atom[l]:{};return{js:{src:o,...s},css:{href:f,...p}}})),Fe("showCodeRowNumber",e.showCodeRowNumber);const i=Pe(()=>{const o={...Xt,...ae.editorConfig.languageUserDefined};return qn(fu(Xt["en-US"]),o[e.language]||{})});Fe("usedLanguageText",i),Fe("previewTheme",Pe(()=>e.previewTheme)),Fe("customIcon",Pe(()=>e.customIcon))},vc=(e,u)=>{Or(e,u),Fe("tabWidth",e.tabWidth)},Mr=e=>{pe(()=>{const{editorExtensions:u,editorExtensionsAttrs:t,iconfontType:n}=ae;e.noIconfont||(n==="svg"?he("script",{...t.iconfont,src:u.iconfont,id:`${F}-icon`}):he("link",{...t.iconfontClass,rel:"stylesheet",href:u.iconfontClass,id:`${F}-icon-class`}))})},Cc=e=>{const{noPrettier:u,noUploadImg:t}=e,{editorExtensions:n,editorExtensionsAttrs:r}=ae,i=u||n.prettier.prettierInstance,o=u||n.prettier.parserMarkdownInstance,a=t||n.cropper.instance;pe(()=>{if(!a){const{js:s={},css:c={}}=r.cropper||{};he("link",{...c,rel:"stylesheet",href:n.cropper.css,id:`${F}-cropperCss`}),he("script",{...s,src:n.cropper.js,id:`${F}-cropper`})}if(!i){const{standaloneJs:s={}}=r.prettier||{};he("script",{...s,src:n.prettier.standaloneJs,id:`${F}-prettier`})}if(!o){const{parserMarkdownJs:s={}}=r.prettier||{};he("script",{...s,src:n.prettier.parserMarkdownJs,id:`${F}-prettierMD`})}}),Mr(e)},Ec=(e,u)=>{const{editorId:t}=e;pe(()=>{O.on(t,{name:mi,callback:n=>{e.onError instanceof Function?e.onError(n):u.emit("onError",n)}})})},Ac=(e,u)=>{const{editorId:t}=e,n=mt({pageFullscreen:e.pageFullscreen,fullscreen:!1,preview:e.preview,htmlPreview:e.preview?!1:e.htmlPreview,previewOnly:!1}),r=mt({...n}),i=(s,c)=>{const l=c===void 0?!n[s]:c;switch(s){case"preview":{n.htmlPreview=!1,n.previewOnly=!1;break}case"htmlPreview":{n.preview=!1,n.previewOnly=!1;break}case"previewOnly":{l?!n.preview&&!n.htmlPreview&&(n.preview=!0):(r.preview||(n.preview=!1),r.htmlPreview||(n.htmlPreview=!1));break}}r[s]=l,n[s]=l};let o="";const a=()=>{n.pageFullscreen||n.fullscreen?document.body.style.overflow="hidden":document.body.style.overflow=o};return ee(()=>[n.pageFullscreen,n.fullscreen],a),pe(()=>{O.on(t,{name:gi,callback(s,c){const l=f=>{O.emit(t,_t,"image",{desc:"",urls:f}),c&&c()};e.onUploadImg?e.onUploadImg(s,l):u.emit("onUploadImg",s,l)}}),o=document.body.style.overflow,a()}),[n,i]},wc=e=>{const{editorId:u}=e,t=hu(!1);return pe(()=>{O.on(u,{name:jn,callback:r=>{r===void 0?t.value=!t.value:t.value=r}})}),Pe(()=>!e.toolbarsExclude.includes("catalog")&&e.toolbars.includes("catalog")&&t.value)},Dc=(e,u,t,n,r,i)=>{const{editorId:o}=e;ee(()=>n.pageFullscreen,s=>{O.emit(o,Yt,s)}),ee(()=>n.fullscreen,s=>{O.emit(o,Qt,s)}),ee(()=>n.preview,s=>{O.emit(o,Jt,s)}),ee(()=>n.previewOnly,s=>{O.emit(o,Kt,s)}),ee(()=>n.htmlPreview,s=>{O.emit(o,en,s)}),ee(t,s=>{O.emit(o,un,s)});const a={on(s,c){switch(s){case"pageFullscreen":{O.on(o,{name:Yt,callback(l){c(l)}});break}case"fullscreen":{O.on(o,{name:Qt,callback(l){c(l)}});break}case"preview":{O.on(o,{name:Jt,callback(l){c(l)}});break}case"previewOnly":{O.on(o,{name:Kt,callback(l){c(l)}});break}case"htmlPreview":{O.on(o,{name:en,callback(l){c(l)}});break}case"catalog":{O.on(o,{name:un,callback(l){c(l)}});break}}},togglePageFullscreen(s){r("pageFullscreen",s)},toggleFullscreen(s){O.emit(o,bi,s)},togglePreview(s){r("preview",s)},togglePreviewOnly(s){r("previewOnly",s)},toggleHtmlPreview(s){r("htmlPreview",s)},toggleCatalog(s){O.emit(o,jn,s)},triggerSave(){O.emit(o,Hn)},insert(s){O.emit(o,_t,"universal",{generate:s})},focus(s){var c;(c=i.value)==null||c.focus(s)},rerender(){O.emit(o,Lt)},getSelectedText(){var s;return(s=i.value)==null?void 0:s.getSelectedText()},resetHistory(){var s;(s=i.value)==null||s.resetHistory()},domEventHandlers(s){O.emit(o,_i,s)},execCommand(s){O.emit(o,_t,s)},getEditorView(){var s;return(s=i.value)==null?void 0:s.getEditorView()}};u.expose(a)},uc=e=>e,Pr={modelValue:{type:String,default:""},onChange:{type:Function,default:void 0},theme:{type:String,default:"light"},class:{type:String,default:""},language:{type:String,default:"zh-CN"},onHtmlChanged:{type:Function,default:void 0},onGetCatalog:{type:Function,default:void 0},editorId:{type:String,default:()=>Ei("md-editor-v3_")},showCodeRowNumber:{type:Boolean,default:!0},previewTheme:{type:String,default:"default"},style:{type:Object,default:()=>({})},mdHeadingId:{type:Function,default:uc},sanitize:{type:Function,default:e=>e},noMermaid:{type:Boolean,default:!1},noKatex:{type:Boolean,default:!1},codeTheme:{type:String,default:"atom"},noIconfont:{type:Boolean,default:void 0},formatCopiedText:{type:Function,default:e=>e},codeStyleReverse:{type:Boolean,default:!0},codeStyleReverseList:{type:Array,default:["default","mk-cute"]},noHighlight:{type:Boolean,default:!1},noImgZoomIn:{type:Boolean,default:!1},customIcon:{type:Object,default:{}},sanitizeMermaid:{type:Function,default:e=>Promise.resolve(e)},codeFoldable:{type:Boolean,default:!0},autoFoldThreshold:{type:Number,default:30}},Fc={...Pr,onSave:{type:Function,default:void 0},onUploadImg:{type:Function,default:void 0},pageFullscreen:{type:Boolean,default:!1},preview:{type:Boolean,default:!0},htmlPreview:{type:Boolean,default:!1},toolbars:{type:Array,default:si},toolbarsExclude:{type:Array,default:[]},noPrettier:{type:Boolean,default:!1},tabWidth:{type:Number,default:2},tableShape:{type:Array,default:[6,4]},placeholder:{type:String,default:""},defToolbars:{type:[String,Object],default:void 0},onError:{type:Function,default:void 0},footers:{type:Array,default:ci},scrollAuto:{type:Boolean,default:!0},defFooters:{type:[String,Object],default:void 0},noUploadImg:{type:Boolean,default:!1},autoFocus:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},readOnly:{type:Boolean,default:!1},maxLength:{type:Number,default:void 0},autoDetectCode:{type:Boolean,default:!1},onBlur:{type:Function,default:void 0},onFocus:{type:Function,default:void 0},completions:{type:Array,default:void 0},showToolbarName:{type:Boolean,default:!1},onInput:{type:Function,default:void 0},onDrop:{type:Function,default:void 0},inputBoxWitdh:{type:String,default:"50%"},onInputBoxWitdhChange:{type:Function,default:void 0},transformImgUrl:{type:Function,default:e=>e}},Br=["onHtmlChanged","onGetCatalog","onChange","update:modelValue"],Sc=[...Br,"onSave","onUploadImg","onError","onBlur","onFocus","onInput","onDrop","onInputBoxWitdhChange"],tc=(e,u)=>{const{editorId:t}=e,n={rerender(){O.emit(t,Lt)}};u.expose(n)},pt=zt({name:"MdPreview",props:Pr,emits:Br,setup(e,u){const{editorId:t,noKatex:n,noMermaid:r,noHighlight:i}=e,o=hu();return Or(e,o),Mr(e),tc(e,u),Pn(()=>{O.clear(t)}),()=>qe("div",{id:t,class:[F,e.class,e.theme==="dark"&&`${F}-dark`,`${F}-previewOnly`],style:e.style,ref:o},[qe(ec,{modelValue:e.modelValue,onChange:a=>{e.onChange&&e.onChange(a),u.emit("onChange",a),u.emit("update:modelValue",a)},onHtmlChanged:a=>{e.onHtmlChanged?e.onHtmlChanged(a):u.emit("onHtmlChanged",a)},onGetCatalog:a=>{e.onGetCatalog?e.onGetCatalog(a):u.emit("onGetCatalog",a)},mdHeadingId:e.mdHeadingId,noMermaid:r,sanitize:e.sanitize,noKatex:n,formatCopiedText:e.formatCopiedText,noHighlight:i,noImgZoomIn:e.noImgZoomIn,previewOnly:!0,sanitizeMermaid:e.sanitizeMermaid,codeFoldable:e.codeFoldable,autoFoldThreshold:e.autoFoldThreshold},null)])}});pt.install=e=>(e.component(pt.name,pt),e);const nc={onClick:{type:Function,default:void 0}},bt=zt({name:"NormalFooterToolbar",props:nc,emits:["onClick"],setup(e,u){return()=>{const t=li({props:e,ctx:u});return qe("div",{class:`${F}-footer-item`,onClick:n=>{e.onClick instanceof Function?e.onClick(n):u.emit("onClick",n)}},[t])}}});bt.install=e=>(e.component(bt.name,bt),e);export{sc as $,hc as A,dc as B,rt as C,_i as D,mi as E,ci as F,bc as G,lc as H,cc as I,pt as M,fc as O,xi as P,_t as R,ki as T,gi as U,gc as a,O as b,ae as c,mc as d,Nn as e,Fc as f,li as g,Sc as h,vc as i,Cc as j,pc as k,Ec as l,Ac as m,wc as n,Dc as o,F as p,kc as q,ec as r,he as s,bi as t,yc as u,si as v,oc as w,ac as x,jn as y,Hn as z};
