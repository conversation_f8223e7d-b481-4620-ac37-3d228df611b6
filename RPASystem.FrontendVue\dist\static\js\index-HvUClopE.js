import{_ as B,r as p,au as $,e as C,x as c,o as D,c as E,i as u,l as a,j as m,D as f,k as s,av as R,v as S}from"./index-D9f5ARRd.js";const N={class:"uploadData"},T={class:"el-upload__tip text-center"},V={__name:"index",props:{importUrl:{type:String},templateUrl:{type:String},data:{type:Object}},setup(_,{emit:x}){const{proxy:o}=S(),v=x,l=_,g=p("/dev-api"+l.importUrl),U=p({Authorization:"Bearer "+$()}),y=C(()=>l.data),n=p(!1),b=(t,e,r)=>{n.value=!0},k=(t,e,r)=>{const{code:i,msg:d}=t;n.value=!1,o.$refs.uploadRef.clearFiles(),o.$refs.uploadRef.handleRemove(e),i!=200?o.$modal.msgError("导入数据失败,原因："+d):v("success",t)},h=function(t){o.$modal.msgError("导入数据失败,原因："+t)};function F(){o.downFile(l.templateUrl)}return(t,e)=>{const r=c("el-button"),i=c("el-link"),d=c("el-upload");return D(),E("div",N,[u(d,{ref:"uploadRef",limit:1,name:"file",accept:".xlsx,.xls",data:s(y),headers:s(U),action:s(g),disabled:s(n),"on-progress":b,"on-success":k,"on-error":h,"auto-upload":!0},{tip:a(()=>[m("div",T,[e[2]||(e[2]=m("span",null,"仅允许导入xls、xlsx格式文件。",-1)),u(i,{type:"primary",onClick:F,icon:"Bottom"},{default:a(()=>e[1]||(e[1]=[f(" 下载模板 ")])),_:1})])]),default:a(()=>[u(r,{type:"primary",icon:"Upload"},{default:a(()=>e[0]||(e[0]=[f("上传文件")])),_:1})]),_:1},8,["data","headers","action","disabled"]),R(t.$slots,"default",{},void 0,!0)])}}},I=B(V,[["__scopeId","data-v-46df486c"]]);export{I as i};
