import{_ as al,r as i,E as fe,a5 as dl,w as ul,a as rl,x as r,N as he,o as f,c as U,O as b,S as X,k as a,i as t,l as n,z as sl,F as Y,K as Z,p as S,D as m,t as c,m as w,A as ge,j as x,q as ye,s as il,v as ml,n as _e,b3 as pl}from"./index-D9f5ARRd.js";import{l as cl,c as fl,g as hl,u as gl,a as yl,d as _l,b as bl,e as vl}from"./role-CC8G5OXs.js";import{t as kl,r as Vl}from"./dept-XWKXN2sY.js";const Cl={class:"app-container"},Sl={key:0},wl={class:"el-dropdown-link"},$l={class:"custom-tree-node"},Rl=["title"],xl={class:"fr ml10"},Kl=il({name:"role"}),Nl=Object.assign(Kl,{setup(Il){const{proxy:u}=ml(),P=i(!0),T=i([]),be=i(!0),ve=i(!0),D=i(!0),O=i(0),ee=i([]),Q=i(""),v=i(!1),L=i(!0),E=i(!1),M=i(!0),z=i(!1),le=i([]),j=i([]),k=i(!1),te=i([{dictValue:"1",dictLabel:"全部"},{dictValue:"2",dictLabel:"自定义"},{dictValue:"3",dictLabel:"本部门"},{dictValue:"4",dictLabel:"本部门及以下"},{dictValue:"5",dictLabel:"仅本人"}]),A=i([]),K=i([]),h=fe({pageNum:1,pageSize:10,roleName:void 0,roleKey:void 0,status:-1}),F=i(""),ke=fe({form:{},rules:{roleName:[{required:!0,message:"角色名称不能为空",trigger:"blur"}],roleKey:[{required:!0,message:"权限字符不能为空",trigger:"blur"}],roleSort:[{required:!0,message:"角色顺序不能为空",trigger:"blur"}]},defaultProps:{children:"children",label:"label",menuType:re}}),Ve=i(),Ce=i(),Se=i(),{form:d,rules:we,defaultProps:$e}=dl(ke);ul(F,l=>{u.$refs.menuRef.filter(l)});function V(){P.value=!0,cl(u.addDateRange(h,le.value)).then(l=>{ee.value=l.data.result,O.value=l.data.totalNum,P.value=!1})}function Re(){kl().then(l=>{K.value=l.data})}function xe(){const l=u.$refs.menuRef.getCheckedKeys(),e=u.$refs.menuRef.getHalfCheckedKeys();return l.unshift.apply(l,e),l}function oe(){const l=u.$refs.deptRef.getCheckedKeys(),e=u.$refs.deptRef.getHalfCheckedKeys();return l.unshift.apply(l,e),l}function Ke(l){return pl(l).then(e=>(A.value=e.data.menus,e))}function Ne(l){return Vl(l).then(e=>(K.value=e.data.depts,e))}function Ie(l){const e=l.status=="0"?"启用":"停用";u.$confirm('确认要"'+e+'""'+l.roleName+'"角色吗?',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return fl(l.roleId,l.status)}).then(()=>{u.$modal.msgSuccess(e+"成功")}).catch(function(){l.status=l.status==0?1:0})}function N(){v.value=!1,k.value=!1,q()}function q(){u.$refs.menuRef!=null&&u.$refs.menuRef.setCheckedKeys([]),L.value=!1,E.value=!1,M.value=!0,z.value=!1,d.value={roleId:void 0,roleName:void 0,roleKey:void 0,roleSort:99,status:0,menuIds:[],deptIds:[],dataScope:1,menuCheckStrictly:!0,deptCheckStrictly:!0,remark:void 0},u.resetForm("form")}function H(){h.pageNum=1,V()}function Ue(){le.value=[],u.resetForm("queryForm"),H()}function Te(l){T.value=l.map(e=>e.roleId),be.value=l.length!=1,ve.value=!l.length}function De(l,e){switch(l){case"handleDataScope":Fe(e);break;case"handleAuthUser":ue(e);break;case"handleExportMenu":Qe(e)}}function ne(l,e){if(e=="menu"){const p=A.value;for(let s=0;s<p.length;s++)u.$refs.menuRef.store.nodesMap[p[s].id].expanded=l}else if(e=="dept"){const p=K.value;for(let s=0;s<p.length;s++)u.$refs.deptRef.store.nodesMap[p[s].id].expanded=l}}function ae(l,e){e=="menu"?u.$refs.menuRef.setCheckedNodes(l?A.value:[]):e=="dept"&&u.$refs.deptRef.setCheckedNodes(l?K.value:[])}function de(l,e){e=="menu"?d.value.menuCheckStrictly=!!l:e=="dept"&&(d.value.deptCheckStrictly=!!l)}function Le(l,e){return l?e.label.indexOf(l)!==-1||e.permission&&e.permission.indexOf(l)!==-1:!0}function Ee(){q(),Re(),v.value=!0,Q.value="添加角色",k.value=!1}function Me(l){q(),k.value=!1;const e=l.roleId||T.value,p=Ne(l.roleId);hl(e).then(s=>{d.value=s.data,v.value=!0,Q.value="修改角色",_e(()=>{p.then($=>{u.$refs.deptRef.setCheckedKeys($.data.checkedKeys)})})})}function ze(l){l!=="2"&&u.$refs.deptRef.setCheckedKeys([])}function Ae(l,e){return u.selectDictLabel(te.value,l.dataScope)}function Fe(l){if(l.roleId==1){k.value=!1;return}q(),k.value=!0;const e=l.roleId||T.value;Ke(e).then(s=>{s.data.checkedKeys.forEach(B=>{_e(()=>{u.$refs.menuRef.setChecked(B,!0,!1)})})}),d.value={roleId:l.roleId,roleName:l.roleName,roleKey:l.roleKey,menuCheckStrictly:l.menuCheckStrictly}}const qe=rl();function ue(l){const e=l.roleId;var p=u.$auth.hasPermiAnd(["system:role:authorize","system:roleusers:list"]);p?qe.push({path:"/system/roleusers",query:{roleId:e}}):u.$modal.msgError("你没有权限访问")}function Be(){u.$refs.formRef.validate(l=>{l&&(d.value.roleId!=null&&d.value.roleId>0?(d.value.type="edit",d.value.deptIds=oe(),gl(d.value).then(e=>{u.$modal.msgSuccess("修改成功"),v.value=!1,V()})):(d.value.type="add",d.value.deptIds=oe(),yl(d.value).then(e=>{v.value=!1,e.code==200?(u.$modal.msgSuccess("新增成功"),V()):u.$modal.msgError(e.msg)})))})}function Pe(){d.value.roleId!=null?(d.value.menuIds=xe(),_l(d.value).then(l=>{u.$modal.msgSuccess("修改成功"),V(),N()})):u.$modal.msgError("请选择角色")}function Oe(l){const e=l.roleId||T.value;u.$confirm('是否确认删除角色编号为"'+e+'"的数据项?',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return bl(e)}).then(()=>{V(),u.$modal.msgSuccess("删除成功")})}function Qe(l){u.$modal.confirm("是否确认导出所有角色菜单数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await vl({roleId:l.roleId})})}V(),u.getDicts("sys_normal_disable").then(l=>{j.value=l.data});function re(l,e){return l.menuType=="C"?"tree-item-flex":null}return(l,e)=>{const p=r("el-input"),s=r("el-form-item"),$=r("el-option"),B=r("el-select"),_=r("el-button"),G=r("el-form"),C=r("el-col"),je=r("right-toolbar"),se=r("el-row"),g=r("el-table-column"),He=r("el-switch"),Ge=r("el-link"),Je=r("arrow-down"),J=r("el-icon"),W=r("el-dropdown-item"),We=r("el-dropdown-menu"),Xe=r("el-dropdown"),Ye=r("el-table"),Ze=r("pagination"),R=r("el-checkbox"),el=r("el-tag"),ie=r("el-tree"),me=r("zr-dialog"),pe=r("questionFilled"),ce=r("el-tooltip"),ll=r("el-input-number"),tl=r("el-radio"),ol=r("el-radio-group"),I=he("hasPermi"),nl=he("loading");return f(),U("div",Cl,[b(t(G,{model:a(h),ref:"queryForm",inline:!0},{default:n(()=>[t(s,{label:"角色名称",prop:"roleName"},{default:n(()=>[t(p,{modelValue:a(h).roleName,"onUpdate:modelValue":e[0]||(e[0]=o=>a(h).roleName=o),placeholder:"请输入角色名称",clearable:"",onKeyup:sl(H,["enter"])},null,8,["modelValue"])]),_:1}),t(s,{label:"状态",prop:"status"},{default:n(()=>[t(B,{modelValue:a(h).status,"onUpdate:modelValue":e[1]||(e[1]=o=>a(h).status=o),placeholder:"角色状态",clearable:""},{default:n(()=>[t($,{label:"全部",value:-1}),(f(!0),U(Y,null,Z(a(j),o=>(f(),S($,{key:o.dictValue,label:o.dictLabel,value:o.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(s,null,{default:n(()=>[t(_,{type:"primary",icon:"search",onClick:H},{default:n(()=>[m(c(l.$t("btn.search")),1)]),_:1}),t(_,{icon:"refresh",onClick:Ue},{default:n(()=>[m(c(l.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[X,a(D)]]),t(se,{gutter:10,class:"mb8"},{default:n(()=>[t(C,{span:1.5},{default:n(()=>[b((f(),S(_,{type:"primary",plain:"",icon:"plus",onClick:Ee},{default:n(()=>[m(c(l.$t("btn.add")),1)]),_:1})),[[I,["system:role:add"]]])]),_:1}),t(je,{showSearch:a(D),"onUpdate:showSearch":e[2]||(e[2]=o=>w(D)?D.value=o:null),onQueryTable:V},null,8,["showSearch"])]),_:1}),b((f(),S(Ye,{data:a(ee),"highlight-current-row":"",onSelectionChange:Te},{default:n(()=>[t(g,{label:"编号",prop:"roleId",width:"80"}),t(g,{label:"名称",prop:"roleName"}),t(g,{label:"显示顺序",prop:"roleSort"}),t(g,{label:"权限字符",prop:"roleKey"}),t(g,{label:"权限范围",prop:"dataScope",formatter:Ae}),t(g,{label:"状态",width:"90"},{default:n(o=>[t(He,{modelValue:o.row.status,"onUpdate:modelValue":y=>o.row.status=y,disabled:o.row.roleKey=="admin","active-value":0,"inactive-value":1,onChange:y=>Ie(o.row)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"])]),_:1}),t(g,{label:"用户个数",align:"center",prop:"userNum",width:"90"},{default:n(o=>[t(Ge,{type:"primary",onClick:y=>ue(o.row)},{default:n(()=>[m(c(o.row.userNum),1)]),_:2},1032,["onClick"])]),_:1}),t(g,{label:"创建时间",prop:"createTime",width:"150"}),t(g,{label:"备注",align:"center",prop:"remark",width:"150","show-overflow-tooltip":!0}),t(g,{label:"操作",align:"center",width:"200"},{default:n(o=>[o.row.roleKey!="admin"?(f(),U("div",Sl,[b(t(_,{text:"",icon:"edit",title:l.$t("btn.edit"),onClick:ge(y=>Me(o.row),["stop"])},null,8,["title","onClick"]),[[I,["system:role:edit"]]]),b(t(_,{text:"",icon:"delete",title:l.$t("btn.delete"),onClick:ge(y=>Oe(o.row),["stop"])},null,8,["title","onClick"]),[[I,["system:role:remove"]]]),b((f(),S(Xe,{onCommand:y=>De(y,o.row)},{dropdown:n(()=>[t(We,null,{default:n(()=>[t(W,{command:"handleDataScope",icon:"circle-check"},{default:n(()=>[m(c(l.$t("menu.menuPermi")),1)]),_:1}),t(W,{command:"handleAuthUser",icon:"user"},{default:n(()=>[m(c(l.$t("menu.assignUsers")),1)]),_:1}),t(W,{command:"handleExportMenu",icon:"download"},{default:n(()=>e[26]||(e[26]=[m("导出菜单")])),_:1})]),_:1})]),default:n(()=>[x("span",wl,[m(c(l.$t("btn.more"))+" ",1),t(J,{class:"el-icon--right"},{default:n(()=>[t(Je)]),_:1})])]),_:2},1032,["onCommand"])),[[I,["system:role:edit","system:role:authorize","system:roleusers:list"]]])])):ye("",!0)]),_:1})]),_:1},8,["data"])),[[nl,a(P)]]),b(t(Ze,{total:a(O),page:a(h).pageNum,"onUpdate:page":e[3]||(e[3]=o=>a(h).pageNum=o),limit:a(h).pageSize,"onUpdate:limit":e[4]||(e[4]=o=>a(h).pageSize=o),onPagination:V},null,8,["total","page","limit"]),[[X,a(O)>0]]),t(me,{title:"角色权限分配",key:"role",top:"0vh",draggable:"",modelValue:a(k),"onUpdate:modelValue":e[12]||(e[12]=o=>w(k)?k.value=o:null),width:"700px",onClose:N},{footer:n(()=>[t(_,{text:"",onClick:N},{default:n(()=>[m(c(l.$t("btn.cancel")),1)]),_:1}),b((f(),S(_,{type:"primary",onClick:Pe},{default:n(()=>[m(c(l.$t("btn.save")),1)]),_:1})),[[I,["system:role:authorize"]]])]),default:n(()=>[t(G,{model:a(d),"label-width":"80px"},{default:n(()=>[t(s,{label:"菜单搜索"},{default:n(()=>[t(p,{placeholder:"请输入关键字进行过滤",modelValue:a(F),"onUpdate:modelValue":e[5]||(e[5]=o=>w(F)?F.value=o:null)},null,8,["modelValue"])]),_:1}),t(s,{label:"权限字符"},{default:n(()=>[m(c(a(d).roleKey),1)]),_:1}),t(s,{label:"菜单权限"},{default:n(()=>[t(R,{modelValue:a(L),"onUpdate:modelValue":e[6]||(e[6]=o=>w(L)?L.value=o:null),onChange:e[7]||(e[7]=o=>ne(o,"menu"))},{default:n(()=>e[27]||(e[27]=[m("展开/折叠")])),_:1},8,["modelValue"]),t(R,{modelValue:a(E),"onUpdate:modelValue":e[8]||(e[8]=o=>w(E)?E.value=o:null),onChange:e[9]||(e[9]=o=>ae(o,"menu"))},{default:n(()=>e[28]||(e[28]=[m("全选/全不选")])),_:1},8,["modelValue"]),t(R,{modelValue:a(d).menuCheckStrictly,"onUpdate:modelValue":e[10]||(e[10]=o=>a(d).menuCheckStrictly=o),onChange:e[11]||(e[11]=o=>de(o,"menu"))},{default:n(()=>e[29]||(e[29]=[m("父子联动")])),_:1},8,["modelValue"]),t(ie,{class:"tree-border",data:a(A),"show-checkbox":"",ref_key:"menuRef",ref:Ve,"node-key":"id","check-strictly":!a(d).menuCheckStrictly,"empty-text":"加载中，请稍后","highlight-current":"","filter-node-method":Le,props:{children:"children",label:"label",class:re}},{default:n(({node:o,data:y})=>[x("div",$l,[x("span",{class:"fl",title:y.permission},c(o.label),9,Rl),x("span",xl,[y.status==1?(f(),S(el,{key:0,type:"danger"},{default:n(()=>e[30]||(e[30]=[m("停用")])),_:1})):ye("",!0)])])]),_:1},8,["data","check-strictly","props"]),e[31]||(e[31]=x("div",{style:{color:"red"}},"请在菜单管理里面添加对应的菜单权限",-1))]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(me,{title:a(Q),key:"roleEdit",modelValue:a(v),"onUpdate:modelValue":e[25]||(e[25]=o=>w(v)?v.value=o:null),"append-to-body":"",onClose:N},{footer:n(()=>[t(_,{text:"",onClick:N},{default:n(()=>[m(c(l.$t("btn.cancel")),1)]),_:1}),t(_,{type:"primary",onClick:Be},{default:n(()=>[m(c(l.$t("btn.submit")),1)]),_:1})]),default:n(()=>[t(G,{ref_key:"formRef",ref:Se,model:a(d),rules:a(we),"label-width":"90px"},{default:n(()=>[t(se,null,{default:n(()=>[t(C,{lg:12},{default:n(()=>[t(s,{label:"角色名称",prop:"roleName"},{default:n(()=>[t(p,{modelValue:a(d).roleName,"onUpdate:modelValue":e[13]||(e[13]=o=>a(d).roleName=o),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1})]),_:1}),t(C,{lg:12},{default:n(()=>[t(s,{label:"权限字符",prop:"roleKey"},{label:n(()=>[x("span",null,[t(ce,{content:"使用： v-hasRole='['admin']'",placement:"top"},{default:n(()=>[t(J,{size:15},{default:n(()=>[t(pe)]),_:1})]),_:1}),e[32]||(e[32]=m(" 权限字符 "))])]),default:n(()=>[t(p,{modelValue:a(d).roleKey,"onUpdate:modelValue":e[14]||(e[14]=o=>a(d).roleKey=o),placeholder:"请输入权限字符"},null,8,["modelValue"])]),_:1})]),_:1}),t(C,{lg:12},{default:n(()=>[t(s,{label:"角色顺序",prop:"roleSort"},{default:n(()=>[t(ll,{modelValue:a(d).roleSort,"onUpdate:modelValue":e[15]||(e[15]=o=>a(d).roleSort=o),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1})]),_:1}),t(C,{lg:12},{default:n(()=>[t(s,{label:"数据范围"},{default:n(()=>[t(B,{modelValue:a(d).dataScope,"onUpdate:modelValue":e[16]||(e[16]=o=>a(d).dataScope=o),onChange:ze},{default:n(()=>[(f(!0),U(Y,null,Z(a(te),o=>(f(),S($,{key:o.dictValue,label:o.dictLabel,value:parseInt(o.dictValue)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(C,{lg:12},{default:n(()=>[t(s,{label:"状态"},{default:n(()=>[t(ol,{modelValue:a(d).status,"onUpdate:modelValue":e[17]||(e[17]=o=>a(d).status=o)},{default:n(()=>[(f(!0),U(Y,null,Z(a(j),o=>(f(),S(tl,{key:o.dictValue,value:parseInt(o.dictValue)},{default:n(()=>[m(c(o.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(C,{lg:24},{default:n(()=>[b(t(s,{label:"数据权限"},{default:n(()=>[t(R,{modelValue:a(M),"onUpdate:modelValue":e[18]||(e[18]=o=>w(M)?M.value=o:null),onChange:e[19]||(e[19]=o=>ne(o,"dept"))},{default:n(()=>e[33]||(e[33]=[m("展开/折叠")])),_:1},8,["modelValue"]),t(R,{modelValue:a(z),"onUpdate:modelValue":e[20]||(e[20]=o=>w(z)?z.value=o:null),onChange:e[21]||(e[21]=o=>ae(o,"dept"))},{default:n(()=>e[34]||(e[34]=[m("全选/全不选")])),_:1},8,["modelValue"]),t(R,{modelValue:a(d).deptCheckStrictly,"onUpdate:modelValue":e[22]||(e[22]=o=>a(d).deptCheckStrictly=o),onChange:e[23]||(e[23]=o=>de(o,"dept"))},{default:n(()=>[e[35]||(e[35]=m(" 父子联动 ")),t(ce,{content:"勾选父节点是否同时选中子节点",placement:"top"},{default:n(()=>[t(J,{size:15},{default:n(()=>[t(pe)]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(ie,{class:"tree-border",data:a(K),"show-checkbox":"","default-expand-all":"",ref_key:"deptRef",ref:Ce,"node-key":"id","check-strictly":!a(d).deptCheckStrictly,"empty-text":"加载中，请稍候",props:a($e)},null,8,["data","check-strictly","props"])]),_:1},512),[[X,a(d).dataScope==2]])]),_:1}),t(C,{lg:24},{default:n(()=>[t(s,{label:"备注"},{default:n(()=>[t(p,{modelValue:a(d).remark,"onUpdate:modelValue":e[24]||(e[24]=o=>a(d).remark=o),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),Ll=al(Nl,[["__scopeId","data-v-500cba52"]]);export{Ll as default};
