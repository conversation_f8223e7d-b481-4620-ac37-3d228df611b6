import{ai as V,r as u,E as Q,u as ie,x as i,N as G,o as y,c as de,i as e,l as n,k as o,z as L,D as _,t as d,A as M,O as w,p as C,q as me,m as F,j as ce,s as pe,v as fe}from"./index-D9f5ARRd.js";import{g as ge}from"./role-CC8G5OXs.js";function be(c){return V({url:"/system/userRole/list",method:"get",params:c})}function ve(c){return V({url:"/system/userRole/create",method:"post",data:c})}function J(c){return V({url:"/system/userRole/delete",method:"post",data:c})}function _e(c){return V({url:"/system/userRole/getExcludeUsers",method:"get",params:c})}const ye={class:"app-container"},he={class:"dialog-footer"},Ne=pe({name:"roleUsers"}),Ie=Object.assign(Ne,{setup(c){const $=u(!1),S=u(!1),W=u(window.innerHeight),B=u([]),I=u(0),E=u([]),D=u(0),R=u([]),f=u([]),g=u(!1),X=u(),s=Q({pageNum:1,pageSize:10,roleId:void 0,userName:void 0,roleName:void 0,roleKey:void 0}),m=Q({pageNum:1,pageSize:10,roleId:void 0,userName:void 0}),z=u([]),{proxy:p}=fe(),Y=ie();p.getDicts("sys_normal_disable").then(l=>{z.value=l.data});const h=Y.query.roleId;s.roleId=h,m.roleId=h;function Z(){K(),ge(s.roleId).then(l=>{const{code:t,data:b}=l;t==200&&(s.roleName=b.roleName,s.roleKey=b.roleKey)})}function K(){s.pageNum=1,N()}function N(){S.value=!0,be(s).then(l=>{B.value=l.data.result,I.value=l.data.totalNum,S.value=!1})}function ee(){const l={path:"/system/role"};p.$tab.closeOpenPage(l)}function te(l){f.value=l.map(t=>t.userId)}function le(){if(f.value.length===0){p.$modal.msgError("请选择要删除的用户");return}p.$confirm("是否确认删除选中的 "+f.value.length+" 条数据?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{J({roleId:h,userIds:f.value}).then(l=>{l.code===200&&(p.$message({message:"成功删除"+l.data+"条数据",type:"success"}),N())})}).catch(()=>{})}function ae(l){f.value=[],f.value.push(l.userId),J({roleId:h,userIds:f.value}).then(t=>{t.code===200&&(p.$message({message:"成功删除"+t.data+"条数据",type:"success"}),N())})}function ne(l){R.value=l.map(t=>t.userId)}function oe(){m.pageNum=1,P()}function P(){g.value=!0,$.value=!0,_e(m).then(l=>{E.value=l.data.result,D.value=l.data.totalNum,$.value=!1})}function se(){if(R.value.length<=0){p.$modal.msgError("请选择要添加的用户");return}ve({roleId:h,userIds:R.value}).then(l=>{l.code===200&&(p.$message({message:"成功添加"+l.data+"条数据",type:"success"}),N(),g.value=!1)})}function re(){g.value=!1}return Z(),(l,t)=>{const b=i("el-input"),U=i("el-form-item"),v=i("el-button"),x=i("el-form"),k=i("el-col"),O=i("el-row"),r=i("el-table-column"),j=i("dict-tag"),A=i("el-table"),q=i("pagination"),ue=i("el-dialog"),T=G("hasPermi"),H=G("loading");return y(),de("div",ye,[e(x,{inline:!0,onSubmit:t[3]||(t[3]=M(()=>{},["prevent"]))},{default:n(()=>[e(U,{label:"角色名"},{default:n(()=>[e(b,{modelValue:o(s).roleName,"onUpdate:modelValue":t[0]||(t[0]=a=>o(s).roleName=a),disabled:""},null,8,["modelValue"])]),_:1}),e(U,{label:"角色字符串"},{default:n(()=>[e(b,{modelValue:o(s).roleKey,"onUpdate:modelValue":t[1]||(t[1]=a=>o(s).roleKey=a),disabled:""},null,8,["modelValue"])]),_:1}),e(U,{label:"用户名"},{default:n(()=>[e(b,{modelValue:o(s).userName,"onUpdate:modelValue":t[2]||(t[2]=a=>o(s).userName=a),placeholder:"请输入用户名称",clearable:"","prefix-icon":"search",onKeyup:L(K,["enter"])},null,8,["modelValue"])]),_:1}),e(U,null,{default:n(()=>[e(v,{type:"primary",icon:"search",onClick:K},{default:n(()=>[_(d(l.$t("btn.search")),1)]),_:1})]),_:1})]),_:1}),e(O,{gutter:10,class:"mb8"},{default:n(()=>[e(k,{span:1.5},{default:n(()=>[w((y(),C(v,{type:"primary",plain:"",icon:"plus",onClick:P},{default:n(()=>[_(d(l.$t("btn.add")),1)]),_:1})),[[T,["system:roleusers:add"]]])]),_:1}),e(k,{span:1.5},{default:n(()=>[w((y(),C(v,{type:"danger",plain:"",icon:"circle-close",onClick:le},{default:n(()=>[_(d(l.$t("btn.multi"))+d(l.$t("btn.cancel"))+d(l.$t("btn.authorize")),1)]),_:1})),[[T,["system:roleusers:remove"]]])]),_:1}),e(k,{span:1.5},{default:n(()=>[e(v,{type:"warning",plain:"",icon:"close",onClick:ee},{default:n(()=>[_(d(l.$t("btn.close")),1)]),_:1})]),_:1})]),_:1}),w((y(),C(A,{ref_key:"roleUserTableRef",ref:X,data:o(B),onSelectionChange:te,"row-key":"userId",stripe:"",border:""},{default:n(()=>[e(r,{type:"selection",width:"55",align:"center"}),e(r,{prop:"userId",align:"center",label:"用户Id",width:"150"}),e(r,{prop:"userName",align:"center",label:"用户名",width:"150"}),e(r,{prop:"nickName",align:"center",label:"用户昵称",width:"150"}),e(r,{prop:"status",align:"center",label:"账号状态",width:"110"},{default:n(a=>[e(j,{options:o(z),value:a.row.status},null,8,["options","value"])]),_:1}),e(r,{prop:"remark","show-overflow-tooltip":!0,align:"center",label:"备注"}),e(r,{align:"center",label:"操作"},{default:n(a=>[a.row.userId!=1?w((y(),C(v,{key:0,text:"",size:"small",icon:"el-icon-circle-close",onClick:Ue=>ae(a.row)},{default:n(()=>[_(d(l.$t("btn.cancel"))+d(l.$t("btn.authorize")),1)]),_:2},1032,["onClick"])),[[T,["system:roleusers:del"]]]):me("",!0)]),_:1})]),_:1},8,["data"])),[[H,o(S)]]),e(q,{total:o(I),"onUpdate:total":t[4]||(t[4]=a=>F(I)?I.value=a:null),page:o(s).pageNum,"onUpdate:page":t[5]||(t[5]=a=>o(s).pageNum=a),limit:o(s).pageSize,"onUpdate:limit":t[6]||(t[6]=a=>o(s).pageSize=a),onPagination:N},null,8,["total","page","limit"]),e(ue,{title:"添加用户",modelValue:o(g),"onUpdate:modelValue":t[12]||(t[12]=a=>F(g)?g.value=a:null),"append-to-body":"",onClose:re},{footer:n(()=>[ce("div",he,[e(v,{text:"",onClick:t[11]||(t[11]=a=>g.value=!1)},{default:n(()=>[_(d(l.$t("btn.cancel")),1)]),_:1}),e(v,{type:"primary",onClick:se},{default:n(()=>[_(d(l.$t("btn.submit")),1)]),_:1})])]),default:n(()=>[e(x,{inline:!0,onSubmit:t[8]||(t[8]=M(()=>{},["prevent"]))},{default:n(()=>[e(U,null,{default:n(()=>[e(b,{modelValue:o(m).userName,"onUpdate:modelValue":t[7]||(t[7]=a=>o(m).userName=a),placeholder:"请输入用户名称",clearable:"","prefix-icon":"search",onKeyup:L(oe,["enter"])},null,8,["modelValue"])]),_:1})]),_:1}),e(O,null,{default:n(()=>[e(k,null,{default:n(()=>[w((y(),C(A,{ref:"userTable",data:o(E),onSelectionChange:ne,"row-key":"userId",stripe:"",border:"",height:o(W)*.5},{default:n(()=>[e(r,{type:"selection",width:"55",align:"center"}),e(r,{prop:"userId",align:"center",label:"用户编号",width:"150"}),e(r,{prop:"userName",align:"center",label:"用户名称",width:"150"}),e(r,{prop:"nickName",align:"center",label:"用户昵称",width:"150"}),e(r,{prop:"status",align:"center",label:"用户状态"},{default:n(a=>[e(j,{options:o(z),value:a.row.status},null,8,["options","value"])]),_:1})]),_:1},8,["data","height"])),[[H,o($)]]),e(q,{total:o(D),page:o(m).pageNum,"onUpdate:page":t[9]||(t[9]=a=>o(m).pageNum=a),limit:o(m).pageSize,"onUpdate:limit":t[10]||(t[10]=a=>o(m).pageSize=a),onPagination:P},null,8,["total","page","limit"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}});export{Ie as default};
