import{_ as He,r as m,E as de,au as Ge,a5 as Je,w as We,x as d,N as ie,o as u,c as S,i as t,l as a,j as x,k as l,m as O,p as r,q as g,D as v,t as _,O as I,z as pe,F as P,K as F,S as Xe,s as Ye,v as Ze}from"./index-D9f5ARRd.js";import{t as me}from"./dept-XWKXN2sY.js";import{l as el,d as ll,e as tl,c as al,r as ol,f as ce,h as nl,i as sl}from"./user-DAowxja8.js";const ul={class:"app-container"},rl={class:"head-container"},dl={class:"head-container"},il={class:"custom-tree-node"},pl={style:{float:"left"}},ml={style:{float:"right"}},cl={class:"el-upload__tip text-center"},fl=Ye({name:"user"}),bl=Object.assign(fl,{setup(gl){const{proxy:i}=Ze(),K=m([]),z=m([]);i.getDicts("sys_normal_disable").then(n=>{K.value=n.data}),i.getDicts("sys_user_sex").then(n=>{z.value=n.data});const W=m([]),N=m(!1),E=m(!0),q=m(!0),A=m([]),X=m(!0),Y=m(!0),Z=m(0),M=m(""),T=m([]),B=m(""),R=m([]),ee=m(void 0),Q=m([]),j=m([]),k=de({open:!1,title:"",isUploading:!1,updateSupport:0,headers:{Authorization:"Bearer "+Ge()},url:"/dev-api/system/user/importData"}),V=m([{key:0,label:"用户编号",visible:!0,prop:"userId"},{key:1,label:"用户名称",visible:!0,prop:"userName"},{key:2,label:"用户昵称",visible:!0,prop:"nickName"},{key:3,label:"部门",visible:!0,prop:"deptName"},{key:4,label:"手机号码",visible:!0,prop:"phonenumber"},{key:5,label:"状态",visible:!0,prop:"status"},{key:6,label:"创建时间",visible:!1,prop:"createTime"},{key:7,label:"性别",visible:!0,prop:"sex"},{key:8,label:"头像",visible:!0,prop:"avatar"},{key:9,label:"邮箱",visible:!1,prop:"email"},{key:10,label:"最后登录时间",visible:!1,prop:"loginDate"}]),fe=de({form:{},queryParams:{pageNum:1,pageSize:10,userName:void 0,phonenumber:void 0,status:-1,deptId:void 0},rules:{userName:[{required:!0,message:"用户名称不能为空",trigger:"blur"},{min:2,max:20,message:"用户名称长度必须介于 2 和 20 之间",trigger:"blur"}],nickName:[{required:!0,message:"用户昵称不能为空",trigger:"blur"}],password:[{required:!0,message:"用户密码不能为空",trigger:"blur"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"}],email:[{required:!0,type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],phonenumber:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}),{queryParams:c,form:s,rules:be}=Je(fe);i.getConfigKey("sys.user.initPassword").then(n=>{ee.value=n.data});const ge=(n,o)=>n?o.label.indexOf(n)!==-1:!0;We(B,n=>{i.$refs.deptTreeRef.filter(n)});function ve(){me().then(n=>{R.value=[{id:0,label:"未知部门",children:[]},...n.data]})}function C(){E.value=!0,el(i.addDateRange(c.value,T.value)).then(n=>{E.value=!1,W.value=n.data.result,Z.value=n.data.totalNum})}function _e(n){c.value.deptId=n.id,D()}function D(){c.value.pageNum=1,C()}function he(){T.value=[],i.resetForm("queryRef"),c.value.deptId=void 0,i.$refs.deptTreeRef.setCurrentKey(null),D()}function le(n){const o=n.userId||A.value;i.$modal.confirm('是否确认删除用户编号为"'+o+'"的数据项？').then(function(){return ll(o)}).then(()=>{C(),i.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ye(){i.$modal.confirm("是否确认导出所有用户数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await tl(c.value)})}function ke(n){let o=n.status==="0"?"启用":"停用";i.$modal.confirm('确认要"'+o+'""'+n.userName+'"用户吗?').then(function(){return al(n.userId,n.status)}).then(()=>{i.$modal.msgSuccess(o+"成功")}).catch(function(){n.status=n.status==1?0:1})}function we(n){i.$prompt('请输入"'+n.userName+'"的新密码',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,inputPattern:/^.{5,20}$/,inputErrorMessage:"用户密码长度必须介于 5 和 20 之间"}).then(({value:o})=>{ol(n.userId,o).then(f=>{i.$modal.msgSuccess("修改成功，新密码是："+o)})}).catch(()=>{})}function Ve(n){A.value=n.map(o=>o.userId),X.value=n.length!=1,Y.value=!n.length}function xe(){k.title="用户导入",k.open=!0}function Ie(){i.download("/system/user/importTemplate","用户数据导入模板")}const Ne=(n,o,f)=>{k.isUploading=!0},Ce=(n,o,f)=>{const{code:h,msg:oe,data:p}=n;k.open=!1,k.isUploading=!1,i.$refs.uploadRef.clearFiles(),i.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+p.item1+"</div>","导入结果",{dangerouslyUseHTMLString:!0}),C()};function Ue(){i.$refs.uploadRef.submit()}function te(){R.value===void 0&&me().then(n=>{R.value=n.data})}function H(){s.value={userId:void 0,deptId:void 0,userName:void 0,nickName:void 0,password:void 0,phonenumber:void 0,email:void 0,sex:2,status:0,remark:void 0,postIds:[],roleIds:[]},i.resetForm("userRef")}function $e(){N.value=!1,H()}function Se(){H(),te(),ce().then(n=>{Q.value=n.data.posts,j.value=n.data.roles,N.value=!0,M.value="添加用户",s.value.password=ee.value})}function ae(n){H(),te();const o=n.userId||A.value;ce(o).then(f=>{var h=f.data;s.value={userId:h.user.userId,deptId:h.user.deptId,userName:h.user.userName,nickName:h.user.nickName,password:"",phonenumber:h.user.phonenumber,email:h.user.email,sex:h.user.sex,status:h.user.status,remark:h.user.remark,postIds:h.postIds,roleIds:h.roleIds},j.value=f.data.roles,Q.value=f.data.posts,N.value=!0,M.value="修改用户",s.password=""})}function Te(){i.$refs.userRef.validate(n=>{n&&(s.value.userId!=null?nl(s.value).then(o=>{i.$modal.msgSuccess("修改成功"),N.value=!1,C()}):sl(s.value).then(o=>{i.$modal.msgSuccess("新增成功"),N.value=!1,C()}))})}function Re(n){return n.userId!=1}function De(n){i.$forceUpdate()}return ve(),C(),(n,o)=>{const f=d("el-input"),h=d("svg-icon"),oe=d("el-tree"),p=d("el-col"),b=d("el-form-item"),L=d("el-option"),G=d("el-select"),Pe=d("el-date-picker"),y=d("el-button"),ne=d("el-form"),Fe=d("right-toolbar"),J=d("el-row"),w=d("el-table-column"),qe=d("el-switch"),Be=d("dict-tag"),Le=d("el-avatar"),Oe=d("el-table"),Ke=d("pagination"),ze=d("el-tree-select"),se=d("el-radio"),ue=d("el-radio-group"),re=d("el-dialog"),Ee=d("upload-filled"),Ae=d("el-icon"),Me=d("el-link"),Qe=d("el-upload"),U=ie("hasPermi"),je=ie("loading");return u(),S("div",ul,[t(J,{gutter:20},{default:a(()=>[t(p,{span:4,xs:24},{default:a(()=>[x("div",rl,[t(f,{modelValue:l(B),"onUpdate:modelValue":o[0]||(o[0]=e=>O(B)?B.value=e:null),placeholder:"请输入部门名称",clearable:"","prefix-icon":"search",style:{"margin-bottom":"20px"}},null,8,["modelValue"])]),x("div",dl,[t(oe,{data:l(R),props:{label:"label",children:"children"},"expand-on-click-node":!1,"filter-node-method":ge,ref:"deptTreeRef","node-key":"id","highlight-current":"","default-expand-all":"",onNodeClick:_e},{default:a(({node:e,data:$})=>[x("span",il,[x("span",null,[$.children&&$.children.length>0?(u(),r(h,{key:0,name:"index"})):g("",!0),v(" "+_(e.label),1)])])]),_:1},8,["data"])])]),_:1}),t(p,{lg:20,xm:24},{default:a(()=>[I(t(ne,{model:l(c),ref:"queryRef",inline:!0,"label-width":"68px"},{default:a(()=>[t(b,{label:"用户名称",prop:"userName"},{default:a(()=>[t(f,{modelValue:l(c).userName,"onUpdate:modelValue":o[1]||(o[1]=e=>l(c).userName=e),placeholder:"请输入用户名称",clearable:"",style:{width:"240px"},onKeyup:pe(D,["enter"])},null,8,["modelValue"])]),_:1}),t(b,{label:"手机号码",prop:"phonenumber"},{default:a(()=>[t(f,{modelValue:l(c).phonenumber,"onUpdate:modelValue":o[2]||(o[2]=e=>l(c).phonenumber=e),placeholder:"请输入手机号码",clearable:"",style:{width:"240px"},onKeyup:pe(D,["enter"])},null,8,["modelValue"])]),_:1}),t(b,{label:"状态",prop:"status"},{default:a(()=>[t(G,{modelValue:l(c).status,"onUpdate:modelValue":o[3]||(o[3]=e=>l(c).status=e),placeholder:"用户状态",clearable:"",style:{width:"240px"}},{default:a(()=>[t(L,{label:"全部",value:-1}),(u(!0),S(P,null,F(l(K),e=>(u(),r(L,{key:e.dictValue,label:e.dictLabel,value:e.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(b,{label:"创建时间"},{default:a(()=>[t(Pe,{modelValue:l(T),"onUpdate:modelValue":o[4]||(o[4]=e=>O(T)?T.value=e:null),style:{width:"240px"},type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),t(b,null,{default:a(()=>[t(y,{type:"primary",icon:"search",onClick:D},{default:a(()=>[v(_(n.$t("btn.search")),1)]),_:1}),t(y,{icon:"refresh",onClick:he},{default:a(()=>[v(_(n.$t("btn.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),[[Xe,l(q)]]),t(J,{gutter:10,class:"mb8"},{default:a(()=>[t(p,{span:1.5},{default:a(()=>[I((u(),r(y,{type:"primary",plain:"",icon:"Plus",onClick:Se},{default:a(()=>[v(_(n.$t("btn.add")),1)]),_:1})),[[U,["system:user:add"]]])]),_:1}),t(p,{span:1.5},{default:a(()=>[I((u(),r(y,{type:"success",plain:"",icon:"Edit",disabled:l(X),onClick:ae},{default:a(()=>[v(_(n.$t("btn.edit")),1)]),_:1},8,["disabled"])),[[U,["system:user:edit"]]])]),_:1}),t(p,{span:1.5},{default:a(()=>[I((u(),r(y,{type:"danger",plain:"",icon:"Delete",disabled:l(Y),onClick:le},{default:a(()=>[v(_(n.$t("btn.delete")),1)]),_:1},8,["disabled"])),[[U,["system:user:remove"]]])]),_:1}),t(p,{span:1.5},{default:a(()=>[I((u(),r(y,{type:"info",plain:"",icon:"Upload",onClick:xe},{default:a(()=>[v(_(n.$t("btn.import")),1)]),_:1})),[[U,["system:user:import"]]])]),_:1}),t(p,{span:1.5},{default:a(()=>[I((u(),r(y,{type:"warning",plain:"",icon:"Download",onClick:ye},{default:a(()=>[v(_(n.$t("btn.export")),1)]),_:1})),[[U,["system:user:export"]]])]),_:1}),t(Fe,{showSearch:l(q),"onUpdate:showSearch":o[5]||(o[5]=e=>O(q)?q.value=e:null),onQueryTable:C,columns:l(V)},null,8,["showSearch","columns"])]),_:1}),I((u(),r(Oe,{data:l(W),onSelectionChange:Ve},{default:a(()=>[t(w,{type:"selection",width:"50",align:"center",selectable:Re}),l(V).showColumn("userId")?(u(),r(w,{label:"用户编号",align:"center",key:"userId",prop:"userId"})):g("",!0),l(V).showColumn("userName")?(u(),r(w,{label:"登录名",align:"center",key:"userName",prop:"userName","show-overflow-tooltip":!0})):g("",!0),l(V).showColumn("nickName")?(u(),r(w,{label:"用户昵称",align:"center",key:"nickName",prop:"nickName","show-overflow-tooltip":!0})):g("",!0),l(V).showColumn("deptName")?(u(),r(w,{label:"部门",align:"center",key:"deptName",prop:"deptName","show-overflow-tooltip":!0})):g("",!0),l(V).showColumn("phonenumber")?(u(),r(w,{label:"手机号码",align:"center",key:"phonenumber",prop:"phonenumber",width:"120"})):g("",!0),l(V).showColumn("status")?(u(),r(w,{label:"启用",align:"center",key:"status"},{default:a(e=>[t(qe,{modelValue:e.row.status,"onUpdate:modelValue":$=>e.row.status=$,"active-value":0,"inactive-value":1,"active-text":"是","inactive-text":"否","inline-prompt":"",onChange:$=>ke(e.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1})):g("",!0),l(V).showColumn("createTime")?(u(),r(w,{key:6,label:"创建时间",align:"center",prop:"createTime",width:"160"})):g("",!0),l(V).showColumn("sex")?(u(),r(w,{key:7,prop:"sex",label:"性别",align:"center"},{default:a(e=>[t(Be,{options:l(z),value:e.row.sex},null,8,["options","value"])]),_:1})):g("",!0),l(V).showColumn("avatar")?(u(),r(w,{key:8,prop:"avatar",label:"头像",align:"center"},{default:a(e=>[t(Le,{src:e.row.avatar},null,8,["src"])]),_:1})):g("",!0),l(V).showColumn("email")?(u(),r(w,{key:9,prop:"email",label:"用户邮箱",align:"center"})):g("",!0),l(V).showColumn("loginDate")?(u(),r(w,{key:10,prop:"loginDate",label:"最后登录时间",align:"center"})):g("",!0),t(w,{label:"操作",align:"left",width:"110","class-name":"small-padding fixed-width"},{default:a(e=>[e.row.userId!==1?I((u(),r(y,{key:0,text:"",icon:"Edit",onClick:$=>ae(e.row)},null,8,["onClick"])),[[U,["system:user:edit"]]]):g("",!0),e.row.isAdmin?g("",!0):I((u(),r(y,{key:1,text:"",icon:"Delete",onClick:$=>le(e.row)},null,8,["onClick"])),[[U,["system:user:remove"]]]),e.row.userId!==1?I((u(),r(y,{key:2,text:"",icon:"Key",title:"重置密码",onClick:$=>we(e.row)},null,8,["onClick"])),[[U,["system:user:resetPwd"]]]):g("",!0)]),_:1})]),_:1},8,["data"])),[[je,l(E)]]),t(Ke,{total:l(Z),page:l(c).pageNum,"onUpdate:page":o[6]||(o[6]=e=>l(c).pageNum=e),limit:l(c).pageSize,"onUpdate:limit":o[7]||(o[7]=e=>l(c).pageSize=e),onPagination:C},null,8,["total","page","limit"])]),_:1})]),_:1}),t(re,{title:l(M),modelValue:l(N),"onUpdate:modelValue":o[20]||(o[20]=e=>O(N)?N.value=e:null),width:"600px","append-to-body":""},{footer:a(()=>[t(y,{text:"",onClick:$e},{default:a(()=>[v(_(n.$t("btn.cancel")),1)]),_:1}),t(y,{type:"primary",onClick:Te},{default:a(()=>[v(_(n.$t("btn.submit")),1)]),_:1})]),default:a(()=>[t(ne,{model:l(s),rules:l(be),ref:"userRef","label-width":"80px"},{default:a(()=>[t(J,{gutter:20},{default:a(()=>[t(p,{lg:12},{default:a(()=>[t(b,{label:"用户名",prop:"userName"},{default:a(()=>[t(f,{disabled:l(s).userId!=null,modelValue:l(s).userName,"onUpdate:modelValue":o[8]||(o[8]=e=>l(s).userName=e),placeholder:"请输入用户名(用于登陆)"},null,8,["disabled","modelValue"])]),_:1})]),_:1}),l(s).userId==null?(u(),r(p,{key:0,lg:12},{default:a(()=>[t(b,{label:"用户密码",prop:"password"},{default:a(()=>[t(f,{modelValue:l(s).password,"onUpdate:modelValue":o[9]||(o[9]=e=>l(s).password=e),"show-password":"",placeholder:"请输入用户密码",type:"password"},null,8,["modelValue"])]),_:1})]),_:1})):g("",!0),t(p,{lg:12},{default:a(()=>[t(b,{label:"用户昵称",prop:"nickName"},{default:a(()=>[t(f,{modelValue:l(s).nickName,"onUpdate:modelValue":o[10]||(o[10]=e=>l(s).nickName=e),placeholder:"请输入用户昵称"},null,8,["modelValue"])]),_:1})]),_:1}),t(p,{lg:12},{default:a(()=>[t(b,{label:"归属部门",prop:"deptId"},{default:a(()=>[t(ze,{modelValue:l(s).deptId,"onUpdate:modelValue":o[11]||(o[11]=e=>l(s).deptId=e),data:l(R),props:{value:"id",label:"label",children:"children"},"value-key":"id",placeholder:"请选择归属部门","check-strictly":"","render-after-expand":!1},null,8,["modelValue","data"])]),_:1})]),_:1}),t(p,{lg:12},{default:a(()=>[t(b,{label:"手机号码",prop:"phonenumber"},{default:a(()=>[t(f,{modelValue:l(s).phonenumber,"onUpdate:modelValue":o[12]||(o[12]=e=>l(s).phonenumber=e),placeholder:"请输入手机号码",maxlength:"11"},null,8,["modelValue"])]),_:1})]),_:1}),t(p,{lg:12},{default:a(()=>[t(b,{label:"邮箱",prop:"email"},{default:a(()=>[t(f,{modelValue:l(s).email,"onUpdate:modelValue":o[13]||(o[13]=e=>l(s).email=e),placeholder:"请输入邮箱",maxlength:"50"},null,8,["modelValue"])]),_:1})]),_:1}),t(p,{lg:12},{default:a(()=>[t(b,{label:"用户性别"},{default:a(()=>[t(ue,{modelValue:l(s).sex,"onUpdate:modelValue":o[14]||(o[14]=e=>l(s).sex=e),placeholder:"请选择用户性别"},{default:a(()=>[(u(!0),S(P,null,F(l(z),e=>(u(),r(se,{key:e.dictValue,value:parseInt(e.dictValue)},{default:a(()=>[v(_(e.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(p,{lg:12},{default:a(()=>[t(b,{label:"用户状态"},{default:a(()=>[t(ue,{modelValue:l(s).status,"onUpdate:modelValue":o[15]||(o[15]=e=>l(s).status=e)},{default:a(()=>[(u(!0),S(P,null,F(l(K),e=>(u(),r(se,{key:e.dictValue,value:parseInt(e.dictValue)},{default:a(()=>[v(_(e.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(p,{lg:24},{default:a(()=>[t(b,{label:"岗位"},{default:a(()=>[t(G,{modelValue:l(s).postIds,"onUpdate:modelValue":o[16]||(o[16]=e=>l(s).postIds=e),multiple:"",placeholder:"请选择岗位",style:{width:"100%"}},{default:a(()=>[(u(!0),S(P,null,F(l(Q),e=>(u(),r(L,{key:e.postId,label:e.postName,value:e.postId,disabled:e.status==1},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(p,{lg:24},{default:a(()=>[t(b,{label:"角色"},{default:a(()=>[t(G,{modelValue:l(s).roleIds,"onUpdate:modelValue":o[17]||(o[17]=e=>l(s).roleIds=e),multiple:"",placeholder:"请选择角色",style:{width:"100%"},onChange:o[18]||(o[18]=e=>De(e))},{default:a(()=>[(u(!0),S(P,null,F(l(j),e=>(u(),r(L,{key:e.roleId,label:e.roleName,value:e.roleId,disabled:e.status==1||l(s).userId==1},{default:a(()=>[x("span",pl,_(e.roleName),1),x("span",ml,_(e.roleKey),1)]),_:2},1032,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(p,{lg:24},{default:a(()=>[t(b,{label:"备注"},{default:a(()=>[t(f,{modelValue:l(s).remark,"onUpdate:modelValue":o[19]||(o[19]=e=>l(s).remark=e),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),t(re,{title:l(k).title,modelValue:l(k).open,"onUpdate:modelValue":o[22]||(o[22]=e=>l(k).open=e),width:"400px","append-to-body":""},{footer:a(()=>[t(y,{onClick:o[21]||(o[21]=e=>l(k).open=!1)},{default:a(()=>[v(_(n.$t("btn.cancel")),1)]),_:1}),t(y,{type:"primary",onClick:Ue},{default:a(()=>[v(_(n.$t("btn.submit")),1)]),_:1})]),default:a(()=>[t(Qe,{name:"file",ref:"uploadRef",limit:1,accept:".xlsx,.xls",headers:l(k).headers,action:l(k).url+"?updateSupport="+l(k).updateSupport,disabled:l(k).isUploading,"on-progress":Ne,"on-success":Ce,"auto-upload":!1,drag:""},{tip:a(()=>[x("div",cl,[o[24]||(o[24]=x("span",null,"仅允许导入xls、xlsx格式文件。",-1)),t(Me,{type:"primary",underline:!1,style:{"font-size":"12px","vertical-align":"baseline"},onClick:Ie},{default:a(()=>o[23]||(o[23]=[v("下载模板")])),_:1})])]),default:a(()=>[t(Ae,{class:"el-icon--upload"},{default:a(()=>[t(Ee)]),_:1}),o[25]||(o[25]=x("div",{class:"el-upload__text"},[v("将文件拖到此处，或"),x("em",null,"点击上传")],-1))]),_:1},8,["headers","action","disabled"])]),_:1},8,["title","modelValue"])])}}}),yl=He(bl,[["__scopeId","data-v-b25f25b2"]]);export{yl as default};
