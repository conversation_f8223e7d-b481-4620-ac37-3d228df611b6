import{r as d,E as X,x as i,N as U,o as p,c as F,O as f,S as I,k as l,i as e,l as o,z,F as Y,K as Z,p as v,m as ee,D as g,j as N,t as S,a8 as te,s as ne,v as le}from"./index-D9f5ARRd.js";import{l as oe,d as ae,c as ie,e as re}from"./logininfor-BB_yx8dN.js";const se={class:"app-container"},ue=ne({name:"logininfor"}),fe=Object.assign(ue,{setup(de){const y=d(!0),T=d([]),B=d(!0),D=d(!0),w=d(0),x=d([]),V=d([]),_=d([]),a=X({pageNum:1,pageSize:10,ipaddr:void 0,userName:void 0,status:void 0}),{proxy:u}=le();function c(){y.value=!0,oe(u.addDateRange(a,_.value)).then(r=>{y.value=!1,r.code==200?(x.value=r.data.result,w.value=r.data.totalNum):(w.value=0,x.value=[])})}c(),u.getDicts("sys_common_status").then(r=>{V.value=r.data});function h(){a.pageNum=1,c()}function K(){_.value=[],u.resetForm("queryForm"),h()}function P(r){T.value=r.map(t=>t.infoId),B.value=!r.length}function L(r){const t=r.infoId||T.value;u.$confirm('是否确认删除访问编号为"'+t+'"的数据项?',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return ae(t)}).then(()=>{c(),u.$modal.msgSuccess("删除成功")})}function q(){u.$confirm("是否确认清空所有登录日志数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return ie()}).then(()=>{c(),u.$modal.msgSuccess("清空成功")})}function E(){u.$confirm("是否确认导出所有操作日志数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return re(a)}).then(r=>{u.download(r.data.path)})}return(r,t)=>{const $=i("el-input"),b=i("el-form-item"),O=i("el-option"),Q=i("el-select"),R=i("el-date-picker"),m=i("el-button"),j=i("el-form"),k=i("el-col"),A=i("right-toolbar"),G=i("el-row"),s=i("el-table-column"),H=i("dict-tag"),J=i("el-table"),M=i("pagination"),C=U("hasPermi"),W=U("loading");return p(),F("div",se,[f(e(j,{model:l(a),ref:"queryForm",inline:!0,"label-width":"68px"},{default:o(()=>[e(b,{label:"登录地址",prop:"ipaddr"},{default:o(()=>[e($,{modelValue:l(a).ipaddr,"onUpdate:modelValue":t[0]||(t[0]=n=>l(a).ipaddr=n),placeholder:"请输入登录地址",clearable:"",onKeyup:z(h,["enter"])},null,8,["modelValue"])]),_:1}),e(b,{label:"用户名称",prop:"userName"},{default:o(()=>[e($,{modelValue:l(a).userName,"onUpdate:modelValue":t[1]||(t[1]=n=>l(a).userName=n),placeholder:"请输入用户名称",clearable:"",onKeyup:z(h,["enter"])},null,8,["modelValue"])]),_:1}),e(b,{label:"状态",prop:"status"},{default:o(()=>[e(Q,{modelValue:l(a).status,"onUpdate:modelValue":t[2]||(t[2]=n=>l(a).status=n),placeholder:"登录状态",clearable:""},{default:o(()=>[(p(!0),F(Y,null,Z(l(V),n=>(p(),v(O,{key:n.dictValue,label:n.dictLabel,value:n.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(b,{label:"登录时间"},{default:o(()=>[e(R,{modelValue:l(_),"onUpdate:modelValue":t[3]||(t[3]=n=>ee(_)?_.value=n:null),type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),e(b,null,{default:o(()=>[e(m,{type:"primary",icon:"search",onClick:h},{default:o(()=>t[6]||(t[6]=[g("搜索")])),_:1}),e(m,{icon:"refresh",onClick:K},{default:o(()=>t[7]||(t[7]=[g("重置")])),_:1})]),_:1})]),_:1},8,["model"]),[[I,l(D)]]),e(G,{gutter:10,class:"mb8"},{default:o(()=>[e(k,{span:1.5},{default:o(()=>[f((p(),v(m,{type:"danger",plain:"",icon:"delete",disabled:l(B),onClick:L},{default:o(()=>t[8]||(t[8]=[g("删除")])),_:1},8,["disabled"])),[[C,["monitor:logininfor:remove"]]])]),_:1}),e(k,{span:1.5},{default:o(()=>[f((p(),v(m,{type:"danger",plain:"",icon:"delete",onClick:q},{default:o(()=>t[9]||(t[9]=[g("清空")])),_:1})),[[C,["monitor:logininfor:remove"]]])]),_:1}),e(k,{span:1.5},{default:o(()=>[f((p(),v(m,{type:"warning",plain:"",icon:"download",onClick:E},{default:o(()=>t[10]||(t[10]=[g("导出")])),_:1})),[[C,["system:logininfor:export"]]])]),_:1}),e(A,{showSearch:l(D),onQueryTable:c},null,8,["showSearch"])]),_:1}),f((p(),v(J,{data:l(x),border:"",onSelectionChange:P},{default:o(()=>[e(s,{type:"selection",width:"55",align:"center"}),e(s,{label:"用户名称",align:"center",prop:"userName",width:"100"}),e(s,{label:"登录地址",align:"center",prop:"ipaddr",width:"130"},{default:o(({row:n})=>[N("div",null,S(n.loginLocation),1),N("div",null,S(n.ipaddr),1)]),_:1}),e(s,{label:"浏览器",prop:"browser"}),e(s,{label:"客户端id",prop:"clientId","show-overflow-tooltip":!0}),e(s,{label:"操作系统",align:"center",prop:"os"}),e(s,{label:"操作状态",align:"center",prop:"status",width:"90"},{default:o(({row:n})=>[e(H,{options:l(V),value:n.status},null,8,["options","value"])]),_:1}),e(s,{label:"操作信息",align:"center",prop:"msg"}),e(s,{label:"登录日期",align:"center",prop:"loginTime",width:"100"},{default:o(n=>[N("span",null,S(l(te)(n.row.loginTime)),1)]),_:1}),e(s,{label:"操作",width:"100",align:"center"},{default:o(n=>[e(m,{type:"danger",text:"",plain:"",icon:"delete",onClick:pe=>L(n.row)},{default:o(()=>t[11]||(t[11]=[g("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[W,l(y)]]),f(e(M,{total:l(w),page:l(a).pageNum,"onUpdate:page":t[4]||(t[4]=n=>l(a).pageNum=n),limit:l(a).pageSize,"onUpdate:limit":t[5]||(t[5]=n=>l(a).pageSize=n),onPagination:c},null,8,["total","page","limit"]),[[I,l(w)>0]])])}}});export{fe as default};
