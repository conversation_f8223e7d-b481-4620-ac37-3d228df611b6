import L from"./PanelGroup-DPPM2iDV.js";import U from"./LineChart-Bu2fgEHm.js";import Z from"./RaddarChart-D9eyCrkS.js";import K from"./PieChart-JpwPnE1-.js";import Q from"./BarChart-ngkQAzeL.js";import X from"./index-6dkBWl17.js";import{g as tt,_ as st,aG as W,r as q,e as B,d as et,J as nt,aM as ot,E as it,x as g,o as z,c as at,i as e,l as i,j as c,k as u,D as O,t as w,m as rt,p as ut,s as dt,v as lt}from"./index-D9f5ARRd.js";import"./index-BtiuLXK9.js";var V={exports:{}},ct=V.exports,A;function ht(){return A||(A=1,function(R,T){(function(Y,d){R.exports=d()})(ct,function(){var Y,d,y=1e3,b=6e4,C=36e5,H=864e5,I=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,x=31536e6,k=2628e6,j=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/,D={years:x,months:k,days:H,hours:C,minutes:b,seconds:y,milliseconds:1,weeks:6048e5},l=function(n){return n instanceof S},p=function(n,s,t){return new S(n,t,s.$l)},M=function(n){return d.p(n)+"s"},_=function(n){return n<0},h=function(n){return _(n)?Math.ceil(n):Math.floor(n)},E=function(n){return Math.abs(n)},v=function(n,s){return n?_(n)?{negative:!0,format:""+E(n)+s}:{negative:!1,format:""+n+s}:{negative:!1,format:""}},S=function(){function n(t,o,r){var a=this;if(this.$d={},this.$l=r,t===void 0&&(this.$ms=0,this.parseFromMilliseconds()),o)return p(t*D[M(o)],this);if(typeof t=="number")return this.$ms=t,this.parseFromMilliseconds(),this;if(typeof t=="object")return Object.keys(t).forEach(function($){a.$d[M($)]=t[$]}),this.calMilliseconds(),this;if(typeof t=="string"){var m=t.match(j);if(m){var f=m.slice(2).map(function($){return $!=null?Number($):0});return this.$d.years=f[0],this.$d.months=f[1],this.$d.weeks=f[2],this.$d.days=f[3],this.$d.hours=f[4],this.$d.minutes=f[5],this.$d.seconds=f[6],this.calMilliseconds(),this}}return this}var s=n.prototype;return s.calMilliseconds=function(){var t=this;this.$ms=Object.keys(this.$d).reduce(function(o,r){return o+(t.$d[r]||0)*D[r]},0)},s.parseFromMilliseconds=function(){var t=this.$ms;this.$d.years=h(t/x),t%=x,this.$d.months=h(t/k),t%=k,this.$d.days=h(t/H),t%=H,this.$d.hours=h(t/C),t%=C,this.$d.minutes=h(t/b),t%=b,this.$d.seconds=h(t/y),t%=y,this.$d.milliseconds=t},s.toISOString=function(){var t=v(this.$d.years,"Y"),o=v(this.$d.months,"M"),r=+this.$d.days||0;this.$d.weeks&&(r+=7*this.$d.weeks);var a=v(r,"D"),m=v(this.$d.hours,"H"),f=v(this.$d.minutes,"M"),$=this.$d.seconds||0;this.$d.milliseconds&&($+=this.$d.milliseconds/1e3,$=Math.round(1e3*$)/1e3);var F=v($,"S"),G=t.negative||o.negative||a.negative||m.negative||f.negative||F.negative,J=m.format||f.format||F.format?"T":"",P=(G?"-":"")+"P"+t.format+o.format+a.format+J+m.format+f.format+F.format;return P==="P"||P==="-P"?"P0D":P},s.toJSON=function(){return this.toISOString()},s.format=function(t){var o=t||"YYYY-MM-DDTHH:mm:ss",r={Y:this.$d.years,YY:d.s(this.$d.years,2,"0"),YYYY:d.s(this.$d.years,4,"0"),M:this.$d.months,MM:d.s(this.$d.months,2,"0"),D:this.$d.days,DD:d.s(this.$d.days,2,"0"),H:this.$d.hours,HH:d.s(this.$d.hours,2,"0"),m:this.$d.minutes,mm:d.s(this.$d.minutes,2,"0"),s:this.$d.seconds,ss:d.s(this.$d.seconds,2,"0"),SSS:d.s(this.$d.milliseconds,3,"0")};return o.replace(I,function(a,m){return m||String(r[a])})},s.as=function(t){return this.$ms/D[M(t)]},s.get=function(t){var o=this.$ms,r=M(t);return r==="milliseconds"?o%=1e3:o=r==="weeks"?h(o/D[r]):this.$d[r],o||0},s.add=function(t,o,r){var a;return a=o?t*D[M(o)]:l(t)?t.$ms:p(t,this).$ms,p(this.$ms+a*(r?-1:1),this)},s.subtract=function(t,o){return this.add(t,o,!0)},s.locale=function(t){var o=this.clone();return o.$l=t,o},s.clone=function(){return p(this.$ms,this)},s.humanize=function(t){return Y().add(this.$ms,"ms").locale(this.$l).fromNow(!t)},s.valueOf=function(){return this.asMilliseconds()},s.milliseconds=function(){return this.get("milliseconds")},s.asMilliseconds=function(){return this.as("milliseconds")},s.seconds=function(){return this.get("seconds")},s.asSeconds=function(){return this.as("seconds")},s.minutes=function(){return this.get("minutes")},s.asMinutes=function(){return this.as("minutes")},s.hours=function(){return this.get("hours")},s.asHours=function(){return this.as("hours")},s.days=function(){return this.get("days")},s.asDays=function(){return this.as("days")},s.weeks=function(){return this.get("weeks")},s.asWeeks=function(){return this.as("weeks")},s.months=function(){return this.get("months")},s.asMonths=function(){return this.as("months")},s.years=function(){return this.get("years")},s.asYears=function(){return this.as("years")},n}(),N=function(n,s,t){return n.add(s.years()*t,"y").add(s.months()*t,"M").add(s.days()*t,"d").add(s.hours()*t,"h").add(s.minutes()*t,"m").add(s.seconds()*t,"s").add(s.milliseconds()*t,"ms")};return function(n,s,t){Y=t,d=t().$utils(),t.duration=function(a,m){var f=t.locale();return p(a,{$l:f},m)},t.isDuration=l;var o=s.prototype.add,r=s.prototype.subtract;s.prototype.add=function(a,m){return l(a)?N(this,a,1):o.bind(this)(a,m)},s.prototype.subtract=function(a,m){return l(a)?N(this,a,-1):r.bind(this)(a,m)}}})}(V)),V.exports}var mt=ht();const ft=tt(mt),_t={class:"home"},pt={class:"user-item"},$t={class:"user-item-left"},vt={class:"user-item-right"},gt={class:"mb10"},yt={class:"text-warning mb10"},wt={class:"work-wrap"},Dt={class:"home-card-more"},Mt={class:"chart-wrapper"},bt={class:"chart-wrapper"},xt={class:"chart-wrapper"},kt={class:"chart-wrapper"},St=dt({name:"index"}),Yt=Object.assign(St,{setup(R){W.extend(ft);const T=q(!1),Y={newVisitis:{expectedData:[100,120,161,134,105,160,165],actualData:[120,82,91,154,162,140,145]},messages:{expectedData:[200,192,120,144,160,130,140],actualData:[180,160,151,106,145,150,130]},purchases:{expectedData:[80,100,121,104,105,90,100],actualData:[120,90,100,138,142,130,130]},shoppings:{expectedData:[130,140,141,142,145,150,160],actualData:[120,82,91,154,162,140,130]}},{proxy:d}=lt(),y=B(()=>et().userInfo),b=B(()=>nt().onlineInfo),C=B(()=>d.parseTime(new Date,"YYYY-MM-DD")),H=ot();let I=it([]);const x=q(null);function k(l){x.value=l,I=Y[l]}k("newVisitis");function j(){d.$modal.msg("请通过搜索添加")}function D(l){return W.duration(l*60,"second").format("HH时mm分")}return(l,p)=>{const M=g("el-avatar"),_=g("el-col"),h=g("el-row"),E=g("router-link"),v=g("el-button"),S=g("el-card"),N=g("el-statistic"),n=g("svg-icon"),s=g("el-scrollbar");return z(),at("div",_t,[e(h,{gutter:15},{default:i(()=>[e(_,{md:24,lg:16,xl:24,class:"mb10"},{default:i(()=>[e(S,{shadow:"hover"},{default:i(()=>[c("div",pt,[c("div",$t,[e(M,{size:60,shape:"circle",src:u(y).avatar},null,8,["src"])]),c("div",vt,[e(h,null,{default:i(()=>[e(_,{xs:24,md:24,class:"right-title mb20 one-text-overflow"},{default:i(()=>[c("div",gt,[O(w(u(y).welcomeMessage)+" ",1),c("strong",null,w(u(y).nickName),1),c("span",null,"("+w(u(y).welcomeContent)+")",1)])]),_:1})]),_:1}),e(h,null,{default:i(()=>[e(v,{icon:"edit"},{default:i(()=>[e(E,{to:"/user/profile"},{default:i(()=>[O(w(l.$t("layout.modifyInformation")),1)]),_:1})]),_:1})]),_:1})])])]),_:1})]),_:1}),e(_,{lg:8,class:"mb10"},{default:i(()=>[e(S,{style:{height:"100%"}},{default:i(()=>[c("div",yt,w(u(C))+" "+w(u(H)),1),c("div",wt,[e(N,{title:l.$t("layout.workTime"),formatter:D,value:u(b).todayOnlineTime},null,8,["title","value"]),e(N,{title:l.$t("layout.onlineClientNum"),value:u(b).clientNum},null,8,["title","value"])])]),_:1})]),_:1})]),_:1}),e(h,{gutter:15},{default:i(()=>[e(_,{md:24,lg:24,xl:24,class:"mb10"},{default:i(()=>[e(S,{shadow:"hover"},{header:i(()=>[c("span",null,[e(n,{name:"tool"}),O(" "+w(l.$t("layout.commonFuncs")),1)]),c("div",Dt,[e(v,{text:"",onClick:p[0]||(p[0]=t=>j())},{default:i(()=>[O(w(l.$t("btn.add")),1)]),_:1})])]),default:i(()=>[c("div",null,[e(s,{"wrap-class":"scrollbar-wrapper"},{default:i(()=>[e(u(X),{modelValue:u(T),"onUpdate:modelValue":p[1]||(p[1]=t=>rt(T)?T.value=t:null)},null,8,["modelValue"])]),_:1})])]),_:1})]),_:1})]),_:1}),e(u(L),{onHandleSetLineChartData:k}),e(h,{gutter:32},{default:i(()=>[e(_,{xs:24,sm:24,lg:24},{default:i(()=>[c("div",Mt,[(z(),ut(u(U),{"chart-data":u(I),key:u(x)},null,8,["chart-data"]))])]),_:1})]),_:1}),e(h,{gutter:32},{default:i(()=>[e(_,{xs:24,sm:24,lg:8},{default:i(()=>[c("div",bt,[e(u(Z))])]),_:1}),e(_,{xs:24,sm:24,lg:8},{default:i(()=>[c("div",xt,[e(u(K))])]),_:1}),e(_,{xs:24,sm:24,lg:8},{default:i(()=>[c("div",kt,[e(u(Q))])]),_:1})]),_:1})])}}}),Et=st(Yt,[["__scopeId","data-v-b41b211b"]]);export{Et as default};
