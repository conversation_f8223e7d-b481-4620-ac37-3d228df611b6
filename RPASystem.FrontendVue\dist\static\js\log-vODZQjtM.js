import{u as de,r as m,E as ie,a5 as pe,x as s,N as F,o as c,c as h,O as V,S as ce,k as l,i as e,l as t,z as me,F as J,K as M,p as v,m as U,D as r,j as B,B as fe,t as b,q as $,s as _e,v as be}from"./index-D9f5ARRd.js";import{l as ge,d as ve,c as we,e as ye}from"./jobLog-DVbq00T2.js";const je={class:"app-container"},he={key:0},Ve={key:1},ke={class:"dialog-footer"},xe=_e({name:"job/log"}),Be=Object.assign(xe,{setup(Le){const{proxy:p}=be(),q=de(),x=m(!0),S=m([]),G=m(!0),I=m(!0),L=m(0),O=m([]),w=m(!1),y=m([]),T=m([]),N=m([]),z=m(),Y=ie({form:{},queryParams:{pageNum:1,pageSize:10,jobName:void 0,jobGroup:void 0,status:void 0,jobId:void 0}}),{form:d,queryParams:u}=pe(Y);u.value.jobId=q.query.jobId,p.getDicts("sys_job_status").then(n=>{T.value=n.data}),p.getDicts("sys_job_group").then(n=>{N.value=n.data});function j(){x.value=!0,ge(p.addDateRange(u.value,y.value)).then(n=>{O.value=n.data.result,L.value=n.data.totalNum,x.value=!1})}function K(n,o){return p.selectDictLabel(N.value,n.jobGroup)}function k(){u.value.pageNum=1,j()}function Q(){y.value=[],p.resetForm("queryForm"),k()}function H(){d.value={createTime:void 0,elapsed:0,exception:void 0,invokeTarget:void 0,jobGroup:void 0,jobId:0,jobLogId:0,jobMessage:void 0,jobName:void 0,status:void 0},p.resetForm("formRef")}function X(n){S.value=n.map(o=>o.jobLogId),G.value=!n.length}function A(n){H(),w.value=!0,d.value=n,console.log(n)}function W(n){const o=S.value;p.$confirm('是否确认删除调度日志编号为"'+o+'"的数据项?',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return ve(o)}).then(()=>{j(),p.$modal.msgSuccess("删除成功")})}function Z(){p.$confirm("是否确认清空所有调度日志数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return we()}).then(()=>{j(),p.$modal.msgSuccess("清空成功")})}function ee(){p.$confirm("是否确认导出所有调度日志数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return ye(u.value)}).then(n=>{p.download(n.data.path)})}return k(),(n,o)=>{const te=s("el-input"),i=s("el-form-item"),R=s("el-option"),D=s("el-select"),oe=s("el-date-picker"),g=s("el-button"),E=s("el-form"),_=s("el-col"),le=s("right-toolbar"),P=s("el-row"),f=s("el-table-column"),ae=s("dict-tag"),ne=s("el-table"),ue=s("pagination"),se=s("el-dialog"),C=F("hasPermi"),re=F("loading");return c(),h("div",je,[V(e(E,{model:l(u),ref:"queryForm",inline:!0,"label-width":"68px"},{default:t(()=>[e(i,{label:"任务名称",prop:"jobName"},{default:t(()=>[e(te,{modelValue:l(u).jobName,"onUpdate:modelValue":o[0]||(o[0]=a=>l(u).jobName=a),placeholder:"请输入任务名称",clearable:"",style:{width:"240px"},onKeyup:me(k,["enter"])},null,8,["modelValue"])]),_:1}),e(i,{label:"任务组名",prop:"jobGroup"},{default:t(()=>[e(D,{modelValue:l(u).jobGroup,"onUpdate:modelValue":o[1]||(o[1]=a=>l(u).jobGroup=a),placeholder:"请任务组名",clearable:"",style:{width:"240px"}},{default:t(()=>[(c(!0),h(J,null,M(l(N),a=>(c(),v(R,{key:a.dictValue,label:a.dictLabel,value:a.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"执行状态",prop:"status"},{default:t(()=>[e(D,{modelValue:l(u).status,"onUpdate:modelValue":o[2]||(o[2]=a=>l(u).status=a),placeholder:"请选择执行状态",clearable:"",style:{width:"240px"}},{default:t(()=>[(c(!0),h(J,null,M(l(T),a=>(c(),v(R,{key:a.dictValue,label:a.dictLabel,value:a.dictValue},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"执行时间"},{default:t(()=>[e(oe,{modelValue:l(y),"onUpdate:modelValue":o[3]||(o[3]=a=>U(y)?y.value=a:null),"value-format":"YYYY-MM-DD HH:mm:ss",type:"datetimerange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",shortcuts:n.dateOptions},null,8,["modelValue","shortcuts"])]),_:1}),e(i,null,{default:t(()=>[e(g,{type:"primary",icon:"search",onClick:k},{default:t(()=>o[8]||(o[8]=[r("搜索")])),_:1}),e(g,{icon:"refresh",onClick:Q},{default:t(()=>o[9]||(o[9]=[r("重置")])),_:1})]),_:1})]),_:1},8,["model"]),[[ce,l(I)]]),e(P,{gutter:10,class:"mb8"},{default:t(()=>[e(_,{span:1.5},{default:t(()=>[V((c(),v(g,{type:"danger",plain:"",icon:"delete",disabled:l(G),onClick:W},{default:t(()=>o[10]||(o[10]=[r("删除")])),_:1},8,["disabled"])),[[C,["PRIV_JOBLOG_DELETE"]]])]),_:1}),e(_,{span:1.5},{default:t(()=>[V((c(),v(g,{type:"danger",plain:"",icon:"delete",onClick:Z,disabled:l(L)<=0},{default:t(()=>o[11]||(o[11]=[r("清空")])),_:1},8,["disabled"])),[[C,["PRIV_JOBLOG_REMOVE"]]])]),_:1}),e(_,{span:1.5},{default:t(()=>[V((c(),v(g,{type:"warning",plain:"",icon:"download",onClick:ee},{default:t(()=>o[12]||(o[12]=[r("导出")])),_:1})),[[C,["PRIV_JOBLOG_EXPORT"]]])]),_:1}),e(le,{showSearch:l(I),onQueryTable:j},null,8,["showSearch"])]),_:1}),V((c(),v(ne,{data:l(O),onSelectionChange:X},{default:t(()=>[e(f,{type:"selection",width:"55",align:"center"}),e(f,{label:"日志编号",width:"80",align:"center",prop:"jobLogId"}),e(f,{label:"任务名称",align:"center",prop:"jobName","show-overflow-tooltip":!0}),e(f,{label:"任务组名",align:"center",prop:"jobGroup",formatter:K,"show-overflow-tooltip":!0}),e(f,{label:"调用目标字符串",align:"center",prop:"invokeTarget","show-overflow-tooltip":!0}),e(f,{label:"日志信息",align:"center",prop:"jobMessage","show-overflow-tooltip":!0}),e(f,{label:"执行状态",align:"center",prop:"status"},{default:t(a=>[e(ae,{options:l(T),value:a.row.status},null,8,["options","value"])]),_:1}),e(f,{label:"作业用时",align:"center",prop:"elapsed"},{default:t(a=>[B("span",{style:fe(a.row.elapsed<1e3?"color:green":a.row.elapsed<3e3?"color:orange":"color:red")},b(Math.floor(a.row.elapsed)/1e3)+" s ",5)]),_:1}),e(f,{label:"执行时间",align:"center",prop:"createTime",width:"180"},{default:t(a=>[B("span",null,b(n.parseTime(a.row.createTime)),1)]),_:1}),e(f,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:t(a=>[e(g,{text:"",icon:"view",onClick:Te=>A(a.row)},{default:t(()=>o[13]||(o[13]=[r("详细")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[re,l(x)]]),e(ue,{total:l(L),page:l(u).pageNum,"onUpdate:page":o[4]||(o[4]=a=>l(u).pageNum=a),limit:l(u).pageSize,"onUpdate:limit":o[5]||(o[5]=a=>l(u).pageSize=a),onPagination:j},null,8,["total","page","limit"]),e(se,{title:"日志详细",modelValue:l(w),"onUpdate:modelValue":o[7]||(o[7]=a=>U(w)?w.value=a:null),width:"700px","append-to-body":""},{footer:t(()=>[B("div",ke,[e(g,{text:"",onClick:o[6]||(o[6]=a=>w.value=!1)},{default:t(()=>o[14]||(o[14]=[r("关 闭")])),_:1})])]),default:t(()=>[e(E,{ref_key:"formRef",ref:z,model:l(d),"label-width":"100px"},{default:t(()=>[e(P,null,{default:t(()=>[e(_,{span:12},{default:t(()=>[e(i,{label:"日志序号："},{default:t(()=>[r(b(l(d).jobLogId),1)]),_:1}),e(i,{label:"任务名称："},{default:t(()=>[r(b(l(d).jobName),1)]),_:1})]),_:1}),e(_,{span:12},{default:t(()=>[e(i,{label:"任务分组："},{default:t(()=>[r(b(l(d).jobGroup),1)]),_:1}),e(i,{label:"执行时间："},{default:t(()=>[r(b(l(d).createTime),1)]),_:1})]),_:1}),e(_,{span:24},{default:t(()=>[e(i,{label:"调用方法："},{default:t(()=>[r(b(l(d).invokeTarget),1)]),_:1})]),_:1}),e(_,{span:24},{default:t(()=>[e(i,{label:"日志信息："},{default:t(()=>[r(b(l(d).jobMessage),1)]),_:1})]),_:1}),e(_,{span:24},{default:t(()=>[e(i,{label:"执行状态："},{default:t(()=>[l(d).status==0?(c(),h("div",he,"正常")):l(d).status==1?(c(),h("div",Ve,"失败")):$("",!0)]),_:1})]),_:1}),l(d).status==1?(c(),v(_,{key:0,span:24},{default:t(()=>[e(i,{label:"异常信息："},{default:t(()=>[r(b(l(d).exception),1)]),_:1})]),_:1})):$("",!0)]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});export{Be as default};
