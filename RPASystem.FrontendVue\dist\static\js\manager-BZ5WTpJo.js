import{a7 as he,a as Ce,r as m,E as ke,a5 as Te,x as u,N as H,o as s,c as V,O as y,S as Se,k as o,i as l,l as a,D as r,F as P,K as $,p,t as f,A as xe,m as J,j,q as T,a8 as Ue,s as Pe,v as $e}from"./index-D9f5ARRd.js";import{l as Le,d as Ne,g as ze,a as W}from"./article-By-2AKrw.js";import{t as Ae}from"./articlecategory-Bm4F1q31.js";import{M as Oe}from"./index-DkvPtx7O.js";const qe={class:"app-container"},Ie=["onClick"],De={key:0},Fe=Pe({name:"index"}),Qe=Object.assign(Fe,{setup(Me){const X=he(),{proxy:w}=$e(),A=Ce(),O=m([]),Y=m(!0),q=m(!0),L=m(!0),B=m([]),R=m(0),E=m([]),I=m(!1),Q=ke({form:{},queryParams:{sort:"cid",sortType:"desc",status:"",isPublic:"",isTop:"",articleType:"",auditStatus:""},options:{isPublicOptions:[{dictLabel:"是",dictValue:"1"},{dictLabel:"否",dictValue:"0",listClass:"info"}],auditOptions:[{dictLabel:"通过",dictValue:"1"},{dictLabel:"待审核",dictValue:"0",listClass:"info"},{dictLabel:"不通过",dictValue:"2",listClass:"danger"}],sys_comment_permi:[],sys_article_status:[],sys_article_type:[]}}),D=m([{visible:!1,prop:"abstractText",label:"摘要"},{visible:!1,prop:"isTop",label:"置顶"}]),Z=m(),{queryParams:i,options:h}=Te(Q);function ee(n){n.prop==null||n.order==null?(i.sort=void 0,i.sortType=void 0):(i.sort=n.prop,i.sortType=n.order),_()}w.getDicts(["sys_article_status","sys_article_type","sys_comment_permi"]).then(n=>{n.data.forEach(t=>{Q.options[t.dictType]=t.list})});function te(){Ae().then(n=>{n.code==200&&(E.value=n.data)})}function S(){I.value=!0,Le(i.value).then(n=>{I.value=!1,n.code==200&&(B.value=n.data.result,R.value=n.data.totalNum)})}function le(){w.resetForm("queryForm"),_()}function _(){S()}function ae(){A.push({path:"/article/publish"})}function oe(){A.push({path:"/article/publishMoments"})}function ne(n){Ne(n.cid).then(t=>{t.code==200&&(w.$modal.msgSuccess("删除成功"),_())})}function ie(n){A.push({path:"/article/publish",query:{cid:n.cid}})}const x=m({});function F(n){ze(n.cid).then(t=>{x.value=t.data,N.value=!0})}function se(n){return n.auditStatus==0}function ue(n){const t=n.cid||O.value;W("pass",t).then(C=>{const{code:c}=C;c==200&&w.$modal.msgSuccess("通过成功"),S()})}function re(n){const t=n.cid||O.value;w.$prompt("请输入拒绝原因","Tip",{}).then(({value:C})=>{W("reject",t,{reason:C}).then(c=>{const{code:M}=c;M==200&&w.$modal.msgSuccess("拒绝成功"),S()})})}function de(n){O.value=n.map(t=>t.cid),Y.value=n.length!=1,q.value=!n.length}const N=m(!1);return te(),_(),(n,t)=>{const C=u("el-input"),c=u("el-form-item"),M=u("el-cascader"),v=u("el-radio-button"),U=u("el-radio-group"),b=u("el-button"),pe=u("el-form"),z=u("el-col"),ce=u("right-toolbar"),me=u("el-row"),d=u("el-table-column"),K=u("image-preview"),fe=u("el-tag"),_e=u("dict-tag"),G=u("el-switch"),ve=u("el-popconfirm"),be=u("el-button-group"),ge=u("el-table"),ye=u("pagination"),Ve=u("el-dialog"),k=H("hasPermi"),we=H("loading");return s(),V("div",qe,[y(l(pe,{model:o(i),"label-position":"left",inline:"",ref_key:"queryForm",ref:Z,onSubmit:t[13]||(t[13]=xe(()=>{},["prevent"]))},{default:a(()=>[l(c,{label:"标题",prop:"title"},{default:a(()=>[l(C,{modelValue:o(i).title,"onUpdate:modelValue":t[0]||(t[0]=e=>o(i).title=e),placeholder:"请输入标题"},null,8,["modelValue"])]),_:1}),l(c,{label:"摘要",prop:"abstractText"},{default:a(()=>[l(C,{modelValue:o(i).abstractText,"onUpdate:modelValue":t[1]||(t[1]=e=>o(i).abstractText=e),placeholder:"请输入摘要"},null,8,["modelValue"])]),_:1}),l(c,{label:"分类",prop:"categoryId"},{default:a(()=>[l(M,{class:"w100",options:o(E),props:{checkStrictly:!0,value:"categoryId",label:"name",emitPath:!1},placeholder:"请选择分类",clearable:"",modelValue:o(i).categoryId,"onUpdate:modelValue":t[2]||(t[2]=e=>o(i).categoryId=e)},null,8,["options","modelValue"])]),_:1}),l(c,{label:"审核状态",prop:"auditStatus"},{default:a(()=>[l(U,{modelValue:o(i).auditStatus,"onUpdate:modelValue":t[3]||(t[3]=e=>o(i).auditStatus=e),onChange:t[4]||(t[4]=e=>_())},{default:a(()=>[l(v,{value:""},{default:a(()=>t[18]||(t[18]=[r("全部")])),_:1}),(s(!0),V(P,null,$(o(h).auditOptions,e=>(s(),p(v,{key:e.dictValue,value:e.dictValue},{default:a(()=>[r(f(e.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(c,{prop:"status"},{default:a(()=>[l(U,{modelValue:o(i).status,"onUpdate:modelValue":t[5]||(t[5]=e=>o(i).status=e),onChange:t[6]||(t[6]=e=>_())},{default:a(()=>[l(v,{value:""},{default:a(()=>t[19]||(t[19]=[r("全部")])),_:1}),(s(!0),V(P,null,$(o(h).sys_article_status,e=>(s(),p(v,{key:e.dictValue,value:e.dictValue},{default:a(()=>[r(f(e.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(c,{prop:"articleType"},{default:a(()=>[l(U,{modelValue:o(i).articleType,"onUpdate:modelValue":t[7]||(t[7]=e=>o(i).articleType=e),onChange:t[8]||(t[8]=e=>_())},{default:a(()=>[l(v,{value:""},{default:a(()=>t[20]||(t[20]=[r("全部")])),_:1}),(s(!0),V(P,null,$(o(h).sys_article_type,e=>(s(),p(v,{key:e.dictValue,value:e.dictValue},{default:a(()=>[r(f(e.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(c,{label:"是否公开",prop:"isPublic"},{default:a(()=>[l(U,{modelValue:o(i).isPublic,"onUpdate:modelValue":t[9]||(t[9]=e=>o(i).isPublic=e),onChange:t[10]||(t[10]=e=>_())},{default:a(()=>[l(v,{value:""},{default:a(()=>t[21]||(t[21]=[r("全部")])),_:1}),(s(!0),V(P,null,$(o(h).isPublicOptions,e=>(s(),p(v,{key:e.dictValue,value:e.dictValue},{default:a(()=>[r(f(e.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(c,{label:"是否置顶",prop:"isTop"},{default:a(()=>[l(U,{modelValue:o(i).isTop,"onUpdate:modelValue":t[11]||(t[11]=e=>o(i).isTop=e),onChange:t[12]||(t[12]=e=>_())},{default:a(()=>[l(v,{value:""},{default:a(()=>t[22]||(t[22]=[r("全部")])),_:1}),(s(!0),V(P,null,$(o(h).isPublicOptions,e=>(s(),p(v,{key:e.dictValue,value:e.dictValue},{default:a(()=>[r(f(e.dictLabel),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(c,null,{default:a(()=>[l(b,{type:"primary",icon:"search",onClick:_},{default:a(()=>t[23]||(t[23]=[r("搜索")])),_:1}),l(b,{icon:"refresh",onClick:le},{default:a(()=>t[24]||(t[24]=[r("重置")])),_:1})]),_:1})]),_:1},8,["model"]),[[Se,o(L)]]),l(me,{gutter:10,class:"mb8"},{default:a(()=>[l(z,{span:1.5},{default:a(()=>[y((s(),p(b,{type:"success",disabled:o(q),plain:"",icon:"check",size:"small",onClick:ue},{default:a(()=>t[25]||(t[25]=[r(" 通过 ")])),_:1},8,["disabled"])),[[k,["article:audit"]]])]),_:1}),l(z,{span:1.5},{default:a(()=>[y((s(),p(b,{type:"danger",disabled:o(q),plain:"",icon:"close",size:"small",onClick:re},{default:a(()=>t[26]||(t[26]=[r(" 拒绝 ")])),_:1},8,["disabled"])),[[k,["article:audit"]]])]),_:1}),l(z,{span:1.5},{default:a(()=>[y((s(),p(b,{type:"primary",plain:"",icon:"plus",size:"small",onClick:ae},{default:a(()=>t[27]||(t[27]=[r("发布文章")])),_:1})),[[k,["system:article:add"]]])]),_:1}),l(z,{span:1.5},{default:a(()=>[y((s(),p(b,{type:"danger",plain:"",icon:"plus",size:"small",onClick:oe},{default:a(()=>t[28]||(t[28]=[r("发布动态")])),_:1})),[[k,["system:article:add"]]])]),_:1}),l(ce,{showSearch:o(L),"onUpdate:showSearch":t[14]||(t[14]=e=>J(L)?L.value=e:null),onQueryTable:S,columns:o(D)},null,8,["showSearch","columns"])]),_:1}),y((s(),p(ge,{data:o(B),height:"600px",onSelectionChange:de,"highlight-current-row":"",onSortChange:ee,ref:"table"},{default:a(()=>[l(d,{type:"selection",width:"50",align:"center",selectable:se}),l(d,{prop:"cid",label:"文章信息",width:"130"},{default:a(({row:e})=>[j("div",{onClick:g=>F(e)},"内容id："+f(e.cid),9,Ie),j("div",null,"作者："+f(e.authorName),1),j("div",null,"标签："+f(e.tags),1)]),_:1}),l(d,{label:"分类"},{default:a(({row:e})=>[e.articleCategoryNav?(s(),V("div",De,f(e.articleCategoryNav.name),1)):T("",!0)]),_:1}),l(d,{prop:"title",label:"标题",width:"120","show-overflow-tooltip":!0},{default:a(e=>[l(b,{link:"",type:"primary",onClick:g=>F(e.row)},{default:a(()=>[r(f(e.row.title),1)]),_:2},1032,["onClick"])]),_:1}),l(d,{prop:"coverUrl",label:"封面",width:"90","show-overflow-tooltip":!0},{default:a(({row:e})=>[e.coverUrl?(s(),p(K,{key:0,src:e.coverUrl,split:",",style:{width:"40px"}},null,8,["src"])):T("",!0)]),_:1}),l(d,{prop:"hits",label:"浏览",width:"80",align:"center",sortable:""}),l(d,{prop:"praiseNum",label:"赞",width:"80",align:"center",sortable:""}),l(d,{prop:"commentNum",label:"评论",width:"80",align:"center",sortable:""}),o(D).showColumn("abstractText")?(s(),p(d,{key:0,prop:"abstractText",label:"摘要"})):T("",!0),l(d,{prop:"status",align:"center",label:"状态",width:"90"},{default:a(e=>[l(fe,{type:e.row.status=="2"?"danger":"success"},{default:a(()=>[r(f(e.row.status=="2"?"草稿":"已发布"),1)]),_:2},1032,["type"])]),_:1}),l(d,{prop:"auditStatus",align:"center",label:"审核",width:"90"},{default:a(e=>[l(_e,{options:o(h).auditOptions,value:e.row.auditStatus},null,8,["options","value"])]),_:1}),o(D).showColumn("isTop")?(s(),p(d,{key:1,label:"置顶",prop:"isTop",width:"90",align:"center",sortable:""},{default:a(e=>[l(G,{modelValue:e.row.isTop,"onUpdate:modelValue":g=>e.row.isTop=g,"inline-prompt":"",disabled:"","active-text":"是","inactive-text":"否","active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue"])]),_:1})):T("",!0),l(d,{label:"公开",align:"center",prop:"isPublic",width:"90"},{default:a(e=>[l(G,{modelValue:e.row.isPublic,"onUpdate:modelValue":g=>e.row.isPublic=g,"inline-prompt":"","active-text":"是","inactive-text":"否","active-value":1,"inactive-value":0,disabled:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(d,{prop:"createTime",label:"发布时间",width:"128","show-overflow-tooltip":!0},{default:a(e=>[r(f(o(Ue)(e.row.createTime)),1)]),_:1}),l(d,{label:"操作",width:"130",fixed:"right"},{default:a(e=>[l(be,null,{default:a(()=>[l(b,{size:"small",icon:"view",onClick:g=>F(e.row)},null,8,["onClick"]),e.row.articleType==0?y((s(),p(b,{key:0,size:"small",icon:"edit",onClick:g=>ie(e.row)},null,8,["onClick"])),[[k,["system:article:update"]]]):T("",!0),l(ve,{title:"确定删除吗？",onConfirm:g=>ne(e.row),style:{"margin-left":"10px"}},{reference:a(()=>[y(l(b,{size:"small",type:"danger",icon:"delete"},null,512),[[k,["system:article:delete"]]])]),_:2},1032,["onConfirm"])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[we,o(I)]]),l(ye,{total:o(R),page:o(i).pageNum,"onUpdate:page":t[15]||(t[15]=e=>o(i).pageNum=e),limit:o(i).pageSize,"onUpdate:limit":t[16]||(t[16]=e=>o(i).pageSize=e),onPagination:S},null,8,["total","page","limit"]),l(Ve,{title:o(x).title,modelValue:o(N),"onUpdate:modelValue":t[17]||(t[17]=e=>J(N)?N.value=e:null)},{default:a(()=>[o(x).coverUrl?(s(),p(K,{key:0,src:o(x).coverUrl,split:",",style:{height:"140px"}},null,8,["src"])):T("",!0),l(o(Oe),{"show-code-row-number":"",editorId:"id1",theme:o(X).codeMode,modelValue:o(x).content},null,8,["theme","modelValue"])]),_:1},8,["title","modelValue"])])}}});export{Qe as default};
