import{_ as w,J as S,e as v,r as T,w as B,x as _,o as l,c as n,j as e,i as c,l as u,F as f,K,k as g,D as b,t as k,O as L,aK as $,z as j,m as D,s as E,v as I}from"./index-D9f5ARRd.js";const M={style:{height:"400px"}},N={class:"message_content"},U={key:0,class:"talk_item talk_primary"},q={class:"head"},z={class:"content"},F={class:"bubble"},O={key:1,class:"talk_item talk_other"},R={class:"head"},H={class:"content"},J={class:"bubble"},W={class:"talk_bottom"},A={class:"talk_area"},G={class:"talk_btn"},P=E({name:"msglist"}),Q=Object.assign(P,{props:{modelValue:{}},setup(x){const m=S(),{proxy:i}=I(),d=x,y=v(()=>m.getMessageList(d.modelValue)),t=T("");function h(){if(t.value.trim().length<=0){i.$modal.msgError("请输入聊天内容");return}var r={toUserId:d.modelValue,message:t.value};m.sendChat(r).then(()=>{t.value="",p()}).catch(()=>{i.$modal.msgError("发送失败")})}function p(r){setTimeout(()=>{const s=v(()=>i.$refs.scrollContainer.$refs.wrapRef),a=s.value.scrollHeight;r==1?s.value.scrollTop=a:s.value.scrollTo({top:a+120,behavior:"smooth"})},100)}return B(()=>d.modelValue,()=>{p(1)},{immediate:!0}),(r,s)=>{const a=_("el-avatar"),C=_("el-scrollbar"),V=_("el-button");return l(),n("div",M,[e("div",N,[c(C,{height:"100%",ref:"scrollContainer"},{default:u(()=>[(l(!0),n(f,null,K(g(y),o=>(l(),n(f,null,[o.self?(l(),n("div",U,[e("div",q,[c(a,{shape:"square"},{default:u(()=>s[1]||(s[1]=[b(" 我 ")])),_:1})]),e("div",z,[e("div",F,k(o.message),1)])])):(l(),n("div",O,[e("div",R,[c(a,{src:o.fromUser.avatar,shape:"square"},null,8,["src"])]),e("div",H,[e("div",J,k(o.message),1)])]))],64))),256))]),_:1},512)]),e("div",W,[e("div",A,[L(e("textarea",{class:"textarea",placeholder:"请输入聊天内容",onKeyup:j(h,["enter"]),"onUpdate:modelValue":s[0]||(s[0]=o=>D(t)?t.value=o:null)},null,544),[[$,g(t)]]),e("div",G,[c(V,{type:"success",size:"default",onClick:h},{default:u(()=>s[2]||(s[2]=[b("发送")])),_:1})])])])])}}}),Y=w(Q,[["__scopeId","data-v-3eefb016"]]);export{Y as default};
