import{_ as l,d as u,x as g,k as d,y as m,o as h,c as p,j as o,t as _,i as a,q as f,v}from"./index-D9f5ARRd.js";const k={key:0,class:"other-login"},w={class:"other-tip"},C={__name:"oauthLogin",setup(N){const i=u(),{proxy:r}=v();function s(e){switch(i.setAuthSource(e),e){default:r.$modal.msg("接入请看文档https://www.izhaorui.cn/doc/backend/oauth.html");break}}return(e,t)=>{const n=g("svg-icon");return d(m).showOtherLogin?(h(),p("div",k,[o("div",w,_(e.$t("login.otherLoginWay")),1),o("span",{onClick:t[0]||(t[0]=c=>s("GITHUB")),title:"github"},[a(n,{name:"github",className:"login-icon"})]),o("span",{onClick:t[1]||(t[1]=c=>s("GITEE")),title:"gitee"},[a(n,{name:"gitee",className:"login-icon"})])])):f("",!0)}}},b=l(C,[["__scopeId","data-v-ee1f4eea"]]);export{b as default};
