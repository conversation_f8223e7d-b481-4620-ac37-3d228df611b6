import{_ as z,d as I,u as K,a as A,r as s,x as i,O as M,S as T,k as e,o as d,p as g,l as n,i as t,B as N,z as $,j as E,q as P,D as G,t as b,A as H,c as S,s as J,h as Q,aj as W,v as X}from"./index-D9f5ARRd.js";const Y={class:"login-code"},Z={key:0},ee={key:1},oe=J({name:"phonelogin"}),ne=Object.assign(oe,{setup(te){const x=I(),R=K(),q=A(),{proxy:p}=X(),a=s({code:"",uuid:"",phoneCode:"",phoneNum:""}),B={phoneNum:[{required:!0,trigger:"blur",message:"请输入手机号码",pattern:/^1\d{10}$/}],phoneCode:[{required:!0,trigger:"blur",message:"请输入短信验证码"}],code:[{required:!0,trigger:"change",message:"请输入验证码"}]},O=s(1),C=s(""),m=s(!1),c=s(""),w=s();w.value=R.query.redirect;function h(){Q().then(o=>{C.value="data:image/gif;base64,"+o.data.img,a.value.uuid=o.data.uuid,c.value=o.data.captchaOff})}const _=s(!1),V=s(0);function U(){const o=["phoneNum","code"];p.$refs.loginRef.validateField(o,async l=>{if(l)W(a.value).then(u=>{u.code==200&&(_.value=!0,V.value=Date.now()+1e3*60)}).catch(u=>{console.log(u),c.value&&h()});else return})}function v(){p.$refs.loginRef.validate(o=>{o&&(m.value=!0,x.phoneNumLogin(a.value).then(()=>{p.$modal.msgSuccess(p.$t("login.loginSuccess")),q.push({path:w.value||"/"})}).catch(l=>{console.error(l),m.value=!1}))})}function D(){_.value=!1}return h(),(o,l)=>{const u=i("svg-icon"),y=i("el-input"),f=i("el-form-item"),F=i("el-image"),k=i("el-button"),L=i("el-countdown"),j=i("el-form");return M((d(),g(j,{ref:"loginRef",model:e(a),rules:B,class:"login-form"},{default:n(()=>[t(f,{prop:"phoneNum"},{default:n(()=>[t(y,{modelValue:e(a).phoneNum,"onUpdate:modelValue":l[0]||(l[0]=r=>e(a).phoneNum=r),type:"phone",maxlength:11,"auto-complete":"off",placeholder:o.$t("login.input_phoneNum")},{prefix:n(()=>[t(u,{name:"phone",class:"input-icon"})]),_:1},8,["modelValue","placeholder"])]),_:1}),e(c)!="off"?(d(),g(f,{key:0,prop:"code",style:N({"margin-top":e(c)=="off"?"40px":""})},{default:n(()=>[t(y,{modelValue:e(a).code,"onUpdate:modelValue":l[1]||(l[1]=r=>e(a).code=r),"auto-complete":"off",placeholder:o.$t("login.captcha"),style:{width:"63%"},onKeyup:$(v,["enter"])},{prefix:n(()=>[t(u,{name:"validCode",class:"input-icon"})]),_:1},8,["modelValue","placeholder"]),E("div",Y,[t(F,{src:e(C),onClick:h,class:"login-code-img"},null,8,["src"])])]),_:1},8,["style"])):P("",!0),t(f,{prop:"phoneCode"},{default:n(()=>[t(y,{modelValue:e(a).phoneCode,"onUpdate:modelValue":l[2]||(l[2]=r=>e(a).phoneCode=r),type:"number","auto-complete":"off",placeholder:o.$t("login.phoneCode"),onKeyup:$(v,["enter"])},{prefix:n(()=>[t(u,{name:"validCode",class:"input-icon"})]),append:n(()=>[e(_)?(d(),g(L,{key:1,value:e(V),format:"mm:ss",onFinish:D},null,8,["value"])):(d(),g(k,{key:0,onClick:U},{default:n(()=>[G(b(o.$t("login.sendPhoneCode")),1)]),_:1}))]),_:1},8,["modelValue","placeholder"])]),_:1}),t(f,{style:N([{width:"100%"},{"margin-top":e(c)=="off"?"40px":""}])},{default:n(()=>[t(k,{loading:e(m),size:"default",round:"",type:"primary",style:{width:"100%"},onClick:H(v,["prevent"])},{default:n(()=>[e(m)?(d(),S("span",ee,"登 录 中...")):(d(),S("span",Z,b(o.$t("login.btnLogin")),1))]),_:1},8,["loading"])]),_:1},8,["style"])]),_:1},8,["model"])),[[T,e(O)==1]])}}}),le=z(ne,[["__scopeId","data-v-7f1e932f"]]);export{le as default};
