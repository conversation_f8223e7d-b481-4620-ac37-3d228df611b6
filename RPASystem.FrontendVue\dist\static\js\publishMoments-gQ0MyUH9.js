import{_ as L,r as m,E as O,a5 as j,x as s,o as r,c as f,i as l,l as t,k as n,F as v,K as V,p as y,j as I,D as S,A as D,s as M,v as h}from"./index-D9f5ARRd.js";import{b as K}from"./article-By-2AKrw.js";import{l as G}from"./articlecategory-Bm4F1q31.js";import{l as H}from"./articletopic-_-hN04re.js";const J={class:"app-container"},Q={class:"upload-wrap"},W=M({name:"articlepublish"}),X=Object.assign(W,{setup(Y){const{proxy:c}=h(),w=m([]);m(""),m(!1);const k=m(),x=O({form:{title:void 0,cid:void 0,content:void 0,status:void 0,categoryId:void 0,isPublic:1,articleType:2,topicId:void 0,tags:"",commentSwitch:0},rules:{title:[{required:!1,message:"标题不能为空",trigger:"blur"}],content:[{required:!0,message:"内容不能为空",trigger:"blur"}]},options:{sys_comment_permi:[]}}),{form:a,rules:C,options:T}=j(x);c.getDicts(["sys_comment_permi"]).then(u=>{u.data.forEach(e=>{x.options[e.dictType]=e.list})});function N(){G({categoryType:1,pageSize:100}).then(u=>{u.code==200&&(w.value=u.data.result)})}const _=m([]);H().then(u=>{_.value=u.data.result});function P(u){var e;a.value.status=u,a.value.tags=(e=_.value.find(p=>p.topicId==a.value.topicId))==null?void 0:e.topicName,c.$refs.formRef.validate(p=>{p&&K(a.value).then(d=>{d.code==200?(c.$modal.msgSuccess("发布成功"),c.$tab.closeOpenPage({path:"/article/index"})):c.$modal.msgError("发布失败")})})}return N(),(u,e)=>{const p=s("el-input"),d=s("el-form-item"),i=s("el-col"),g=s("el-option"),b=s("el-select"),R=s("questionFilled"),U=s("el-icon"),$=s("el-tooltip"),q=s("el-switch"),A=s("plus"),B=s("UploadImage"),E=s("el-button"),F=s("el-row"),z=s("el-form");return r(),f("div",J,[l(z,{model:n(a),ref_key:"formRef",ref:k,rules:n(C),onSubmit:e[8]||(e[8]=D(()=>{},["prevent"]))},{default:t(()=>[l(F,{class:"mb10"},{default:t(()=>[l(i,{lg:24},{default:t(()=>[l(d,{label:"",prop:"title"},{default:t(()=>[l(p,{modelValue:n(a).title,"onUpdate:modelValue":e[0]||(e[0]=o=>n(a).title=o),placeholder:"请输入标题"},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{lg:24},{default:t(()=>[l(d,{prop:"content",label:""},{default:t(()=>[l(p,{type:"textarea",rows:"10",placeholder:"说点什么吧","show-word-limit":"",modelValue:n(a).content,"onUpdate:modelValue":e[1]||(e[1]=o=>n(a).content=o)},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{lg:12},{default:t(()=>[l(d,{prop:"categoryId",label:"选择圈子"},{default:t(()=>[l(b,{modelValue:n(a).categoryId,"onUpdate:modelValue":e[2]||(e[2]=o=>n(a).categoryId=o),clearable:""},{default:t(()=>[(r(!0),f(v,null,V(n(w),o=>(r(),y(g,{key:o.categoryId,label:o.name,value:o.categoryId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(i,{lg:24},{default:t(()=>[l(d,{prop:"commentSwitch",label:"评论权限"},{default:t(()=>[l(b,{clearable:"",modelValue:n(a).commentSwitch,"onUpdate:modelValue":e[3]||(e[3]=o=>n(a).commentSwitch=o)},{default:t(()=>[(r(!0),f(v,null,V(n(T).sys_comment_permi,o=>(r(),y(g,{label:o.dictLabel,value:parseInt(o.dictValue)},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(i,{lg:24},{default:t(()=>[l(d,{prop:"topicId",label:"选择话题"},{default:t(()=>[l(b,{clearable:"",modelValue:n(a).topicId,"onUpdate:modelValue":e[4]||(e[4]=o=>n(a).topicId=o)},{default:t(()=>[(r(!0),f(v,null,V(n(_),o=>(r(),y(g,{label:o.topicName,value:o.topicId},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(i,{lg:8},{default:t(()=>[l(d,null,{label:t(()=>[I("span",null,[l($,{content:"不公开只有自己会看到",placement:"top"},{default:t(()=>[l(U,{size:15},{default:t(()=>[l(R)]),_:1})]),_:1}),e[9]||(e[9]=S(" 是否公开 "))])]),default:t(()=>[l(q,{modelValue:n(a).isPublic,"onUpdate:modelValue":e[5]||(e[5]=o=>n(a).isPublic=o),"inline-prompt":"","active-value":1,"in-active-value":0,"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{lg:24},{default:t(()=>[l(d,null,{default:t(()=>[l(B,{ref:"uploadRef",modelValue:n(a).coverUrl,"onUpdate:modelValue":e[6]||(e[6]=o=>n(a).coverUrl=o),limit:9,fileSize:15,style:{width:"90px"}},{icon:t(()=>[I("div",Q,[l(U,null,{default:t(()=>[l(A)]),_:1}),e[10]||(e[10]=I("div",null,"请选择",-1))])]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(i,{lg:24},{default:t(()=>[l(E,{type:"success",icon:"upload",onClick:e[7]||(e[7]=o=>P("1"))},{default:t(()=>e[11]||(e[11]=[S("立即发布")])),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])])}}}),oe=L(X,[["__scopeId","data-v-b9807a36"]]);export{oe as default};
