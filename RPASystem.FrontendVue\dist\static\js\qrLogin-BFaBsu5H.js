import{_ as v,d as _,r as f,e as p,o as h,c as I,j as s,t as C,ak as L,al as y,v as B}from"./index-D9f5ARRd.js";import{Q as S}from"./qrcode-B0sKqZbl.js";const x={class:"qr-wrap login-form"},E={class:"login-scan-container"},R={ref:"imgContainerRef",id:"imgContainer",class:"qrCode"},U={class:"mt10 text-muted"},b={__name:"qrLogin",setup(k,{expose:u}){const c=_(),{proxy:a}=B(),r=f(null),i=p(()=>c.clientId);function m(){n();var e=g();document.getElementById("imgContainer").innerHTML="正在生成中...",L({uuid:e,deviceId:i.value}).then(t=>{const{code:d,data:o}=t;document.getElementById("imgContainer").innerHTML="",d==200&&new S(document.getElementById("imgContainer"),{text:JSON.stringify(o.codeContent),width:160,height:160})}),r.value=setInterval(()=>{y({uuid:e,deviceId:i.value}).then(t=>{const{code:d,data:o}=t;o.status==-1?(n(),document.getElementById("imgContainer").innerHTML="二维码已过期"):o.status==2&&(c.scanLogin(o).then(()=>{a.$modal.msgSuccess(a.$t("login.loginSuccess")),router.push({path:redirect.value||"/"})}).catch(l=>{console.error(l),a.$modal.msgError(l.msg)}),n())}).catch(()=>{n()})},1e3)}function g(){var e=URL.createObjectURL(new Blob),t=e.toString().replace("-","");return URL.revokeObjectURL(e),t.substr(t.lastIndexOf("/")+1)}function n(){clearInterval(r.value),r.value=null}return u({clearQr:n,generateCode:m}),(e,t)=>(h(),I("div",x,[s("div",E,[s("div",R,null,512),s("div",U,C(e.$t("login.tip_scan_code")),1)])]))}},O=v(b,[["__scopeId","data-v-8e73f041"]]);export{O as default};
