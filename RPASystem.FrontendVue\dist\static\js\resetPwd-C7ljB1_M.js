import{b as g}from"./user-DAowxja8.js";import{E as _,r as V,x as u,o as y,p as $,l as a,i as s,k as l,D as m,t as f,v as C}from"./index-D9f5ARRd.js";const x={__name:"resetPwd",setup(h){const{proxy:p}=C(),e=_({oldPassword:void 0,newPassword:void 0,confirmPassword:void 0}),c=V({oldPassword:[{required:!0,message:"旧密码不能为空",trigger:"blur"}],newPassword:[{required:!0,message:"新密码不能为空",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"确认密码不能为空",trigger:"blur"},{required:!0,validator:(r,o,d)=>{e.newPassword!==o?d(new Error("两次输入的密码不一致")):d()},trigger:"blur"}]});function P(){p.$refs.pwdRef.validate(r=>{r&&g(e.oldPassword,e.newPassword).then(o=>{p.$modal.msgSuccess("修改成功"),w()})})}function w(){p.$tab.closePage()}return(r,o)=>{const d=u("el-input"),n=u("el-form-item"),i=u("el-button"),b=u("el-form");return y(),$(b,{ref:"pwdRef",model:l(e),rules:l(c),"label-width":"100px","label-position":"left",style:{"max-width":"350px"}},{default:a(()=>[s(n,{label:r.$t("user.oldPwd"),prop:"oldPassword"},{default:a(()=>[s(d,{modelValue:l(e).oldPassword,"onUpdate:modelValue":o[0]||(o[0]=t=>l(e).oldPassword=t),placeholder:"请输入旧密码",type:"password","show-password":""},null,8,["modelValue"])]),_:1},8,["label"]),s(n,{label:r.$t("user.newPwd"),prop:"newPassword"},{default:a(()=>[s(d,{modelValue:l(e).newPassword,"onUpdate:modelValue":o[1]||(o[1]=t=>l(e).newPassword=t),placeholder:"请输入新密码",type:"password","show-password":""},null,8,["modelValue"])]),_:1},8,["label"]),s(n,{label:r.$t("user.confirmPwd"),prop:"confirmPassword"},{default:a(()=>[s(d,{modelValue:l(e).confirmPassword,"onUpdate:modelValue":o[2]||(o[2]=t=>l(e).confirmPassword=t),placeholder:"请确认密码",type:"password","show-password":""},null,8,["modelValue"])]),_:1},8,["label"]),s(n,null,{default:a(()=>[s(i,{type:"danger",icon:"Close",onClick:w},{default:a(()=>[m(f(r.$t("btn.close")),1)]),_:1}),s(i,{type:"primary",icon:"Check",onClick:P},{default:a(()=>[m(f(r.$t("btn.save")),1)]),_:1})]),_:1})]),_:1},8,["model","rules"])}}};export{x as default};
