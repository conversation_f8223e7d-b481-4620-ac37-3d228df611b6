import{ai as t,aq as a}from"./index-D9f5ARRd.js";function u(e){return t({url:"/system/role/list",method:"get",params:e})}function l(e){return t({url:"/system/role/"+e,method:"get"})}const n=e=>t({url:"/system/role/edit",method:"post",data:e});function d(e){return t({url:"/system/role/edit",method:"put",data:e})}function m(e){return t({url:"/system/role/dataScope",method:"put",data:e})}function c(e,o){return t({url:"/system/role/changeStatus",method:"put",data:{roleId:e,status:o}})}function i(e){return t({url:"/system/role/"+e,method:"delete"})}async function p(e){await a("/system/role/exportRoleMenu",{...e})}export{n as a,i as b,c,m as d,p as e,l as g,u as l,d as u};
