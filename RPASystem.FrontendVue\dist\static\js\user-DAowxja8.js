import{ai as e,ap as a,aq as u}from"./index-D9f5ARRd.js";function o(t){return e({url:"/system/user/list",method:"get",params:t})}function d(t){return e({url:"/system/user/"+a(t),method:"get"})}function m(t){return e({url:"/system/user/add",method:"post",data:t})}function i(t){return e({url:"/system/user/edit",method:"put",data:t})}function p(t){return e({url:"/system/user/"+t,method:"delete"})}async function l(t){await u("/system/user/export",{...t})}function f(t,r){return e({url:"/system/user/resetPwd",method:"put",data:{userId:t,password:r}})}function c(t,r){return e({url:"/system/user/changeStatus",method:"put",data:{userId:t,status:r}})}function h(){return e({url:"/system/user/profile",method:"get"})}function y(t){return e({url:"/system/user/profile",method:"put",data:t})}function U(t,r){return e({url:"/system/user/profile/updatePwd",method:"put",params:{oldPassword:t,newPassword:r}})}function g(t){return e({url:"/system/user/profile/avatar",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"}})}export{y as a,U as b,c,p as d,l as e,d as f,h as g,i as h,m as i,o as l,f as r,g as u};
