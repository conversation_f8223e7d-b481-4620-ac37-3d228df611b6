import{c as Y,O as E,S as k,j as v,B as H,q as I,V as B,t as T,s as z,an as _,ao as P,o as X,_ as U,d as R,r as W,E as D,x as b,i as w,l as C,p as F,D as A,F as V,v as q}from"./index-D9f5ARRd.js";import{u as N}from"./user-DAowxja8.js";const $={};$.getData=t=>new Promise((e,s)=>{let i={};(function(o){let h=null;return new Promise((r,n)=>{if(o.src)if(/^data\:/i.test(o.src))h=function(p){p=p.replace(/^data\:([^\;]+)\;base64,/gim,"");for(var g=atob(p),d=g.length,l=new ArrayBuffer(d),m=new Uint8Array(l),u=0;u<d;u++)m[u]=g.charCodeAt(u);return l}(o.src),r(h);else if(/^blob\:/i.test(o.src)){var a=new FileReader;a.onload=function(p){h=p.target.result,r(h)},function(p,g){var d=new XMLHttpRequest;d.open("GET",p,!0),d.responseType="blob",d.onload=function(l){this.status!=200&&this.status!==0||g(this.response)},d.send()}(o.src,function(p){a.readAsArrayBuffer(p)})}else{var c=new XMLHttpRequest;c.onload=function(){if(this.status!=200&&this.status!==0)throw"Could not load image";h=c.response,r(h),c=null},c.open("GET",o.src,!0),c.responseType="arraybuffer",c.send(null)}else n("img error")})})(t).then(o=>{i.arrayBuffer=o,i.orientation=function(h){var r,n,a,c,p,g,d,l,m,u=new DataView(h),f=u.byteLength;if(u.getUint8(0)===255&&u.getUint8(1)===216)for(l=2;l<f;){if(u.getUint8(l)===255&&u.getUint8(l+1)===225){g=l;break}l++}if(g&&(n=g+10,function(x,y,M){var O,L="";for(O=y,M+=y;O<M;O++)L+=String.fromCharCode(x.getUint8(O));return L}(u,g+4,4)==="Exif"&&((c=(p=u.getUint16(n))===18761)||p===19789)&&u.getUint16(n+2,c)===42&&(a=u.getUint32(n+4,c))>=8&&(d=n+a)),d){for(f=u.getUint16(d,c),m=0;m<f;m++)if(l=d+12*m+2,u.getUint16(l,c)===274){l+=8,r=u.getUint16(l,c);break}}return r}(o),e(i)}).catch(o=>{s(o)})});const S=z({data:function(){return{w:0,h:0,scale:1,x:0,y:0,loading:!0,trueWidth:0,trueHeight:0,move:!0,moveX:0,moveY:0,crop:!1,cropping:!1,cropW:0,cropH:0,cropOldW:0,cropOldH:0,canChangeX:!1,canChangeY:!1,changeCropTypeX:1,changeCropTypeY:1,cropX:0,cropY:0,cropChangeX:0,cropChangeY:0,cropOffsertX:0,cropOffsertY:0,support:"",touches:[],touchNow:!1,rotate:0,isIos:!1,orientation:0,imgs:"",coe:.2,scaling:!1,scalingSet:"",coeStatus:"",isCanShow:!0}},props:{img:{type:[String,Blob,null,File],default:""},outputSize:{type:Number,default:1},outputType:{type:String,default:"jpeg"},info:{type:Boolean,default:!0},canScale:{type:Boolean,default:!0},autoCrop:{type:Boolean,default:!1},autoCropWidth:{type:[Number,String],default:0},autoCropHeight:{type:[Number,String],default:0},fixed:{type:Boolean,default:!1},fixedNumber:{type:Array,default:()=>[1,1]},fixedBox:{type:Boolean,default:!1},full:{type:Boolean,default:!1},canMove:{type:Boolean,default:!0},canMoveBox:{type:Boolean,default:!0},original:{type:Boolean,default:!1},centerBox:{type:Boolean,default:!1},high:{type:Boolean,default:!0},infoTrue:{type:Boolean,default:!1},maxImgSize:{type:[Number,String],default:2e3},enlarge:{type:[Number,String],default:1},preW:{type:[Number,String],default:0},mode:{type:String,default:"contain"},limitMinSize:{type:[Number,Array,String],default:()=>10}},computed:{cropInfo(){let t={};if(t.top=this.cropOffsertY>21?"-21px":"0px",t.width=this.cropW>0?this.cropW:0,t.height=this.cropH>0?this.cropH:0,this.infoTrue){let e=1;this.high&&!this.full&&(e=window.devicePixelRatio),this.enlarge!==1&!this.full&&(e=Math.abs(Number(this.enlarge))),t.width=t.width*e,t.height=t.height*e,this.full&&(t.width=t.width/this.scale,t.height=t.height/this.scale)}return t.width=t.width.toFixed(0),t.height=t.height.toFixed(0),t},isIE:()=>!!window.ActiveXObject||"ActiveXObject"in window,passive(){return this.isIE?null:{passive:!1}}},watch:{img(){this.checkedImg()},imgs(t){t!==""&&this.reload()},cropW(){this.showPreview()},cropH(){this.showPreview()},cropOffsertX(){this.showPreview()},cropOffsertY(){this.showPreview()},scale(t,e){this.showPreview()},x(){this.showPreview()},y(){this.showPreview()},autoCrop(t){t&&this.goAutoCrop()},autoCropWidth(){this.autoCrop&&this.goAutoCrop()},autoCropHeight(){this.autoCrop&&this.goAutoCrop()},mode(){this.checkedImg()},rotate(){this.showPreview(),(this.autoCrop||this.cropW>0||this.cropH>0)&&this.goAutoCrop(this.cropW,this.cropH)}},methods:{getVersion(t){var e=navigator.userAgent.split(" "),s="";let i=0;const o=new RegExp(t,"i");for(var h=0;h<e.length;h++)o.test(e[h])&&(s=e[h]);return i=s?s.split("/")[1].split("."):["0","0","0"],i},checkOrientationImage(t,e,s,i){if(this.getVersion("chrome")[0]>=81)e=-1;else if(this.getVersion("safari")[0]>=605){const r=this.getVersion("version");r[0]>13&&r[1]>1&&(e=-1)}else{const r=navigator.userAgent.toLowerCase().match(/cpu iphone os (.*?) like mac os/);if(r){let n=r[1];n=n.split("_"),(n[0]>13||n[0]>=13&&n[1]>=4)&&(e=-1)}}let o=document.createElement("canvas"),h=o.getContext("2d");switch(h.save(),e){case 2:o.width=s,o.height=i,h.translate(s,0),h.scale(-1,1);break;case 3:o.width=s,o.height=i,h.translate(s/2,i/2),h.rotate(180*Math.PI/180),h.translate(-s/2,-i/2);break;case 4:o.width=s,o.height=i,h.translate(0,i),h.scale(1,-1);break;case 5:o.height=s,o.width=i,h.rotate(.5*Math.PI),h.scale(1,-1);break;case 6:o.width=i,o.height=s,h.translate(i/2,s/2),h.rotate(90*Math.PI/180),h.translate(-s/2,-i/2);break;case 7:o.height=s,o.width=i,h.rotate(.5*Math.PI),h.translate(s,-i),h.scale(-1,1);break;case 8:o.height=s,o.width=i,h.translate(i/2,s/2),h.rotate(-90*Math.PI/180),h.translate(-s/2,-i/2);break;default:o.width=s,o.height=i}h.drawImage(t,0,0,s,i),h.restore(),o.toBlob(r=>{let n=URL.createObjectURL(r);URL.revokeObjectURL(this.imgs),this.imgs=n},"image/"+this.outputType,1)},checkedImg(){if(this.img===null||this.img==="")return this.imgs="",void this.clearCrop();this.loading=!0,this.scale=1,this.rotate=0,this.clearCrop();let t=new Image;if(t.onload=()=>{if(this.img==="")return this.$emit("imgLoad","error"),this.$emit("img-load","error"),!1;let s=t.width,i=t.height;$.getData(t).then(o=>{this.orientation=o.orientation||1;let h=Number(this.maxImgSize);!this.orientation&&s<h&i<h?this.imgs=this.img:(s>h&&(i=i/s*h,s=h),i>h&&(s=s/i*h,i=h),this.checkOrientationImage(t,this.orientation,s,i))})},t.onerror=()=>{this.$emit("imgLoad","error"),this.$emit("img-load","error")},this.img.substr(0,4)!=="data"&&(t.crossOrigin=""),this.isIE){var e=new XMLHttpRequest;e.onload=function(){var s=URL.createObjectURL(this.response);t.src=s},e.open("GET",this.img,!0),e.responseType="blob",e.send()}else t.src=this.img},startMove(t){if(t.preventDefault(),this.move&&!this.crop){if(!this.canMove)return!1;this.moveX=("clientX"in t?t.clientX:t.touches[0].clientX)-this.x,this.moveY=("clientY"in t?t.clientY:t.touches[0].clientY)-this.y,t.touches?(window.addEventListener("touchmove",this.moveImg),window.addEventListener("touchend",this.leaveImg),t.touches.length==2&&(this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale))):(window.addEventListener("mousemove",this.moveImg),window.addEventListener("mouseup",this.leaveImg)),this.$emit("imgMoving",{moving:!0,axis:this.getImgAxis()}),this.$emit("img-moving",{moving:!0,axis:this.getImgAxis()})}else this.cropping=!0,window.addEventListener("mousemove",this.createCrop),window.addEventListener("mouseup",this.endCrop),window.addEventListener("touchmove",this.createCrop),window.addEventListener("touchend",this.endCrop),this.cropOffsertX=t.offsetX?t.offsetX:t.touches[0].pageX-this.$refs.cropper.offsetLeft,this.cropOffsertY=t.offsetY?t.offsetY:t.touches[0].pageY-this.$refs.cropper.offsetTop,this.cropX="clientX"in t?t.clientX:t.touches[0].clientX,this.cropY="clientY"in t?t.clientY:t.touches[0].clientY,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.cropW=0,this.cropH=0},touchScale(t){t.preventDefault();let e=this.scale;var s=this.touches[0].clientX,i=this.touches[0].clientY,o=t.touches[0].clientX,h=t.touches[0].clientY,r=this.touches[1].clientX,n=this.touches[1].clientY,a=t.touches[1].clientX,c=t.touches[1].clientY,p=Math.sqrt(Math.pow(s-r,2)+Math.pow(i-n,2)),g=Math.sqrt(Math.pow(o-a,2)+Math.pow(h-c,2))-p,d=1,l=(d=(d=d/this.trueWidth>d/this.trueHeight?d/this.trueHeight:d/this.trueWidth)>.1?.1:d)*g;if(!this.touchNow){if(this.touchNow=!0,g>0?e+=Math.abs(l):g<0&&e>Math.abs(l)&&(e-=Math.abs(l)),this.touches=t.touches,setTimeout(()=>{this.touchNow=!1},8),!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e}},cancelTouchScale(t){window.removeEventListener("touchmove",this.touchScale)},moveImg(t){if(t.preventDefault(),t.touches&&t.touches.length===2)return this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale),window.removeEventListener("touchmove",this.moveImg),!1;let e,s,i="clientX"in t?t.clientX:t.touches[0].clientX,o="clientY"in t?t.clientY:t.touches[0].clientY;e=i-this.moveX,s=o-this.moveY,this.$nextTick(()=>{if(this.centerBox){let h,r,n,a,c=this.getImgAxis(e,s,this.scale),p=this.getCropAxis(),g=this.trueHeight*this.scale,d=this.trueWidth*this.scale;switch(this.rotate){case 1:case-1:case 3:case-3:h=this.cropOffsertX-this.trueWidth*(1-this.scale)/2+(g-d)/2,r=this.cropOffsertY-this.trueHeight*(1-this.scale)/2+(d-g)/2,n=h-g+this.cropW,a=r-d+this.cropH;break;default:h=this.cropOffsertX-this.trueWidth*(1-this.scale)/2,r=this.cropOffsertY-this.trueHeight*(1-this.scale)/2,n=h-d+this.cropW,a=r-g+this.cropH}c.x1>=p.x1&&(e=h),c.y1>=p.y1&&(s=r),c.x2<=p.x2&&(e=n),c.y2<=p.y2&&(s=a)}this.x=e,this.y=s,this.$emit("imgMoving",{moving:!0,axis:this.getImgAxis()}),this.$emit("img-moving",{moving:!0,axis:this.getImgAxis()})})},leaveImg(t){window.removeEventListener("mousemove",this.moveImg),window.removeEventListener("touchmove",this.moveImg),window.removeEventListener("mouseup",this.leaveImg),window.removeEventListener("touchend",this.leaveImg),this.$emit("imgMoving",{moving:!1,axis:this.getImgAxis()}),this.$emit("img-moving",{moving:!1,axis:this.getImgAxis()})},scaleImg(){this.canScale&&window.addEventListener(this.support,this.changeSize,this.passive)},cancelScale(){this.canScale&&window.removeEventListener(this.support,this.changeSize)},changeSize(t){t.preventDefault();let e=this.scale;var s=t.deltaY||t.wheelDelta;s=navigator.userAgent.indexOf("Firefox")>0?30*s:s,this.isIE&&(s=-s);var i=this.coe,o=(i=i/this.trueWidth>i/this.trueHeight?i/this.trueHeight:i/this.trueWidth)*s;o<0?e+=Math.abs(o):e>Math.abs(o)&&(e-=Math.abs(o));let h=o<0?"add":"reduce";if(h!==this.coeStatus&&(this.coeStatus=h,this.coe=.2),this.scaling||(this.scalingSet=setTimeout(()=>{this.scaling=!1,this.coe=this.coe+=.01},50)),this.scaling=!0,!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e},changeScale(t){let e=this.scale;t=t||1;var s=20;if((t*=s=s/this.trueWidth>s/this.trueHeight?s/this.trueHeight:s/this.trueWidth)>0?e+=Math.abs(t):e>Math.abs(t)&&(e-=Math.abs(t)),!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e},createCrop(t){t.preventDefault();var e="clientX"in t?t.clientX:t.touches?t.touches[0].clientX:0,s="clientY"in t?t.clientY:t.touches?t.touches[0].clientY:0;this.$nextTick(()=>{var i=e-this.cropX,o=s-this.cropY;if(i>0?(this.cropW=i+this.cropChangeX>this.w?this.w-this.cropChangeX:i,this.cropOffsertX=this.cropChangeX):(this.cropW=this.w-this.cropChangeX+Math.abs(i)>this.w?this.cropChangeX:Math.abs(i),this.cropOffsertX=this.cropChangeX+i>0?this.cropChangeX+i:0),this.fixed){var h=this.cropW/this.fixedNumber[0]*this.fixedNumber[1];h+this.cropOffsertY>this.h?(this.cropH=this.h-this.cropOffsertY,this.cropW=this.cropH/this.fixedNumber[1]*this.fixedNumber[0],this.cropOffsertX=i>0?this.cropChangeX:this.cropChangeX-this.cropW):this.cropH=h,this.cropOffsertY=this.cropOffsertY}else o>0?(this.cropH=o+this.cropChangeY>this.h?this.h-this.cropChangeY:o,this.cropOffsertY=this.cropChangeY):(this.cropH=this.h-this.cropChangeY+Math.abs(o)>this.h?this.cropChangeY:Math.abs(o),this.cropOffsertY=this.cropChangeY+o>0?this.cropChangeY+o:0)})},changeCropSize(t,e,s,i,o){t.preventDefault(),window.addEventListener("mousemove",this.changeCropNow),window.addEventListener("mouseup",this.changeCropEnd),window.addEventListener("touchmove",this.changeCropNow),window.addEventListener("touchend",this.changeCropEnd),this.canChangeX=e,this.canChangeY=s,this.changeCropTypeX=i,this.changeCropTypeY=o,this.cropX="clientX"in t?t.clientX:t.touches[0].clientX,this.cropY="clientY"in t?t.clientY:t.touches[0].clientY,this.cropOldW=this.cropW,this.cropOldH=this.cropH,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.fixed&&this.canChangeX&&this.canChangeY&&(this.canChangeY=0),this.$emit("change-crop-size",{width:this.cropW,height:this.cropH})},changeCropNow(t){t.preventDefault();var e="clientX"in t?t.clientX:t.touches?t.touches[0].clientX:0,s="clientY"in t?t.clientY:t.touches?t.touches[0].clientY:0;let i=this.w,o=this.h,h=0,r=0;if(this.centerBox){let n=this.getImgAxis(),a=n.x2,c=n.y2;h=n.x1>0?n.x1:0,r=n.y1>0?n.y1:0,i>a&&(i=a),o>c&&(o=c)}this.$nextTick(()=>{var n=e-this.cropX,a=s-this.cropY;if(this.canChangeX&&(this.changeCropTypeX===1?this.cropOldW-n>0?(this.cropW=i-this.cropChangeX-n<=i-h?this.cropOldW-n:this.cropOldW+this.cropChangeX-h,this.cropOffsertX=i-this.cropChangeX-n<=i-h?this.cropChangeX+n:h):(this.cropW=Math.abs(n)+this.cropChangeX<=i?Math.abs(n)-this.cropOldW:i-this.cropOldW-this.cropChangeX,this.cropOffsertX=this.cropChangeX+this.cropOldW):this.changeCropTypeX===2&&(this.cropOldW+n>0?(this.cropW=this.cropOldW+n+this.cropOffsertX<=i?this.cropOldW+n:i-this.cropOffsertX,this.cropOffsertX=this.cropChangeX):(this.cropW=i-this.cropChangeX+Math.abs(n+this.cropOldW)<=i-h?Math.abs(n+this.cropOldW):this.cropChangeX-h,this.cropOffsertX=i-this.cropChangeX+Math.abs(n+this.cropOldW)<=i-h?this.cropChangeX-Math.abs(n+this.cropOldW):h))),this.canChangeY&&(this.changeCropTypeY===1?this.cropOldH-a>0?(this.cropH=o-this.cropChangeY-a<=o-r?this.cropOldH-a:this.cropOldH+this.cropChangeY-r,this.cropOffsertY=o-this.cropChangeY-a<=o-r?this.cropChangeY+a:r):(this.cropH=Math.abs(a)+this.cropChangeY<=o?Math.abs(a)-this.cropOldH:o-this.cropOldH-this.cropChangeY,this.cropOffsertY=this.cropChangeY+this.cropOldH):this.changeCropTypeY===2&&(this.cropOldH+a>0?(this.cropH=this.cropOldH+a+this.cropOffsertY<=o?this.cropOldH+a:o-this.cropOffsertY,this.cropOffsertY=this.cropChangeY):(this.cropH=o-this.cropChangeY+Math.abs(a+this.cropOldH)<=o-r?Math.abs(a+this.cropOldH):this.cropChangeY-r,this.cropOffsertY=o-this.cropChangeY+Math.abs(a+this.cropOldH)<=o-r?this.cropChangeY-Math.abs(a+this.cropOldH):r))),this.canChangeX&&this.fixed){var c=this.cropW/this.fixedNumber[0]*this.fixedNumber[1];c+this.cropOffsertY>o?(this.cropH=o-this.cropOffsertY,this.cropW=this.cropH/this.fixedNumber[1]*this.fixedNumber[0]):this.cropH=c}if(this.canChangeY&&this.fixed){var p=this.cropH/this.fixedNumber[1]*this.fixedNumber[0];p+this.cropOffsertX>i?(this.cropW=i-this.cropOffsertX,this.cropH=this.cropW/this.fixedNumber[0]*this.fixedNumber[1]):this.cropW=p}})},checkCropLimitSize(){let{cropW:t,cropH:e,limitMinSize:s}=this,i=new Array;return i=Array.isArray[s]?s:[s,s],t=parseFloat(i[0]),e=parseFloat(i[1]),[t,e]},changeCropEnd(t){window.removeEventListener("mousemove",this.changeCropNow),window.removeEventListener("mouseup",this.changeCropEnd),window.removeEventListener("touchmove",this.changeCropNow),window.removeEventListener("touchend",this.changeCropEnd)},endCrop(){this.cropW===0&&this.cropH===0&&(this.cropping=!1),window.removeEventListener("mousemove",this.createCrop),window.removeEventListener("mouseup",this.endCrop),window.removeEventListener("touchmove",this.createCrop),window.removeEventListener("touchend",this.endCrop)},startCrop(){this.crop=!0},stopCrop(){this.crop=!1},clearCrop(){this.cropping=!1,this.cropW=0,this.cropH=0},cropMove(t){if(t.preventDefault(),!this.canMoveBox)return this.crop=!1,this.startMove(t),!1;if(t.touches&&t.touches.length===2)return this.crop=!1,this.startMove(t),this.leaveCrop(),!1;window.addEventListener("mousemove",this.moveCrop),window.addEventListener("mouseup",this.leaveCrop),window.addEventListener("touchmove",this.moveCrop),window.addEventListener("touchend",this.leaveCrop);let e,s,i="clientX"in t?t.clientX:t.touches[0].clientX,o="clientY"in t?t.clientY:t.touches[0].clientY;e=i-this.cropOffsertX,s=o-this.cropOffsertY,this.cropX=e,this.cropY=s,this.$emit("cropMoving",{moving:!0,axis:this.getCropAxis()}),this.$emit("crop-moving",{moving:!0,axis:this.getCropAxis()})},moveCrop(t,e){let s=0,i=0;t&&(t.preventDefault(),s="clientX"in t?t.clientX:t.touches[0].clientX,i="clientY"in t?t.clientY:t.touches[0].clientY),this.$nextTick(()=>{let o,h,r=s-this.cropX,n=i-this.cropY;if(e&&(r=this.cropOffsertX,n=this.cropOffsertY),o=r<=0?0:r+this.cropW>this.w?this.w-this.cropW:r,h=n<=0?0:n+this.cropH>this.h?this.h-this.cropH:n,this.centerBox){let a=this.getImgAxis();o<=a.x1&&(o=a.x1),o+this.cropW>a.x2&&(o=a.x2-this.cropW),h<=a.y1&&(h=a.y1),h+this.cropH>a.y2&&(h=a.y2-this.cropH)}this.cropOffsertX=o,this.cropOffsertY=h,this.$emit("cropMoving",{moving:!0,axis:this.getCropAxis()}),this.$emit("crop-moving",{moving:!0,axis:this.getCropAxis()})})},getImgAxis(t,e,s){t=t||this.x,e=e||this.y,s=s||this.scale;let i={x1:0,x2:0,y1:0,y2:0},o=this.trueWidth*s,h=this.trueHeight*s;switch(this.rotate){case 0:i.x1=t+this.trueWidth*(1-s)/2,i.x2=i.x1+this.trueWidth*s,i.y1=e+this.trueHeight*(1-s)/2,i.y2=i.y1+this.trueHeight*s;break;case 1:case-1:case 3:case-3:i.x1=t+this.trueWidth*(1-s)/2+(o-h)/2,i.x2=i.x1+this.trueHeight*s,i.y1=e+this.trueHeight*(1-s)/2+(h-o)/2,i.y2=i.y1+this.trueWidth*s;break;default:i.x1=t+this.trueWidth*(1-s)/2,i.x2=i.x1+this.trueWidth*s,i.y1=e+this.trueHeight*(1-s)/2,i.y2=i.y1+this.trueHeight*s}return i},getCropAxis(){let t={x1:0,x2:0,y1:0,y2:0};return t.x1=this.cropOffsertX,t.x2=t.x1+this.cropW,t.y1=this.cropOffsertY,t.y2=t.y1+this.cropH,t},leaveCrop(t){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.$emit("cropMoving",{moving:!1,axis:this.getCropAxis()}),this.$emit("crop-moving",{moving:!1,axis:this.getCropAxis()})},getCropChecked(t){let e=document.createElement("canvas"),s=new Image,i=this.rotate,o=this.trueWidth,h=this.trueHeight,r=this.cropOffsertX,n=this.cropOffsertY;function a(c,p){e.width=Math.round(c),e.height=Math.round(p)}s.onload=()=>{if(this.cropW!==0){let c=e.getContext("2d"),p=1;this.high&!this.full&&(p=window.devicePixelRatio),this.enlarge!==1&!this.full&&(p=Math.abs(Number(this.enlarge)));let g=this.cropW*p,d=this.cropH*p,l=o*this.scale*p,m=h*this.scale*p,u=(this.x-r+this.trueWidth*(1-this.scale)/2)*p,f=(this.y-n+this.trueHeight*(1-this.scale)/2)*p;switch(a(g,d),c.save(),i){case 0:this.full?(a(g/this.scale,d/this.scale),c.drawImage(s,u/this.scale,f/this.scale,l/this.scale,m/this.scale)):c.drawImage(s,u,f,l,m);break;case 1:case-3:this.full?(a(g/this.scale,d/this.scale),u=u/this.scale+(l/this.scale-m/this.scale)/2,f=f/this.scale+(m/this.scale-l/this.scale)/2,c.rotate(90*i*Math.PI/180),c.drawImage(s,f,-u-m/this.scale,l/this.scale,m/this.scale)):(u+=(l-m)/2,f+=(m-l)/2,c.rotate(90*i*Math.PI/180),c.drawImage(s,f,-u-m,l,m));break;case 2:case-2:this.full?(a(g/this.scale,d/this.scale),c.rotate(90*i*Math.PI/180),u/=this.scale,f/=this.scale,c.drawImage(s,-u-l/this.scale,-f-m/this.scale,l/this.scale,m/this.scale)):(c.rotate(90*i*Math.PI/180),c.drawImage(s,-u-l,-f-m,l,m));break;case 3:case-1:this.full?(a(g/this.scale,d/this.scale),u=u/this.scale+(l/this.scale-m/this.scale)/2,f=f/this.scale+(m/this.scale-l/this.scale)/2,c.rotate(90*i*Math.PI/180),c.drawImage(s,-f-l/this.scale,u,l/this.scale,m/this.scale)):(u+=(l-m)/2,f+=(m-l)/2,c.rotate(90*i*Math.PI/180),c.drawImage(s,-f-l,u,l,m));break;default:this.full?(a(g/this.scale,d/this.scale),c.drawImage(s,u/this.scale,f/this.scale,l/this.scale,m/this.scale)):c.drawImage(s,u,f,l,m)}c.restore()}else{let c=o*this.scale,p=h*this.scale,g=e.getContext("2d");switch(g.save(),i){case 0:a(c,p),g.drawImage(s,0,0,c,p);break;case 1:case-3:a(p,c),g.rotate(90*i*Math.PI/180),g.drawImage(s,0,-p,c,p);break;case 2:case-2:a(c,p),g.rotate(90*i*Math.PI/180),g.drawImage(s,-c,-p,c,p);break;case 3:case-1:a(p,c),g.rotate(90*i*Math.PI/180),g.drawImage(s,-c,0,c,p);break;default:a(c,p),g.drawImage(s,0,0,c,p)}g.restore()}t(e)},this.img.substr(0,4)!=="data"&&(s.crossOrigin="Anonymous"),s.src=this.imgs},getCropData(t){this.getCropChecked(e=>{t(e.toDataURL("image/"+this.outputType,this.outputSize))})},getCropBlob(t){this.getCropChecked(e=>{e.toBlob(s=>t(s),"image/"+this.outputType,this.outputSize)})},showPreview(){if(!this.isCanShow)return!1;this.isCanShow=!1,setTimeout(()=>{this.isCanShow=!0},16);let t=this.cropW,e=this.cropH,s=this.scale;var i={};i.div={width:`${t}px`,height:`${e}px`};let o=(this.x-this.cropOffsertX)/s,h=(this.y-this.cropOffsertY)/s;i.w=t,i.h=e,i.url=this.imgs,i.img={width:`${this.trueWidth}px`,height:`${this.trueHeight}px`,transform:`scale(${s})translate3d(${o}px, ${h}px, 0px)rotateZ(${90*this.rotate}deg)`},i.html=`
      <div class="show-preview" style="width: ${i.w}px; height: ${i.h}px,; overflow: hidden">
        <div style="width: ${t}px; height: ${e}px">
          <img src=${i.url} style="width: ${this.trueWidth}px; height: ${this.trueHeight}px; transform:
          scale(${s})translate3d(${o}px, ${h}px, 0px)rotateZ(${90*this.rotate}deg)">
        </div>
      </div>`,this.$emit("realTime",i),this.$emit("real-time",i)},reload(){let t=new Image;t.onload=()=>{this.w=parseFloat(window.getComputedStyle(this.$refs.cropper).width),this.h=parseFloat(window.getComputedStyle(this.$refs.cropper).height),this.trueWidth=t.width,this.trueHeight=t.height,this.original?this.scale=1:this.scale=this.checkedMode(),this.$nextTick(()=>{this.x=-(this.trueWidth-this.trueWidth*this.scale)/2+(this.w-this.trueWidth*this.scale)/2,this.y=-(this.trueHeight-this.trueHeight*this.scale)/2+(this.h-this.trueHeight*this.scale)/2,this.loading=!1,this.autoCrop&&this.goAutoCrop(),this.$emit("img-load","success"),this.$emit("imgLoad","success"),setTimeout(()=>{this.showPreview()},20)})},t.onerror=()=>{this.$emit("imgLoad","error"),this.$emit("img-load","error")},t.src=this.imgs},checkedMode(){let t=1,e=this.trueWidth,s=this.trueHeight;const i=this.mode.split(" ");switch(i[0]){case"contain":this.trueWidth>this.w&&(t=this.w/this.trueWidth),this.trueHeight*t>this.h&&(t=this.h/this.trueHeight);break;case"cover":e=this.w,t=e/this.trueWidth,s*=t,s<this.h&&(s=this.h,t=s/this.trueHeight);break;default:try{let o=i[0];if(o.search("px")!==-1){o=o.replace("px",""),e=parseFloat(o);const h=e/this.trueWidth;let r=1,n=i[1];n.search("px")!==-1&&(n=n.replace("px",""),s=parseFloat(n),r=s/this.trueHeight),t=Math.min(h,r)}if(o.search("%")!==-1&&(o=o.replace("%",""),e=parseFloat(o)/100*this.w,t=e/this.trueWidth),i.length===2&&o==="auto"){let h=i[1];h.search("px")!==-1&&(h=h.replace("px",""),s=parseFloat(h),t=s/this.trueHeight),h.search("%")!==-1&&(h=h.replace("%",""),s=parseFloat(h)/100*this.h,t=s/this.trueHeight)}}catch{t=1}}return t},goAutoCrop(t,e){if(this.imgs===""||this.imgs===null)return;this.clearCrop(),this.cropping=!0;let s=this.w,i=this.h;if(this.centerBox){const r=Math.abs(this.rotate)%2>0;let n=(r?this.trueHeight:this.trueWidth)*this.scale,a=(r?this.trueWidth:this.trueHeight)*this.scale;s=n<s?n:s,i=a<i?a:i}var o=t||parseFloat(this.autoCropWidth),h=e||parseFloat(this.autoCropHeight);o!==0&&h!==0||(o=.8*s,h=.8*i),o=o>s?s:o,h=h>i?i:h,this.fixed&&(h=o/this.fixedNumber[0]*this.fixedNumber[1]),h>this.h&&(o=(h=this.h)/this.fixedNumber[1]*this.fixedNumber[0]),this.changeCrop(o,h)},changeCrop(t,e){if(this.centerBox){let s=this.getImgAxis();t>s.x2-s.x1&&(e=(t=s.x2-s.x1)/this.fixedNumber[0]*this.fixedNumber[1]),e>s.y2-s.y1&&(t=(e=s.y2-s.y1)/this.fixedNumber[1]*this.fixedNumber[0])}this.cropW=t,this.cropH=e,this.checkCropLimitSize(),this.$nextTick(()=>{this.cropOffsertX=(this.w-this.cropW)/2,this.cropOffsertY=(this.h-this.cropH)/2,this.centerBox&&this.moveCrop(null,!0)})},refresh(){this.img,this.imgs="",this.scale=1,this.crop=!1,this.rotate=0,this.w=0,this.h=0,this.trueWidth=0,this.trueHeight=0,this.clearCrop(),this.$nextTick(()=>{this.checkedImg()})},rotateLeft(){this.rotate=this.rotate<=-3?0:this.rotate-1},rotateRight(){this.rotate=this.rotate>=3?0:this.rotate+1},rotateClear(){this.rotate=0},checkoutImgAxis(t,e,s){t=t||this.x,e=e||this.y,s=s||this.scale;let i=!0;if(this.centerBox){let o=this.getImgAxis(t,e,s),h=this.getCropAxis();o.x1>=h.x1&&(i=!1),o.x2<=h.x2&&(i=!1),o.y1>=h.y1&&(i=!1),o.y2<=h.y2&&(i=!1)}return i}},mounted(){this.support="onwheel"in document.createElement("div")?"wheel":document.onmousewheel!==void 0?"mousewheel":"DOMMouseScroll";let t=this;var e=navigator.userAgent;this.isIOS=!!e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),HTMLCanvasElement.prototype.toBlob||Object.defineProperty(HTMLCanvasElement.prototype,"toBlob",{value:function(s,i,o){for(var h=atob(this.toDataURL(i,o).split(",")[1]),r=h.length,n=new Uint8Array(r),a=0;a<r;a++)n[a]=h.charCodeAt(a);s(new Blob([n],{type:t.type||"image/png"}))}}),this.showPreview(),this.checkedImg()},destroyed(){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.cancelScale()}});_("data-v-48aab112");const j={key:0,class:"cropper-box"},G=["src"],Z={class:"cropper-view-box"},J=["src"],K={key:1};P(),S.render=function(t,e,s,i,o,h){return X(),Y("div",{class:"vue-cropper",ref:"cropper",onMouseover:e[28]||(e[28]=(...r)=>t.scaleImg&&t.scaleImg(...r)),onMouseout:e[29]||(e[29]=(...r)=>t.cancelScale&&t.cancelScale(...r))},[t.imgs?(X(),Y("div",j,[E(v("div",{class:"cropper-box-canvas",style:H({width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+t.x/t.scale+"px,"+t.y/t.scale+"px,0)rotateZ("+90*t.rotate+"deg)"})},[v("img",{src:t.imgs,alt:"cropper-img",ref:"cropperImg"},null,8,G)],4),[[k,!t.loading]])])):I("",!0),v("div",{class:B(["cropper-drag-box",{"cropper-move":t.move&&!t.crop,"cropper-crop":t.crop,"cropper-modal":t.cropping}]),onMousedown:e[0]||(e[0]=(...r)=>t.startMove&&t.startMove(...r)),onTouchstart:e[1]||(e[1]=(...r)=>t.startMove&&t.startMove(...r))},null,34),E(v("div",{class:"cropper-crop-box",style:H({width:t.cropW+"px",height:t.cropH+"px",transform:"translate3d("+t.cropOffsertX+"px,"+t.cropOffsertY+"px,0)"})},[v("span",Z,[v("img",{style:H({width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+(t.x-t.cropOffsertX)/t.scale+"px,"+(t.y-t.cropOffsertY)/t.scale+"px,0)rotateZ("+90*t.rotate+"deg)"}),src:t.imgs,alt:"cropper-img"},null,12,J)]),v("span",{class:"cropper-face cropper-move",onMousedown:e[2]||(e[2]=(...r)=>t.cropMove&&t.cropMove(...r)),onTouchstart:e[3]||(e[3]=(...r)=>t.cropMove&&t.cropMove(...r))},null,32),t.info?(X(),Y("span",{key:0,class:"crop-info",style:H({top:t.cropInfo.top})},T(t.cropInfo.width)+" × "+T(t.cropInfo.height),5)):I("",!0),t.fixedBox?I("",!0):(X(),Y("span",K,[v("span",{class:"crop-line line-w",onMousedown:e[4]||(e[4]=r=>t.changeCropSize(r,!1,!0,0,1)),onTouchstart:e[5]||(e[5]=r=>t.changeCropSize(r,!1,!0,0,1))},null,32),v("span",{class:"crop-line line-a",onMousedown:e[6]||(e[6]=r=>t.changeCropSize(r,!0,!1,1,0)),onTouchstart:e[7]||(e[7]=r=>t.changeCropSize(r,!0,!1,1,0))},null,32),v("span",{class:"crop-line line-s",onMousedown:e[8]||(e[8]=r=>t.changeCropSize(r,!1,!0,0,2)),onTouchstart:e[9]||(e[9]=r=>t.changeCropSize(r,!1,!0,0,2))},null,32),v("span",{class:"crop-line line-d",onMousedown:e[10]||(e[10]=r=>t.changeCropSize(r,!0,!1,2,0)),onTouchstart:e[11]||(e[11]=r=>t.changeCropSize(r,!0,!1,2,0))},null,32),v("span",{class:"crop-point point1",onMousedown:e[12]||(e[12]=r=>t.changeCropSize(r,!0,!0,1,1)),onTouchstart:e[13]||(e[13]=r=>t.changeCropSize(r,!0,!0,1,1))},null,32),v("span",{class:"crop-point point2",onMousedown:e[14]||(e[14]=r=>t.changeCropSize(r,!1,!0,0,1)),onTouchstart:e[15]||(e[15]=r=>t.changeCropSize(r,!1,!0,0,1))},null,32),v("span",{class:"crop-point point3",onMousedown:e[16]||(e[16]=r=>t.changeCropSize(r,!0,!0,2,1)),onTouchstart:e[17]||(e[17]=r=>t.changeCropSize(r,!0,!0,2,1))},null,32),v("span",{class:"crop-point point4",onMousedown:e[18]||(e[18]=r=>t.changeCropSize(r,!0,!1,1,0)),onTouchstart:e[19]||(e[19]=r=>t.changeCropSize(r,!0,!1,1,0))},null,32),v("span",{class:"crop-point point5",onMousedown:e[20]||(e[20]=r=>t.changeCropSize(r,!0,!1,2,0)),onTouchstart:e[21]||(e[21]=r=>t.changeCropSize(r,!0,!1,2,0))},null,32),v("span",{class:"crop-point point6",onMousedown:e[22]||(e[22]=r=>t.changeCropSize(r,!0,!0,1,2)),onTouchstart:e[23]||(e[23]=r=>t.changeCropSize(r,!0,!0,1,2))},null,32),v("span",{class:"crop-point point7",onMousedown:e[24]||(e[24]=r=>t.changeCropSize(r,!1,!0,0,2)),onTouchstart:e[25]||(e[25]=r=>t.changeCropSize(r,!1,!0,0,2))},null,32),v("span",{class:"crop-point point8",onMousedown:e[26]||(e[26]=r=>t.changeCropSize(r,!0,!0,2,2)),onTouchstart:e[27]||(e[27]=r=>t.changeCropSize(r,!0,!0,2,2))},null,32)]))],4),[[k,t.cropping]])],544)},S.__scopeId="data-v-48aab112";typeof window<"u"&&window.Vue&&window.Vue.createApp({}).component("VueCropper",S);const Q={components:{VueCropper:S,uploadAvatar:N},setup(){const t=R(),{proxy:e}=q(),s=W(!1),i=W(!1),o=W("修改头像"),h=W(""),r=D({img:t.avatar,autoCrop:!0,autoCropWidth:200,autoCropHeight:200,fixedBox:!0,previews:{}});function n(){s.value=!0}function a(){i.value=!0}function c(){}function p(){e.$refs.cropper.rotateLeft()}function g(){e.$refs.cropper.rotateRight()}function d(x){x=x||1,e.$refs.cropper.changeScale(x)}function l(x){if(x.type.indexOf("image/")==-1)e.$modal.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");else{h.value=x.name;const y=new FileReader;y.readAsDataURL(x),y.onload=()=>{r.img=y.result}}}function m(){e.$refs.cropper.getCropBlob(x=>{let y=new FormData;var M=new File([x],h.value);y.append("picture",M),N(y).then(O=>{s.value=!1,r.img=O.data.imgUrl,t.avatar=r.img,e.$modal.msgSuccess("修改成功"),i.value=!1})})}function u(x){r.previews=x}function f(){r.img=t.avatar,r.visible=!1}return{open:s,visible:i,title:o,options:r,requestUpload:c,modalOpened:a,rotateRight:g,rotateLeft:p,editCropper:n,changeScale:d,beforeUpload:l,uploadImg:m,realTime:u,closeDialog:f}}},tt={class:"avatar-upload-preview"},et=["src"];function it(t,e,s,i,o,h){const r=b("el-avatar"),n=b("vue-cropper"),a=b("el-col"),c=b("el-row"),p=b("Upload"),g=b("el-icon"),d=b("el-button"),l=b("el-upload"),m=b("el-dialog");return X(),Y(V,null,[v("div",{class:"user-info-head",onClick:e[0]||(e[0]=u=>i.editCropper())},[w(r,{shape:"circle",size:120,src:i.options.img},null,8,["src"])]),w(m,{title:i.title,modelValue:i.open,"onUpdate:modelValue":e[6]||(e[6]=u=>i.open=u),width:"800px","append-to-body":"",onOpened:i.modalOpened,onClose:i.closeDialog},{default:C(()=>[w(c,null,{default:C(()=>[w(a,{xs:24,md:12,style:{height:"350px"}},{default:C(()=>[i.visible?(X(),F(n,{key:0,ref:"cropper",img:i.options.img,info:!0,autoCrop:i.options.autoCrop,autoCropWidth:i.options.autoCropWidth,autoCropHeight:i.options.autoCropHeight,fixedBox:i.options.fixedBox,onRealTime:i.realTime},null,8,["img","autoCrop","autoCropWidth","autoCropHeight","fixedBox","onRealTime"])):I("",!0)]),_:1}),w(a,{xs:24,md:12,style:{height:"350px"}},{default:C(()=>[v("div",tt,[v("img",{src:i.options.previews.url,style:H(i.options.previews.img)},null,12,et)])]),_:1})]),_:1}),e[9]||(e[9]=v("br",null,null,-1)),w(c,null,{default:C(()=>[w(a,{lg:2,md:2},{default:C(()=>[w(l,{action:"#","http-request":i.requestUpload,"show-file-list":!1,"before-upload":i.beforeUpload},{default:C(()=>[w(d,null,{default:C(()=>[e[7]||(e[7]=A(" 选择 ")),w(g,{class:"el-icon--right"},{default:C(()=>[w(p)]),_:1})]),_:1})]),_:1},8,["http-request","before-upload"])]),_:1}),w(a,{lg:{span:1,offset:2},md:2},{default:C(()=>[w(d,{icon:"Plus",onClick:e[1]||(e[1]=u=>i.changeScale(1))})]),_:1}),w(a,{lg:{span:1,offset:1},md:2},{default:C(()=>[w(d,{icon:"Minus",onClick:e[2]||(e[2]=u=>i.changeScale(-1))})]),_:1}),w(a,{lg:{span:1,offset:1},md:2},{default:C(()=>[w(d,{icon:"RefreshLeft",onClick:e[3]||(e[3]=u=>i.rotateLeft())})]),_:1}),w(a,{lg:{span:1,offset:1},md:2},{default:C(()=>[w(d,{icon:"RefreshRight",onClick:e[4]||(e[4]=u=>i.rotateRight())})]),_:1}),w(a,{lg:{span:2,offset:6},md:2},{default:C(()=>[w(d,{type:"primary",onClick:e[5]||(e[5]=u=>i.uploadImg())},{default:C(()=>e[8]||(e[8]=[A("提 交")])),_:1})]),_:1})]),_:1})]),_:1},8,["title","modelValue","onOpened","onClose"])],64)}const ht=U(Q,[["render",it],["__scopeId","data-v-5e930612"]]);export{ht as default};
